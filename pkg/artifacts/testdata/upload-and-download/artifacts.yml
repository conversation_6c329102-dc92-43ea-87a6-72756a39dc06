
name: "Test that artifact uploads and downloads succeed"
on: push

jobs:
  test-artifacts:
    runs-on: ubuntu-latest
    steps:
      - run: mkdir -p path/to/artifact
      - run: echo hello > path/to/artifact/world.txt
      - uses: actions/upload-artifact@v2
        with:
          name: my-artifact
          path: path/to/artifact/world.txt

      - run: rm -rf path

      - uses: actions/download-artifact@v2
        with:
          name: my-artifact
      - name: Display structure of downloaded files
        run: ls -la

      # Test end-to-end by uploading two artifacts and then downloading them
      - name: Create artifact files
        run: |
          mkdir -p path/to/dir-1
          mkdir -p path/to/dir-2
          mkdir -p path/to/dir-3
          mkdir -p path/to/dir-5
          mkdir -p path/to/dir-6
          mkdir -p path/to/dir-7
          echo "Lorem ipsum dolor sit amet" > path/to/dir-1/file1.txt
          echo "Hello world from file #2" > path/to/dir-2/file2.txt
          echo "This is a going to be a test for a large enough file that should get compressed with GZip. The @actions/artifact package uses GZip to upload files. This text should have a compression ratio greater than 100% so it should get uploaded using GZip" > path/to/dir-3/gzip.txt
          dd if=/dev/random of=path/to/dir-5/file5.rnd bs=1024 count=1024
          dd if=/dev/random of=path/to/dir-6/file6.rnd bs=1024 count=$((10*1024))
          dd if=/dev/random of=path/to/dir-7/file7.rnd bs=1024 count=$((10*1024))

      # Upload a single file artifact
      - name: 'Upload artifact #1'
        uses: actions/upload-artifact@v2
        with:
          name: 'Artifact-A'
          path: path/to/dir-1/file1.txt
      
      # Upload using a wildcard pattern, name should default to 'artifact' if not provided
      - name: 'Upload artifact #2'
        uses: actions/upload-artifact@v2
        with:
          path: path/**/dir*/
      
      # Upload a directory that contains a file that will be uploaded with GZip
      - name: 'Upload artifact #3'
        uses: actions/upload-artifact@v2
        with:
          name: 'GZip-Artifact'
          path: path/to/dir-3/
      
      # Upload a directory that contains a file that will be uploaded with GZip
      - name: 'Upload artifact #4'
        uses: actions/upload-artifact@v2
        with:
          name: 'Multi-Path-Artifact'
          path: |
            path/to/dir-1/*
            path/to/dir-[23]/*
            !path/to/dir-3/*.txt
      
      # Upload a mid-size file artifact
      - name: 'Upload artifact #5'
        uses: actions/upload-artifact@v2
        with:
          name: 'Mid-Size-Artifact'
          path: path/to/dir-5/file5.rnd
      
      # Upload a big file artifact
      - name: 'Upload artifact #6'
        uses: actions/upload-artifact@v2
        with:
          name: 'Big-Artifact'
          path: path/to/dir-6/file6.rnd

      # Upload a big file artifact twice
      - name: 'Upload artifact #7 (First)'
        uses: actions/upload-artifact@v2
        with:
          name: 'Big-Uploaded-Twice'
          path: path/to/dir-7/file7.rnd

      # Upload a big file artifact twice
      - name: 'Upload artifact #7 (Second)'
        uses: actions/upload-artifact@v2
        with:
          name: 'Big-Uploaded-Twice'
          path: path/to/dir-7/file7.rnd

      # Verify artifacts. Switch to download-artifact@v2 once it's out of preview
      
      # Download Artifact #1 and verify the correctness of the content
      - name: 'Download artifact #1'
        uses: actions/download-artifact@v2
        with:
          name: 'Artifact-A'
          path: some/new/path
      
      - name: 'Verify Artifact #1'
        run: |
          file="some/new/path/file1.txt"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if [ "$(cat $file)" != "Lorem ipsum dolor sit amet" ] ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi
      
      # Download Artifact #2 and verify the correctness of the content
      - name: 'Download artifact #2'
        uses: actions/download-artifact@v2
        with:
          name: 'artifact'
          path: some/other/path
      
      - name: 'Verify Artifact #2'
        run: |
          file1="some/other/path/to/dir-1/file1.txt"
          file2="some/other/path/to/dir-2/file2.txt"
          if [ ! -f $file1 -o ! -f $file2 ] ; then
            echo "Expected files do not exist"
            exit 1
          fi
          if [ "$(cat $file1)" != "Lorem ipsum dolor sit amet" -o "$(cat $file2)" != "Hello world from file #2" ] ; then
            echo "File contents of downloaded artifacts are incorrect"
            exit 1
          fi
      
      # Download Artifact #3 and verify the correctness of the content
      - name: 'Download artifact #3'
        uses: actions/download-artifact@v2
        with:
          name: 'GZip-Artifact'
          path: gzip/artifact/path
      
      # Because a directory was used as input during the upload the parent directories, path/to/dir-3/, should not be included in the uploaded artifact
      - name: 'Verify Artifact #3'
        run: |
          gzipFile="gzip/artifact/path/gzip.txt"
          if [ ! -f $gzipFile ] ; then
            echo "Expected file do not exist"
            exit 1
          fi
          if [ "$(cat $gzipFile)" != "This is a going to be a test for a large enough file that should get compressed with GZip. The @actions/artifact package uses GZip to upload files. This text should have a compression ratio greater than 100% so it should get uploaded using GZip" ] ; then
            echo "File contents of downloaded artifact is incorrect"
            exit 1
          fi
      
      - name: 'Download artifact #4'
        uses: actions/download-artifact@v2
        with:
          name: 'Multi-Path-Artifact'
          path: multi/artifact
      
      - name: 'Verify Artifact #4'
        run: |
          file1="multi/artifact/dir-1/file1.txt"
          file2="multi/artifact/dir-2/file2.txt"
          if [ ! -f $file1 -o ! -f $file2 ] ; then
            echo "Expected files do not exist"
            exit 1
          fi
          if [ "$(cat $file1)" != "Lorem ipsum dolor sit amet" -o "$(cat $file2)" != "Hello world from file #2" ] ; then
            echo "File contents of downloaded artifacts are incorrect"
            exit 1
          fi
      
      - name: 'Download artifact #5'
        uses: actions/download-artifact@v2
        with:
          name: 'Mid-Size-Artifact'
          path: mid-size/artifact/path
      
      - name: 'Verify Artifact #5'
        run: |
          file="mid-size/artifact/path/file5.rnd"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if ! diff $file path/to/dir-5/file5.rnd ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi
      
      - name: 'Download artifact #6'
        uses: actions/download-artifact@v2
        with:
          name: 'Big-Artifact'
          path: big/artifact/path
      
      - name: 'Verify Artifact #6'
        run: |
          file="big/artifact/path/file6.rnd"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if ! diff $file path/to/dir-6/file6.rnd ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi

      - name: 'Download artifact #7'
        uses: actions/download-artifact@v2
        with:
          name: 'Big-Uploaded-Twice'
          path: big-uploaded-twice/artifact/path

      - name: 'Verify Artifact #7'
        run: |
          file="big-uploaded-twice/artifact/path/file7.rnd"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if ! diff $file path/to/dir-7/file7.rnd ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi
