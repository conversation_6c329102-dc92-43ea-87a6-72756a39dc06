
name: "GHSL-2023-0004"
on: push

jobs:
  test-artifacts:
    runs-on: ubuntu-latest
    steps:
      - run: echo "hello world" > test.txt
      - name: curl upload
        run: curl --silent --show-error --fail ${ACTIONS_RUNTIME_URL}upload/1?itemPath=../../my-artifact/secret.txt --upload-file test.txt
      - uses: actions/download-artifact@v2
        with:
          name: my-artifact
          path: test-artifacts
      - name: 'Verify Artifact #1'
        run: |
          file="test-artifacts/secret.txt"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if [ "$(cat $file)" != "hello world" ] ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi
      - name: Verify download should work by clean extra dots
        run: curl --silent --show-error --fail --path-as-is -o out.txt ${ACTIONS_RUNTIME_URL}artifact/1/../../../1/my-artifact/secret.txt
      - name: 'Verify download content'
        run: |
          file="out.txt"
          if [ ! -f $file ] ; then
            echo "Expected file does not exist"
            exit 1
          fi
          if [ "$(cat $file)" != "hello world" ] ; then
            echo "File contents of downloaded artifact are incorrect"
            exit 1
          fi
