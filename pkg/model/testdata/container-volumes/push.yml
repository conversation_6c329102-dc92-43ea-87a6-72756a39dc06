name: Job Container
on: push

jobs:
  with-volumes:
    runs-on: ubuntu-latest
    container:
      image: node:16-buster-slim
      volumes:
        - my_docker_volume:/path/to/volume
        - /path/to/nonexist/directory
        - /proc/sys/kernel/random/boot_id:/current/boot_id
    steps:
      - run: |
          set -e
          test -d /path/to/volume
          test "$(cat /proc/sys/kernel/random/boot_id)" = "$(cat /current/boot_id)"
          test -d /path/to/nonexist/directory
  