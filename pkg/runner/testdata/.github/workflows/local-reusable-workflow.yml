name: reusable

on:
  workflow_call:
    inputs:
      string_required:
        required: true
        type: string
      string_optional:
        required: false
        type: string
        default: string
      bool_required:
        required: true
        type: boolean
      bool_optional:
        required: false
        type: boolean
        default: true
      number_required:
        required: true
        type: number
      number_optional:
        required: false
        type: number
        default: ${{ 1 }}
    outputs:
      output:
        description: "A workflow output"
        value: ${{ jobs.reusable_workflow_job.outputs.job-output }}

jobs:
  reusable_workflow_job:
    runs-on: ubuntu-latest
    steps:
    - name: test required string
      run: |
        echo inputs.string_required=${{ inputs.string_required }}
        [[ "${{ inputs.string_required == 'string' }}" = "true" ]] || exit 1

    - name: test optional string
      run: |
        echo inputs.string_optional=${{ inputs.string_optional }}
        [[ "${{ inputs.string_optional == 'string' }}" = "true" ]] || exit 1

    - name: test required bool
      run: |
        echo inputs.bool_required=${{ tojson(inputs.bool_required) }}
        [[ "${{ tojson(inputs.bool_required) }}" = "true" ]] || exit 1

    - name: test optional bool
      run: |
        echo inputs.bool_optional=${{ tojson(inputs.bool_optional) }}
        [[ "${{ tojson(inputs.bool_optional) }}" = "true" ]] || exit 1

    - name: test required number
      run: |
        echo inputs.number_required=${{ tojson(inputs.number_required) }}
        [[ "${{ tojson(inputs.number_required) == '1' }}" = "true" ]] || exit 1

    - name: test optional number
      run: |
        echo inputs.number_optional=${{ tojson(inputs.number_optional) }}
        [[ "${{ tojson(inputs.number_optional) == '1' }}" = "true" ]] || exit 1

    - name: test secret
      run: |
        echo secrets.secret=${{ secrets.secret }}
        [[ "${{ secrets.secret == 'keep_it_private' }}" = "true" ]] || exit 1

    - name: test github.event_name is never workflow_call
      run: |
        echo github.event_name=${{ github.event_name }}
        [[ "${{ github.event_name != 'workflow_call' }}" = "true" ]] || exit 1

    - name: test output
      id: output_test
      run: |
        echo "value=${{ inputs.string_required }}" >> $GITHUB_OUTPUT

    outputs:
      job-output: ${{ steps.output_test.outputs.value }}
