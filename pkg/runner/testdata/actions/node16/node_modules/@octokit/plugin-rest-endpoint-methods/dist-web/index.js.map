{"version": 3, "file": "index.js", "sources": ["../dist-src/generated/endpoints.js", "../dist-src/version.js", "../dist-src/endpoints-to-methods.js", "../dist-src/index.js"], "sourcesContent": ["const Endpoints = {\n    actions: {\n        addSelectedRepoToOrgSecret: [\n            \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n        ],\n        cancelWorkflowRun: [\n            \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel\",\n        ],\n        createOrUpdateEnvironmentSecret: [\n            \"PUT /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}\",\n        ],\n        createOrUpdateOrgSecret: [\"PUT /orgs/{org}/actions/secrets/{secret_name}\"],\n        createOrUpdateRepoSecret: [\n            \"PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n        ],\n        createRegistrationTokenForOrg: [\n            \"POST /orgs/{org}/actions/runners/registration-token\",\n        ],\n        createRegistrationTokenForRepo: [\n            \"POST /repos/{owner}/{repo}/actions/runners/registration-token\",\n        ],\n        createRemoveTokenForOrg: [\"POST /orgs/{org}/actions/runners/remove-token\"],\n        createRemoveTokenForRepo: [\n            \"POST /repos/{owner}/{repo}/actions/runners/remove-token\",\n        ],\n        createWorkflowDispatch: [\n            \"POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches\",\n        ],\n        deleteArtifact: [\n            \"DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\",\n        ],\n        deleteEnvironmentSecret: [\n            \"DELETE /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}\",\n        ],\n        deleteOrgSecret: [\"DELETE /orgs/{org}/actions/secrets/{secret_name}\"],\n        deleteRepoSecret: [\n            \"DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n        ],\n        deleteSelfHostedRunnerFromOrg: [\n            \"DELETE /orgs/{org}/actions/runners/{runner_id}\",\n        ],\n        deleteSelfHostedRunnerFromRepo: [\n            \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n        ],\n        deleteWorkflowRun: [\"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n        deleteWorkflowRunLogs: [\n            \"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n        ],\n        disableSelectedRepositoryGithubActionsOrganization: [\n            \"DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n        ],\n        disableWorkflow: [\n            \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable\",\n        ],\n        downloadArtifact: [\n            \"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}\",\n        ],\n        downloadJobLogsForWorkflowRun: [\n            \"GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs\",\n        ],\n        downloadWorkflowRunLogs: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n        ],\n        enableSelectedRepositoryGithubActionsOrganization: [\n            \"PUT /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n        ],\n        enableWorkflow: [\n            \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable\",\n        ],\n        getAllowedActionsOrganization: [\n            \"GET /orgs/{org}/actions/permissions/selected-actions\",\n        ],\n        getAllowedActionsRepository: [\n            \"GET /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n        ],\n        getArtifact: [\"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\"],\n        getEnvironmentPublicKey: [\n            \"GET /repositories/{repository_id}/environments/{environment_name}/secrets/public-key\",\n        ],\n        getEnvironmentSecret: [\n            \"GET /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}\",\n        ],\n        getGithubActionsPermissionsOrganization: [\n            \"GET /orgs/{org}/actions/permissions\",\n        ],\n        getGithubActionsPermissionsRepository: [\n            \"GET /repos/{owner}/{repo}/actions/permissions\",\n        ],\n        getJobForWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/jobs/{job_id}\"],\n        getOrgPublicKey: [\"GET /orgs/{org}/actions/secrets/public-key\"],\n        getOrgSecret: [\"GET /orgs/{org}/actions/secrets/{secret_name}\"],\n        getPendingDeploymentsForRun: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n        ],\n        getRepoPermissions: [\n            \"GET /repos/{owner}/{repo}/actions/permissions\",\n            {},\n            { renamed: [\"actions\", \"getGithubActionsPermissionsRepository\"] },\n        ],\n        getRepoPublicKey: [\"GET /repos/{owner}/{repo}/actions/secrets/public-key\"],\n        getRepoSecret: [\"GET /repos/{owner}/{repo}/actions/secrets/{secret_name}\"],\n        getReviewsForRun: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals\",\n        ],\n        getSelfHostedRunnerForOrg: [\"GET /orgs/{org}/actions/runners/{runner_id}\"],\n        getSelfHostedRunnerForRepo: [\n            \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n        ],\n        getWorkflow: [\"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}\"],\n        getWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n        getWorkflowRunUsage: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing\",\n        ],\n        getWorkflowUsage: [\n            \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing\",\n        ],\n        listArtifactsForRepo: [\"GET /repos/{owner}/{repo}/actions/artifacts\"],\n        listEnvironmentSecrets: [\n            \"GET /repositories/{repository_id}/environments/{environment_name}/secrets\",\n        ],\n        listJobsForWorkflowRun: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n        ],\n        listOrgSecrets: [\"GET /orgs/{org}/actions/secrets\"],\n        listRepoSecrets: [\"GET /repos/{owner}/{repo}/actions/secrets\"],\n        listRepoWorkflows: [\"GET /repos/{owner}/{repo}/actions/workflows\"],\n        listRunnerApplicationsForOrg: [\"GET /orgs/{org}/actions/runners/downloads\"],\n        listRunnerApplicationsForRepo: [\n            \"GET /repos/{owner}/{repo}/actions/runners/downloads\",\n        ],\n        listSelectedReposForOrgSecret: [\n            \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n        ],\n        listSelectedRepositoriesEnabledGithubActionsOrganization: [\n            \"GET /orgs/{org}/actions/permissions/repositories\",\n        ],\n        listSelfHostedRunnersForOrg: [\"GET /orgs/{org}/actions/runners\"],\n        listSelfHostedRunnersForRepo: [\"GET /repos/{owner}/{repo}/actions/runners\"],\n        listWorkflowRunArtifacts: [\n            \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n        ],\n        listWorkflowRuns: [\n            \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n        ],\n        listWorkflowRunsForRepo: [\"GET /repos/{owner}/{repo}/actions/runs\"],\n        reRunWorkflow: [\"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun\"],\n        removeSelectedRepoFromOrgSecret: [\n            \"DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n        ],\n        reviewPendingDeploymentsForRun: [\n            \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n        ],\n        setAllowedActionsOrganization: [\n            \"PUT /orgs/{org}/actions/permissions/selected-actions\",\n        ],\n        setAllowedActionsRepository: [\n            \"PUT /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n        ],\n        setGithubActionsPermissionsOrganization: [\n            \"PUT /orgs/{org}/actions/permissions\",\n        ],\n        setGithubActionsPermissionsRepository: [\n            \"PUT /repos/{owner}/{repo}/actions/permissions\",\n        ],\n        setSelectedReposForOrgSecret: [\n            \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n        ],\n        setSelectedRepositoriesEnabledGithubActionsOrganization: [\n            \"PUT /orgs/{org}/actions/permissions/repositories\",\n        ],\n    },\n    activity: {\n        checkRepoIsStarredByAuthenticatedUser: [\"GET /user/starred/{owner}/{repo}\"],\n        deleteRepoSubscription: [\"DELETE /repos/{owner}/{repo}/subscription\"],\n        deleteThreadSubscription: [\n            \"DELETE /notifications/threads/{thread_id}/subscription\",\n        ],\n        getFeeds: [\"GET /feeds\"],\n        getRepoSubscription: [\"GET /repos/{owner}/{repo}/subscription\"],\n        getThread: [\"GET /notifications/threads/{thread_id}\"],\n        getThreadSubscriptionForAuthenticatedUser: [\n            \"GET /notifications/threads/{thread_id}/subscription\",\n        ],\n        listEventsForAuthenticatedUser: [\"GET /users/{username}/events\"],\n        listNotificationsForAuthenticatedUser: [\"GET /notifications\"],\n        listOrgEventsForAuthenticatedUser: [\n            \"GET /users/{username}/events/orgs/{org}\",\n        ],\n        listPublicEvents: [\"GET /events\"],\n        listPublicEventsForRepoNetwork: [\"GET /networks/{owner}/{repo}/events\"],\n        listPublicEventsForUser: [\"GET /users/{username}/events/public\"],\n        listPublicOrgEvents: [\"GET /orgs/{org}/events\"],\n        listReceivedEventsForUser: [\"GET /users/{username}/received_events\"],\n        listReceivedPublicEventsForUser: [\n            \"GET /users/{username}/received_events/public\",\n        ],\n        listRepoEvents: [\"GET /repos/{owner}/{repo}/events\"],\n        listRepoNotificationsForAuthenticatedUser: [\n            \"GET /repos/{owner}/{repo}/notifications\",\n        ],\n        listReposStarredByAuthenticatedUser: [\"GET /user/starred\"],\n        listReposStarredByUser: [\"GET /users/{username}/starred\"],\n        listReposWatchedByUser: [\"GET /users/{username}/subscriptions\"],\n        listStargazersForRepo: [\"GET /repos/{owner}/{repo}/stargazers\"],\n        listWatchedReposForAuthenticatedUser: [\"GET /user/subscriptions\"],\n        listWatchersForRepo: [\"GET /repos/{owner}/{repo}/subscribers\"],\n        markNotificationsAsRead: [\"PUT /notifications\"],\n        markRepoNotificationsAsRead: [\"PUT /repos/{owner}/{repo}/notifications\"],\n        markThreadAsRead: [\"PATCH /notifications/threads/{thread_id}\"],\n        setRepoSubscription: [\"PUT /repos/{owner}/{repo}/subscription\"],\n        setThreadSubscription: [\n            \"PUT /notifications/threads/{thread_id}/subscription\",\n        ],\n        starRepoForAuthenticatedUser: [\"PUT /user/starred/{owner}/{repo}\"],\n        unstarRepoForAuthenticatedUser: [\"DELETE /user/starred/{owner}/{repo}\"],\n    },\n    apps: {\n        addRepoToInstallation: [\n            \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n        ],\n        checkToken: [\"POST /applications/{client_id}/token\"],\n        createContentAttachment: [\n            \"POST /content_references/{content_reference_id}/attachments\",\n            { mediaType: { previews: [\"corsair\"] } },\n        ],\n        createFromManifest: [\"POST /app-manifests/{code}/conversions\"],\n        createInstallationAccessToken: [\n            \"POST /app/installations/{installation_id}/access_tokens\",\n        ],\n        deleteAuthorization: [\"DELETE /applications/{client_id}/grant\"],\n        deleteInstallation: [\"DELETE /app/installations/{installation_id}\"],\n        deleteToken: [\"DELETE /applications/{client_id}/token\"],\n        getAuthenticated: [\"GET /app\"],\n        getBySlug: [\"GET /apps/{app_slug}\"],\n        getInstallation: [\"GET /app/installations/{installation_id}\"],\n        getOrgInstallation: [\"GET /orgs/{org}/installation\"],\n        getRepoInstallation: [\"GET /repos/{owner}/{repo}/installation\"],\n        getSubscriptionPlanForAccount: [\n            \"GET /marketplace_listing/accounts/{account_id}\",\n        ],\n        getSubscriptionPlanForAccountStubbed: [\n            \"GET /marketplace_listing/stubbed/accounts/{account_id}\",\n        ],\n        getUserInstallation: [\"GET /users/{username}/installation\"],\n        getWebhookConfigForApp: [\"GET /app/hook/config\"],\n        listAccountsForPlan: [\"GET /marketplace_listing/plans/{plan_id}/accounts\"],\n        listAccountsForPlanStubbed: [\n            \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n        ],\n        listInstallationReposForAuthenticatedUser: [\n            \"GET /user/installations/{installation_id}/repositories\",\n        ],\n        listInstallations: [\"GET /app/installations\"],\n        listInstallationsForAuthenticatedUser: [\"GET /user/installations\"],\n        listPlans: [\"GET /marketplace_listing/plans\"],\n        listPlansStubbed: [\"GET /marketplace_listing/stubbed/plans\"],\n        listReposAccessibleToInstallation: [\"GET /installation/repositories\"],\n        listSubscriptionsForAuthenticatedUser: [\"GET /user/marketplace_purchases\"],\n        listSubscriptionsForAuthenticatedUserStubbed: [\n            \"GET /user/marketplace_purchases/stubbed\",\n        ],\n        removeRepoFromInstallation: [\n            \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n        ],\n        resetToken: [\"PATCH /applications/{client_id}/token\"],\n        revokeInstallationAccessToken: [\"DELETE /installation/token\"],\n        scopeToken: [\"POST /applications/{client_id}/token/scoped\"],\n        suspendInstallation: [\"PUT /app/installations/{installation_id}/suspended\"],\n        unsuspendInstallation: [\n            \"DELETE /app/installations/{installation_id}/suspended\",\n        ],\n        updateWebhookConfigForApp: [\"PATCH /app/hook/config\"],\n    },\n    billing: {\n        getGithubActionsBillingOrg: [\"GET /orgs/{org}/settings/billing/actions\"],\n        getGithubActionsBillingUser: [\n            \"GET /users/{username}/settings/billing/actions\",\n        ],\n        getGithubPackagesBillingOrg: [\"GET /orgs/{org}/settings/billing/packages\"],\n        getGithubPackagesBillingUser: [\n            \"GET /users/{username}/settings/billing/packages\",\n        ],\n        getSharedStorageBillingOrg: [\n            \"GET /orgs/{org}/settings/billing/shared-storage\",\n        ],\n        getSharedStorageBillingUser: [\n            \"GET /users/{username}/settings/billing/shared-storage\",\n        ],\n    },\n    checks: {\n        create: [\"POST /repos/{owner}/{repo}/check-runs\"],\n        createSuite: [\"POST /repos/{owner}/{repo}/check-suites\"],\n        get: [\"GET /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n        getSuite: [\"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}\"],\n        listAnnotations: [\n            \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n        ],\n        listForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\"],\n        listForSuite: [\n            \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n        ],\n        listSuitesForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\"],\n        rerequestSuite: [\n            \"POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest\",\n        ],\n        setSuitesPreferences: [\n            \"PATCH /repos/{owner}/{repo}/check-suites/preferences\",\n        ],\n        update: [\"PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n    },\n    codeScanning: {\n        deleteAnalysis: [\n            \"DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}\",\n        ],\n        getAlert: [\n            \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n            {},\n            { renamedParameters: { alert_id: \"alert_number\" } },\n        ],\n        getAnalysis: [\n            \"GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}\",\n        ],\n        getSarif: [\"GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}\"],\n        listAlertsForRepo: [\"GET /repos/{owner}/{repo}/code-scanning/alerts\"],\n        listAlertsInstances: [\n            \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n        ],\n        listRecentAnalyses: [\"GET /repos/{owner}/{repo}/code-scanning/analyses\"],\n        updateAlert: [\n            \"PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n        ],\n        uploadSarif: [\"POST /repos/{owner}/{repo}/code-scanning/sarifs\"],\n    },\n    codesOfConduct: {\n        getAllCodesOfConduct: [\n            \"GET /codes_of_conduct\",\n            { mediaType: { previews: [\"scarlet-witch\"] } },\n        ],\n        getConductCode: [\n            \"GET /codes_of_conduct/{key}\",\n            { mediaType: { previews: [\"scarlet-witch\"] } },\n        ],\n        getForRepo: [\n            \"GET /repos/{owner}/{repo}/community/code_of_conduct\",\n            { mediaType: { previews: [\"scarlet-witch\"] } },\n        ],\n    },\n    emojis: { get: [\"GET /emojis\"] },\n    enterpriseAdmin: {\n        disableSelectedOrganizationGithubActionsEnterprise: [\n            \"DELETE /enterprises/{enterprise}/actions/permissions/organizations/{org_id}\",\n        ],\n        enableSelectedOrganizationGithubActionsEnterprise: [\n            \"PUT /enterprises/{enterprise}/actions/permissions/organizations/{org_id}\",\n        ],\n        getAllowedActionsEnterprise: [\n            \"GET /enterprises/{enterprise}/actions/permissions/selected-actions\",\n        ],\n        getGithubActionsPermissionsEnterprise: [\n            \"GET /enterprises/{enterprise}/actions/permissions\",\n        ],\n        listSelectedOrganizationsEnabledGithubActionsEnterprise: [\n            \"GET /enterprises/{enterprise}/actions/permissions/organizations\",\n        ],\n        setAllowedActionsEnterprise: [\n            \"PUT /enterprises/{enterprise}/actions/permissions/selected-actions\",\n        ],\n        setGithubActionsPermissionsEnterprise: [\n            \"PUT /enterprises/{enterprise}/actions/permissions\",\n        ],\n        setSelectedOrganizationsEnabledGithubActionsEnterprise: [\n            \"PUT /enterprises/{enterprise}/actions/permissions/organizations\",\n        ],\n    },\n    gists: {\n        checkIsStarred: [\"GET /gists/{gist_id}/star\"],\n        create: [\"POST /gists\"],\n        createComment: [\"POST /gists/{gist_id}/comments\"],\n        delete: [\"DELETE /gists/{gist_id}\"],\n        deleteComment: [\"DELETE /gists/{gist_id}/comments/{comment_id}\"],\n        fork: [\"POST /gists/{gist_id}/forks\"],\n        get: [\"GET /gists/{gist_id}\"],\n        getComment: [\"GET /gists/{gist_id}/comments/{comment_id}\"],\n        getRevision: [\"GET /gists/{gist_id}/{sha}\"],\n        list: [\"GET /gists\"],\n        listComments: [\"GET /gists/{gist_id}/comments\"],\n        listCommits: [\"GET /gists/{gist_id}/commits\"],\n        listForUser: [\"GET /users/{username}/gists\"],\n        listForks: [\"GET /gists/{gist_id}/forks\"],\n        listPublic: [\"GET /gists/public\"],\n        listStarred: [\"GET /gists/starred\"],\n        star: [\"PUT /gists/{gist_id}/star\"],\n        unstar: [\"DELETE /gists/{gist_id}/star\"],\n        update: [\"PATCH /gists/{gist_id}\"],\n        updateComment: [\"PATCH /gists/{gist_id}/comments/{comment_id}\"],\n    },\n    git: {\n        createBlob: [\"POST /repos/{owner}/{repo}/git/blobs\"],\n        createCommit: [\"POST /repos/{owner}/{repo}/git/commits\"],\n        createRef: [\"POST /repos/{owner}/{repo}/git/refs\"],\n        createTag: [\"POST /repos/{owner}/{repo}/git/tags\"],\n        createTree: [\"POST /repos/{owner}/{repo}/git/trees\"],\n        deleteRef: [\"DELETE /repos/{owner}/{repo}/git/refs/{ref}\"],\n        getBlob: [\"GET /repos/{owner}/{repo}/git/blobs/{file_sha}\"],\n        getCommit: [\"GET /repos/{owner}/{repo}/git/commits/{commit_sha}\"],\n        getRef: [\"GET /repos/{owner}/{repo}/git/ref/{ref}\"],\n        getTag: [\"GET /repos/{owner}/{repo}/git/tags/{tag_sha}\"],\n        getTree: [\"GET /repos/{owner}/{repo}/git/trees/{tree_sha}\"],\n        listMatchingRefs: [\"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\"],\n        updateRef: [\"PATCH /repos/{owner}/{repo}/git/refs/{ref}\"],\n    },\n    gitignore: {\n        getAllTemplates: [\"GET /gitignore/templates\"],\n        getTemplate: [\"GET /gitignore/templates/{name}\"],\n    },\n    interactions: {\n        getRestrictionsForAuthenticatedUser: [\"GET /user/interaction-limits\"],\n        getRestrictionsForOrg: [\"GET /orgs/{org}/interaction-limits\"],\n        getRestrictionsForRepo: [\"GET /repos/{owner}/{repo}/interaction-limits\"],\n        getRestrictionsForYourPublicRepos: [\n            \"GET /user/interaction-limits\",\n            {},\n            { renamed: [\"interactions\", \"getRestrictionsForAuthenticatedUser\"] },\n        ],\n        removeRestrictionsForAuthenticatedUser: [\"DELETE /user/interaction-limits\"],\n        removeRestrictionsForOrg: [\"DELETE /orgs/{org}/interaction-limits\"],\n        removeRestrictionsForRepo: [\n            \"DELETE /repos/{owner}/{repo}/interaction-limits\",\n        ],\n        removeRestrictionsForYourPublicRepos: [\n            \"DELETE /user/interaction-limits\",\n            {},\n            { renamed: [\"interactions\", \"removeRestrictionsForAuthenticatedUser\"] },\n        ],\n        setRestrictionsForAuthenticatedUser: [\"PUT /user/interaction-limits\"],\n        setRestrictionsForOrg: [\"PUT /orgs/{org}/interaction-limits\"],\n        setRestrictionsForRepo: [\"PUT /repos/{owner}/{repo}/interaction-limits\"],\n        setRestrictionsForYourPublicRepos: [\n            \"PUT /user/interaction-limits\",\n            {},\n            { renamed: [\"interactions\", \"setRestrictionsForAuthenticatedUser\"] },\n        ],\n    },\n    issues: {\n        addAssignees: [\n            \"POST /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n        ],\n        addLabels: [\"POST /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n        checkUserCanBeAssigned: [\"GET /repos/{owner}/{repo}/assignees/{assignee}\"],\n        create: [\"POST /repos/{owner}/{repo}/issues\"],\n        createComment: [\n            \"POST /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n        ],\n        createLabel: [\"POST /repos/{owner}/{repo}/labels\"],\n        createMilestone: [\"POST /repos/{owner}/{repo}/milestones\"],\n        deleteComment: [\n            \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}\",\n        ],\n        deleteLabel: [\"DELETE /repos/{owner}/{repo}/labels/{name}\"],\n        deleteMilestone: [\n            \"DELETE /repos/{owner}/{repo}/milestones/{milestone_number}\",\n        ],\n        get: [\"GET /repos/{owner}/{repo}/issues/{issue_number}\"],\n        getComment: [\"GET /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n        getEvent: [\"GET /repos/{owner}/{repo}/issues/events/{event_id}\"],\n        getLabel: [\"GET /repos/{owner}/{repo}/labels/{name}\"],\n        getMilestone: [\"GET /repos/{owner}/{repo}/milestones/{milestone_number}\"],\n        list: [\"GET /issues\"],\n        listAssignees: [\"GET /repos/{owner}/{repo}/assignees\"],\n        listComments: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\"],\n        listCommentsForRepo: [\"GET /repos/{owner}/{repo}/issues/comments\"],\n        listEvents: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/events\"],\n        listEventsForRepo: [\"GET /repos/{owner}/{repo}/issues/events\"],\n        listEventsForTimeline: [\n            \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n            { mediaType: { previews: [\"mockingbird\"] } },\n        ],\n        listForAuthenticatedUser: [\"GET /user/issues\"],\n        listForOrg: [\"GET /orgs/{org}/issues\"],\n        listForRepo: [\"GET /repos/{owner}/{repo}/issues\"],\n        listLabelsForMilestone: [\n            \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n        ],\n        listLabelsForRepo: [\"GET /repos/{owner}/{repo}/labels\"],\n        listLabelsOnIssue: [\n            \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n        ],\n        listMilestones: [\"GET /repos/{owner}/{repo}/milestones\"],\n        lock: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n        removeAllLabels: [\n            \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n        ],\n        removeAssignees: [\n            \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n        ],\n        removeLabel: [\n            \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}\",\n        ],\n        setLabels: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n        unlock: [\"DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n        update: [\"PATCH /repos/{owner}/{repo}/issues/{issue_number}\"],\n        updateComment: [\"PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n        updateLabel: [\"PATCH /repos/{owner}/{repo}/labels/{name}\"],\n        updateMilestone: [\n            \"PATCH /repos/{owner}/{repo}/milestones/{milestone_number}\",\n        ],\n    },\n    licenses: {\n        get: [\"GET /licenses/{license}\"],\n        getAllCommonlyUsed: [\"GET /licenses\"],\n        getForRepo: [\"GET /repos/{owner}/{repo}/license\"],\n    },\n    markdown: {\n        render: [\"POST /markdown\"],\n        renderRaw: [\n            \"POST /markdown/raw\",\n            { headers: { \"content-type\": \"text/plain; charset=utf-8\" } },\n        ],\n    },\n    meta: {\n        get: [\"GET /meta\"],\n        getOctocat: [\"GET /octocat\"],\n        getZen: [\"GET /zen\"],\n        root: [\"GET /\"],\n    },\n    migrations: {\n        cancelImport: [\"DELETE /repos/{owner}/{repo}/import\"],\n        deleteArchiveForAuthenticatedUser: [\n            \"DELETE /user/migrations/{migration_id}/archive\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        deleteArchiveForOrg: [\n            \"DELETE /orgs/{org}/migrations/{migration_id}/archive\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        downloadArchiveForOrg: [\n            \"GET /orgs/{org}/migrations/{migration_id}/archive\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        getArchiveForAuthenticatedUser: [\n            \"GET /user/migrations/{migration_id}/archive\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        getCommitAuthors: <AUTHORS>\n        getImportStatus: [\"GET /repos/{owner}/{repo}/import\"],\n        getLargeFiles: [\"GET /repos/{owner}/{repo}/import/large_files\"],\n        getStatusForAuthenticatedUser: [\n            \"GET /user/migrations/{migration_id}\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        getStatusForOrg: [\n            \"GET /orgs/{org}/migrations/{migration_id}\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        listForAuthenticatedUser: [\n            \"GET /user/migrations\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        listForOrg: [\n            \"GET /orgs/{org}/migrations\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        listReposForOrg: [\n            \"GET /orgs/{org}/migrations/{migration_id}/repositories\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        listReposForUser: [\n            \"GET /user/migrations/{migration_id}/repositories\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        mapCommitAuthor: [\"PATCH /repos/{owner}/{repo}/import/authors/{author_id}\"],\n        setLfsPreference: [\"PATCH /repos/{owner}/{repo}/import/lfs\"],\n        startForAuthenticatedUser: [\"POST /user/migrations\"],\n        startForOrg: [\"POST /orgs/{org}/migrations\"],\n        startImport: [\"PUT /repos/{owner}/{repo}/import\"],\n        unlockRepoForAuthenticatedUser: [\n            \"DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        unlockRepoForOrg: [\n            \"DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock\",\n            { mediaType: { previews: [\"wyandotte\"] } },\n        ],\n        updateImport: [\"PATCH /repos/{owner}/{repo}/import\"],\n    },\n    orgs: {\n        blockUser: [\"PUT /orgs/{org}/blocks/{username}\"],\n        cancelInvitation: [\"DELETE /orgs/{org}/invitations/{invitation_id}\"],\n        checkBlockedUser: [\"GET /orgs/{org}/blocks/{username}\"],\n        checkMembershipForUser: [\"GET /orgs/{org}/members/{username}\"],\n        checkPublicMembershipForUser: [\"GET /orgs/{org}/public_members/{username}\"],\n        convertMemberToOutsideCollaborator: [\n            \"PUT /orgs/{org}/outside_collaborators/{username}\",\n        ],\n        createInvitation: [\"POST /orgs/{org}/invitations\"],\n        createWebhook: [\"POST /orgs/{org}/hooks\"],\n        deleteWebhook: [\"DELETE /orgs/{org}/hooks/{hook_id}\"],\n        get: [\"GET /orgs/{org}\"],\n        getMembershipForAuthenticatedUser: [\"GET /user/memberships/orgs/{org}\"],\n        getMembershipForUser: [\"GET /orgs/{org}/memberships/{username}\"],\n        getWebhook: [\"GET /orgs/{org}/hooks/{hook_id}\"],\n        getWebhookConfigForOrg: [\"GET /orgs/{org}/hooks/{hook_id}/config\"],\n        list: [\"GET /organizations\"],\n        listAppInstallations: [\"GET /orgs/{org}/installations\"],\n        listBlockedUsers: [\"GET /orgs/{org}/blocks\"],\n        listFailedInvitations: [\"GET /orgs/{org}/failed_invitations\"],\n        listForAuthenticatedUser: [\"GET /user/orgs\"],\n        listForUser: [\"GET /users/{username}/orgs\"],\n        listInvitationTeams: [\"GET /orgs/{org}/invitations/{invitation_id}/teams\"],\n        listMembers: [\"GET /orgs/{org}/members\"],\n        listMembershipsForAuthenticatedUser: [\"GET /user/memberships/orgs\"],\n        listOutsideCollaborators: [\"GET /orgs/{org}/outside_collaborators\"],\n        listPendingInvitations: [\"GET /orgs/{org}/invitations\"],\n        listPublicMembers: [\"GET /orgs/{org}/public_members\"],\n        listWebhooks: [\"GET /orgs/{org}/hooks\"],\n        pingWebhook: [\"POST /orgs/{org}/hooks/{hook_id}/pings\"],\n        removeMember: [\"DELETE /orgs/{org}/members/{username}\"],\n        removeMembershipForUser: [\"DELETE /orgs/{org}/memberships/{username}\"],\n        removeOutsideCollaborator: [\n            \"DELETE /orgs/{org}/outside_collaborators/{username}\",\n        ],\n        removePublicMembershipForAuthenticatedUser: [\n            \"DELETE /orgs/{org}/public_members/{username}\",\n        ],\n        setMembershipForUser: [\"PUT /orgs/{org}/memberships/{username}\"],\n        setPublicMembershipForAuthenticatedUser: [\n            \"PUT /orgs/{org}/public_members/{username}\",\n        ],\n        unblockUser: [\"DELETE /orgs/{org}/blocks/{username}\"],\n        update: [\"PATCH /orgs/{org}\"],\n        updateMembershipForAuthenticatedUser: [\n            \"PATCH /user/memberships/orgs/{org}\",\n        ],\n        updateWebhook: [\"PATCH /orgs/{org}/hooks/{hook_id}\"],\n        updateWebhookConfigForOrg: [\"PATCH /orgs/{org}/hooks/{hook_id}/config\"],\n    },\n    packages: {\n        deletePackageForAuthenticatedUser: [\n            \"DELETE /user/packages/{package_type}/{package_name}\",\n        ],\n        deletePackageForOrg: [\n            \"DELETE /orgs/{org}/packages/{package_type}/{package_name}\",\n        ],\n        deletePackageVersionForAuthenticatedUser: [\n            \"DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n        ],\n        deletePackageVersionForOrg: [\n            \"DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n        ],\n        getAllPackageVersionsForAPackageOwnedByAnOrg: [\n            \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n            {},\n            { renamed: [\"packages\", \"getAllPackageVersionsForPackageOwnedByOrg\"] },\n        ],\n        getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser: [\n            \"GET /user/packages/{package_type}/{package_name}/versions\",\n            {},\n            {\n                renamed: [\n                    \"packages\",\n                    \"getAllPackageVersionsForPackageOwnedByAuthenticatedUser\",\n                ],\n            },\n        ],\n        getAllPackageVersionsForPackageOwnedByAuthenticatedUser: [\n            \"GET /user/packages/{package_type}/{package_name}/versions\",\n        ],\n        getAllPackageVersionsForPackageOwnedByOrg: [\n            \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n        ],\n        getAllPackageVersionsForPackageOwnedByUser: [\n            \"GET /users/{username}/packages/{package_type}/{package_name}/versions\",\n        ],\n        getPackageForAuthenticatedUser: [\n            \"GET /user/packages/{package_type}/{package_name}\",\n        ],\n        getPackageForOrganization: [\n            \"GET /orgs/{org}/packages/{package_type}/{package_name}\",\n        ],\n        getPackageForUser: [\n            \"GET /users/{username}/packages/{package_type}/{package_name}\",\n        ],\n        getPackageVersionForAuthenticatedUser: [\n            \"GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n        ],\n        getPackageVersionForOrganization: [\n            \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n        ],\n        getPackageVersionForUser: [\n            \"GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n        ],\n        restorePackageForAuthenticatedUser: [\n            \"POST /user/packages/{package_type}/{package_name}/restore{?token}\",\n        ],\n        restorePackageForOrg: [\n            \"POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}\",\n        ],\n        restorePackageVersionForAuthenticatedUser: [\n            \"POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n        ],\n        restorePackageVersionForOrg: [\n            \"POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n        ],\n    },\n    projects: {\n        addCollaborator: [\n            \"PUT /projects/{project_id}/collaborators/{username}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        createCard: [\n            \"POST /projects/columns/{column_id}/cards\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        createColumn: [\n            \"POST /projects/{project_id}/columns\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        createForAuthenticatedUser: [\n            \"POST /user/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        createForOrg: [\n            \"POST /orgs/{org}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        createForRepo: [\n            \"POST /repos/{owner}/{repo}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        delete: [\n            \"DELETE /projects/{project_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        deleteCard: [\n            \"DELETE /projects/columns/cards/{card_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        deleteColumn: [\n            \"DELETE /projects/columns/{column_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        get: [\n            \"GET /projects/{project_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        getCard: [\n            \"GET /projects/columns/cards/{card_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        getColumn: [\n            \"GET /projects/columns/{column_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        getPermissionForUser: [\n            \"GET /projects/{project_id}/collaborators/{username}/permission\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listCards: [\n            \"GET /projects/columns/{column_id}/cards\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listCollaborators: [\n            \"GET /projects/{project_id}/collaborators\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listColumns: [\n            \"GET /projects/{project_id}/columns\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listForOrg: [\n            \"GET /orgs/{org}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listForRepo: [\n            \"GET /repos/{owner}/{repo}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listForUser: [\n            \"GET /users/{username}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        moveCard: [\n            \"POST /projects/columns/cards/{card_id}/moves\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        moveColumn: [\n            \"POST /projects/columns/{column_id}/moves\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        removeCollaborator: [\n            \"DELETE /projects/{project_id}/collaborators/{username}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        update: [\n            \"PATCH /projects/{project_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        updateCard: [\n            \"PATCH /projects/columns/cards/{card_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        updateColumn: [\n            \"PATCH /projects/columns/{column_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n    },\n    pulls: {\n        checkIfMerged: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n        create: [\"POST /repos/{owner}/{repo}/pulls\"],\n        createReplyForReviewComment: [\n            \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies\",\n        ],\n        createReview: [\"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n        createReviewComment: [\n            \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n        ],\n        deletePendingReview: [\n            \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n        ],\n        deleteReviewComment: [\n            \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n        ],\n        dismissReview: [\n            \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals\",\n        ],\n        get: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}\"],\n        getReview: [\n            \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n        ],\n        getReviewComment: [\"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}\"],\n        list: [\"GET /repos/{owner}/{repo}/pulls\"],\n        listCommentsForReview: [\n            \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n        ],\n        listCommits: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\"],\n        listFiles: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\"],\n        listRequestedReviewers: [\n            \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n        ],\n        listReviewComments: [\n            \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n        ],\n        listReviewCommentsForRepo: [\"GET /repos/{owner}/{repo}/pulls/comments\"],\n        listReviews: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n        merge: [\"PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n        removeRequestedReviewers: [\n            \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n        ],\n        requestReviewers: [\n            \"POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n        ],\n        submitReview: [\n            \"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events\",\n        ],\n        update: [\"PATCH /repos/{owner}/{repo}/pulls/{pull_number}\"],\n        updateBranch: [\n            \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch\",\n            { mediaType: { previews: [\"lydian\"] } },\n        ],\n        updateReview: [\n            \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n        ],\n        updateReviewComment: [\n            \"PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n        ],\n    },\n    rateLimit: { get: [\"GET /rate_limit\"] },\n    reactions: {\n        createForCommitComment: [\n            \"POST /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        createForIssue: [\n            \"POST /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        createForIssueComment: [\n            \"POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        createForPullRequestReviewComment: [\n            \"POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        createForTeamDiscussionCommentInOrg: [\n            \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        createForTeamDiscussionInOrg: [\n            \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForCommitComment: [\n            \"DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForIssue: [\n            \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForIssueComment: [\n            \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForPullRequestComment: [\n            \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForTeamDiscussion: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteForTeamDiscussionComment: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        deleteLegacy: [\n            \"DELETE /reactions/{reaction_id}\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n            {\n                deprecated: \"octokit.rest.reactions.deleteLegacy() is deprecated, see https://docs.github.com/rest/reference/reactions/#delete-a-reaction-legacy\",\n            },\n        ],\n        listForCommitComment: [\n            \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        listForIssue: [\n            \"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        listForIssueComment: [\n            \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        listForPullRequestReviewComment: [\n            \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        listForTeamDiscussionCommentInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n        listForTeamDiscussionInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n            { mediaType: { previews: [\"squirrel-girl\"] } },\n        ],\n    },\n    repos: {\n        acceptInvitation: [\"PATCH /user/repository_invitations/{invitation_id}\"],\n        addAppAccessRestrictions: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n            {},\n            { mapToData: \"apps\" },\n        ],\n        addCollaborator: [\"PUT /repos/{owner}/{repo}/collaborators/{username}\"],\n        addStatusCheckContexts: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n            {},\n            { mapToData: \"contexts\" },\n        ],\n        addTeamAccessRestrictions: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n            {},\n            { mapToData: \"teams\" },\n        ],\n        addUserAccessRestrictions: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n            {},\n            { mapToData: \"users\" },\n        ],\n        checkCollaborator: [\"GET /repos/{owner}/{repo}/collaborators/{username}\"],\n        checkVulnerabilityAlerts: [\n            \"GET /repos/{owner}/{repo}/vulnerability-alerts\",\n            { mediaType: { previews: [\"dorian\"] } },\n        ],\n        compareCommits: [\"GET /repos/{owner}/{repo}/compare/{base}...{head}\"],\n        createCommitComment: [\n            \"POST /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n        ],\n        createCommitSignatureProtection: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n            { mediaType: { previews: [\"zzzax\"] } },\n        ],\n        createCommitStatus: [\"POST /repos/{owner}/{repo}/statuses/{sha}\"],\n        createDeployKey: [\"POST /repos/{owner}/{repo}/keys\"],\n        createDeployment: [\"POST /repos/{owner}/{repo}/deployments\"],\n        createDeploymentStatus: [\n            \"POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n        ],\n        createDispatchEvent: [\"POST /repos/{owner}/{repo}/dispatches\"],\n        createForAuthenticatedUser: [\"POST /user/repos\"],\n        createFork: [\"POST /repos/{owner}/{repo}/forks\"],\n        createInOrg: [\"POST /orgs/{org}/repos\"],\n        createOrUpdateEnvironment: [\n            \"PUT /repos/{owner}/{repo}/environments/{environment_name}\",\n        ],\n        createOrUpdateFileContents: [\"PUT /repos/{owner}/{repo}/contents/{path}\"],\n        createPagesSite: [\n            \"POST /repos/{owner}/{repo}/pages\",\n            { mediaType: { previews: [\"switcheroo\"] } },\n        ],\n        createRelease: [\"POST /repos/{owner}/{repo}/releases\"],\n        createUsingTemplate: [\n            \"POST /repos/{template_owner}/{template_repo}/generate\",\n            { mediaType: { previews: [\"baptiste\"] } },\n        ],\n        createWebhook: [\"POST /repos/{owner}/{repo}/hooks\"],\n        declineInvitation: [\"DELETE /user/repository_invitations/{invitation_id}\"],\n        delete: [\"DELETE /repos/{owner}/{repo}\"],\n        deleteAccessRestrictions: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n        ],\n        deleteAdminBranchProtection: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n        ],\n        deleteAnEnvironment: [\n            \"DELETE /repos/{owner}/{repo}/environments/{environment_name}\",\n        ],\n        deleteBranchProtection: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection\",\n        ],\n        deleteCommitComment: [\"DELETE /repos/{owner}/{repo}/comments/{comment_id}\"],\n        deleteCommitSignatureProtection: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n            { mediaType: { previews: [\"zzzax\"] } },\n        ],\n        deleteDeployKey: [\"DELETE /repos/{owner}/{repo}/keys/{key_id}\"],\n        deleteDeployment: [\n            \"DELETE /repos/{owner}/{repo}/deployments/{deployment_id}\",\n        ],\n        deleteFile: [\"DELETE /repos/{owner}/{repo}/contents/{path}\"],\n        deleteInvitation: [\n            \"DELETE /repos/{owner}/{repo}/invitations/{invitation_id}\",\n        ],\n        deletePagesSite: [\n            \"DELETE /repos/{owner}/{repo}/pages\",\n            { mediaType: { previews: [\"switcheroo\"] } },\n        ],\n        deletePullRequestReviewProtection: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n        ],\n        deleteRelease: [\"DELETE /repos/{owner}/{repo}/releases/{release_id}\"],\n        deleteReleaseAsset: [\n            \"DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n        ],\n        deleteWebhook: [\"DELETE /repos/{owner}/{repo}/hooks/{hook_id}\"],\n        disableAutomatedSecurityFixes: [\n            \"DELETE /repos/{owner}/{repo}/automated-security-fixes\",\n            { mediaType: { previews: [\"london\"] } },\n        ],\n        disableVulnerabilityAlerts: [\n            \"DELETE /repos/{owner}/{repo}/vulnerability-alerts\",\n            { mediaType: { previews: [\"dorian\"] } },\n        ],\n        downloadArchive: [\n            \"GET /repos/{owner}/{repo}/zipball/{ref}\",\n            {},\n            { renamed: [\"repos\", \"downloadZipballArchive\"] },\n        ],\n        downloadTarballArchive: [\"GET /repos/{owner}/{repo}/tarball/{ref}\"],\n        downloadZipballArchive: [\"GET /repos/{owner}/{repo}/zipball/{ref}\"],\n        enableAutomatedSecurityFixes: [\n            \"PUT /repos/{owner}/{repo}/automated-security-fixes\",\n            { mediaType: { previews: [\"london\"] } },\n        ],\n        enableVulnerabilityAlerts: [\n            \"PUT /repos/{owner}/{repo}/vulnerability-alerts\",\n            { mediaType: { previews: [\"dorian\"] } },\n        ],\n        get: [\"GET /repos/{owner}/{repo}\"],\n        getAccessRestrictions: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n        ],\n        getAdminBranchProtection: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n        ],\n        getAllEnvironments: [\"GET /repos/{owner}/{repo}/environments\"],\n        getAllStatusCheckContexts: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n        ],\n        getAllTopics: [\n            \"GET /repos/{owner}/{repo}/topics\",\n            { mediaType: { previews: [\"mercy\"] } },\n        ],\n        getAppsWithAccessToProtectedBranch: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n        ],\n        getBranch: [\"GET /repos/{owner}/{repo}/branches/{branch}\"],\n        getBranchProtection: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection\",\n        ],\n        getClones: [\"GET /repos/{owner}/{repo}/traffic/clones\"],\n        getCodeFrequencyStats: [\"GET /repos/{owner}/{repo}/stats/code_frequency\"],\n        getCollaboratorPermissionLevel: [\n            \"GET /repos/{owner}/{repo}/collaborators/{username}/permission\",\n        ],\n        getCombinedStatusForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/status\"],\n        getCommit: [\"GET /repos/{owner}/{repo}/commits/{ref}\"],\n        getCommitActivityStats: [\"GET /repos/{owner}/{repo}/stats/commit_activity\"],\n        getCommitComment: [\"GET /repos/{owner}/{repo}/comments/{comment_id}\"],\n        getCommitSignatureProtection: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n            { mediaType: { previews: [\"zzzax\"] } },\n        ],\n        getCommunityProfileMetrics: [\"GET /repos/{owner}/{repo}/community/profile\"],\n        getContent: [\"GET /repos/{owner}/{repo}/contents/{path}\"],\n        getContributorsStats: [\"GET /repos/{owner}/{repo}/stats/contributors\"],\n        getDeployKey: [\"GET /repos/{owner}/{repo}/keys/{key_id}\"],\n        getDeployment: [\"GET /repos/{owner}/{repo}/deployments/{deployment_id}\"],\n        getDeploymentStatus: [\n            \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}\",\n        ],\n        getEnvironment: [\n            \"GET /repos/{owner}/{repo}/environments/{environment_name}\",\n        ],\n        getLatestPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/latest\"],\n        getLatestRelease: [\"GET /repos/{owner}/{repo}/releases/latest\"],\n        getPages: [\"GET /repos/{owner}/{repo}/pages\"],\n        getPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/{build_id}\"],\n        getParticipationStats: [\"GET /repos/{owner}/{repo}/stats/participation\"],\n        getPullRequestReviewProtection: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n        ],\n        getPunchCardStats: [\"GET /repos/{owner}/{repo}/stats/punch_card\"],\n        getReadme: [\"GET /repos/{owner}/{repo}/readme\"],\n        getReadmeInDirectory: [\"GET /repos/{owner}/{repo}/readme/{dir}\"],\n        getRelease: [\"GET /repos/{owner}/{repo}/releases/{release_id}\"],\n        getReleaseAsset: [\"GET /repos/{owner}/{repo}/releases/assets/{asset_id}\"],\n        getReleaseByTag: [\"GET /repos/{owner}/{repo}/releases/tags/{tag}\"],\n        getStatusChecksProtection: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n        ],\n        getTeamsWithAccessToProtectedBranch: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n        ],\n        getTopPaths: [\"GET /repos/{owner}/{repo}/traffic/popular/paths\"],\n        getTopReferrers: [\"GET /repos/{owner}/{repo}/traffic/popular/referrers\"],\n        getUsersWithAccessToProtectedBranch: [\n            \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n        ],\n        getViews: [\"GET /repos/{owner}/{repo}/traffic/views\"],\n        getWebhook: [\"GET /repos/{owner}/{repo}/hooks/{hook_id}\"],\n        getWebhookConfigForRepo: [\n            \"GET /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n        ],\n        listBranches: [\"GET /repos/{owner}/{repo}/branches\"],\n        listBranchesForHeadCommit: [\n            \"GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head\",\n            { mediaType: { previews: [\"groot\"] } },\n        ],\n        listCollaborators: [\"GET /repos/{owner}/{repo}/collaborators\"],\n        listCommentsForCommit: [\n            \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n        ],\n        listCommitCommentsForRepo: [\"GET /repos/{owner}/{repo}/comments\"],\n        listCommitStatusesForRef: [\n            \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n        ],\n        listCommits: [\"GET /repos/{owner}/{repo}/commits\"],\n        listContributors: [\"GET /repos/{owner}/{repo}/contributors\"],\n        listDeployKeys: [\"GET /repos/{owner}/{repo}/keys\"],\n        listDeploymentStatuses: [\n            \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n        ],\n        listDeployments: [\"GET /repos/{owner}/{repo}/deployments\"],\n        listForAuthenticatedUser: [\"GET /user/repos\"],\n        listForOrg: [\"GET /orgs/{org}/repos\"],\n        listForUser: [\"GET /users/{username}/repos\"],\n        listForks: [\"GET /repos/{owner}/{repo}/forks\"],\n        listInvitations: [\"GET /repos/{owner}/{repo}/invitations\"],\n        listInvitationsForAuthenticatedUser: [\"GET /user/repository_invitations\"],\n        listLanguages: [\"GET /repos/{owner}/{repo}/languages\"],\n        listPagesBuilds: [\"GET /repos/{owner}/{repo}/pages/builds\"],\n        listPublic: [\"GET /repositories\"],\n        listPullRequestsAssociatedWithCommit: [\n            \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n            { mediaType: { previews: [\"groot\"] } },\n        ],\n        listReleaseAssets: [\n            \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n        ],\n        listReleases: [\"GET /repos/{owner}/{repo}/releases\"],\n        listTags: [\"GET /repos/{owner}/{repo}/tags\"],\n        listTeams: [\"GET /repos/{owner}/{repo}/teams\"],\n        listWebhooks: [\"GET /repos/{owner}/{repo}/hooks\"],\n        merge: [\"POST /repos/{owner}/{repo}/merges\"],\n        pingWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/pings\"],\n        removeAppAccessRestrictions: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n            {},\n            { mapToData: \"apps\" },\n        ],\n        removeCollaborator: [\n            \"DELETE /repos/{owner}/{repo}/collaborators/{username}\",\n        ],\n        removeStatusCheckContexts: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n            {},\n            { mapToData: \"contexts\" },\n        ],\n        removeStatusCheckProtection: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n        ],\n        removeTeamAccessRestrictions: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n            {},\n            { mapToData: \"teams\" },\n        ],\n        removeUserAccessRestrictions: [\n            \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n            {},\n            { mapToData: \"users\" },\n        ],\n        renameBranch: [\"POST /repos/{owner}/{repo}/branches/{branch}/rename\"],\n        replaceAllTopics: [\n            \"PUT /repos/{owner}/{repo}/topics\",\n            { mediaType: { previews: [\"mercy\"] } },\n        ],\n        requestPagesBuild: [\"POST /repos/{owner}/{repo}/pages/builds\"],\n        setAdminBranchProtection: [\n            \"POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n        ],\n        setAppAccessRestrictions: [\n            \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n            {},\n            { mapToData: \"apps\" },\n        ],\n        setStatusCheckContexts: [\n            \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n            {},\n            { mapToData: \"contexts\" },\n        ],\n        setTeamAccessRestrictions: [\n            \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n            {},\n            { mapToData: \"teams\" },\n        ],\n        setUserAccessRestrictions: [\n            \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n            {},\n            { mapToData: \"users\" },\n        ],\n        testPushWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/tests\"],\n        transfer: [\"POST /repos/{owner}/{repo}/transfer\"],\n        update: [\"PATCH /repos/{owner}/{repo}\"],\n        updateBranchProtection: [\n            \"PUT /repos/{owner}/{repo}/branches/{branch}/protection\",\n        ],\n        updateCommitComment: [\"PATCH /repos/{owner}/{repo}/comments/{comment_id}\"],\n        updateInformationAboutPagesSite: [\"PUT /repos/{owner}/{repo}/pages\"],\n        updateInvitation: [\n            \"PATCH /repos/{owner}/{repo}/invitations/{invitation_id}\",\n        ],\n        updatePullRequestReviewProtection: [\n            \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n        ],\n        updateRelease: [\"PATCH /repos/{owner}/{repo}/releases/{release_id}\"],\n        updateReleaseAsset: [\n            \"PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n        ],\n        updateStatusCheckPotection: [\n            \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n            {},\n            { renamed: [\"repos\", \"updateStatusCheckProtection\"] },\n        ],\n        updateStatusCheckProtection: [\n            \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n        ],\n        updateWebhook: [\"PATCH /repos/{owner}/{repo}/hooks/{hook_id}\"],\n        updateWebhookConfigForRepo: [\n            \"PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n        ],\n        uploadReleaseAsset: [\n            \"POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}\",\n            { baseUrl: \"https://uploads.github.com\" },\n        ],\n    },\n    search: {\n        code: [\"GET /search/code\"],\n        commits: [\"GET /search/commits\", { mediaType: { previews: [\"cloak\"] } }],\n        issuesAndPullRequests: [\"GET /search/issues\"],\n        labels: [\"GET /search/labels\"],\n        repos: [\"GET /search/repositories\"],\n        topics: [\"GET /search/topics\", { mediaType: { previews: [\"mercy\"] } }],\n        users: [\"GET /search/users\"],\n    },\n    secretScanning: {\n        getAlert: [\n            \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n        ],\n        listAlertsForRepo: [\"GET /repos/{owner}/{repo}/secret-scanning/alerts\"],\n        updateAlert: [\n            \"PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n        ],\n    },\n    teams: {\n        addOrUpdateMembershipForUserInOrg: [\n            \"PUT /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n        ],\n        addOrUpdateProjectPermissionsInOrg: [\n            \"PUT /orgs/{org}/teams/{team_slug}/projects/{project_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        addOrUpdateRepoPermissionsInOrg: [\n            \"PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n        ],\n        checkPermissionsForProjectInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/projects/{project_id}\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        checkPermissionsForRepoInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n        ],\n        create: [\"POST /orgs/{org}/teams\"],\n        createDiscussionCommentInOrg: [\n            \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n        ],\n        createDiscussionInOrg: [\"POST /orgs/{org}/teams/{team_slug}/discussions\"],\n        deleteDiscussionCommentInOrg: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n        ],\n        deleteDiscussionInOrg: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n        ],\n        deleteInOrg: [\"DELETE /orgs/{org}/teams/{team_slug}\"],\n        getByName: [\"GET /orgs/{org}/teams/{team_slug}\"],\n        getDiscussionCommentInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n        ],\n        getDiscussionInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n        ],\n        getMembershipForUserInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n        ],\n        list: [\"GET /orgs/{org}/teams\"],\n        listChildInOrg: [\"GET /orgs/{org}/teams/{team_slug}/teams\"],\n        listDiscussionCommentsInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n        ],\n        listDiscussionsInOrg: [\"GET /orgs/{org}/teams/{team_slug}/discussions\"],\n        listForAuthenticatedUser: [\"GET /user/teams\"],\n        listMembersInOrg: [\"GET /orgs/{org}/teams/{team_slug}/members\"],\n        listPendingInvitationsInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n        ],\n        listProjectsInOrg: [\n            \"GET /orgs/{org}/teams/{team_slug}/projects\",\n            { mediaType: { previews: [\"inertia\"] } },\n        ],\n        listReposInOrg: [\"GET /orgs/{org}/teams/{team_slug}/repos\"],\n        removeMembershipForUserInOrg: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n        ],\n        removeProjectInOrg: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/projects/{project_id}\",\n        ],\n        removeRepoInOrg: [\n            \"DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n        ],\n        updateDiscussionCommentInOrg: [\n            \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n        ],\n        updateDiscussionInOrg: [\n            \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n        ],\n        updateInOrg: [\"PATCH /orgs/{org}/teams/{team_slug}\"],\n    },\n    users: {\n        addEmailForAuthenticated: [\"POST /user/emails\"],\n        block: [\"PUT /user/blocks/{username}\"],\n        checkBlocked: [\"GET /user/blocks/{username}\"],\n        checkFollowingForUser: [\"GET /users/{username}/following/{target_user}\"],\n        checkPersonIsFollowedByAuthenticated: [\"GET /user/following/{username}\"],\n        createGpgKeyForAuthenticated: [\"POST /user/gpg_keys\"],\n        createPublicSshKeyForAuthenticated: [\"POST /user/keys\"],\n        deleteEmailForAuthenticated: [\"DELETE /user/emails\"],\n        deleteGpgKeyForAuthenticated: [\"DELETE /user/gpg_keys/{gpg_key_id}\"],\n        deletePublicSshKeyForAuthenticated: [\"DELETE /user/keys/{key_id}\"],\n        follow: [\"PUT /user/following/{username}\"],\n        getAuthenticated: [\"GET /user\"],\n        getByUsername: [\"GET /users/{username}\"],\n        getContextForUser: [\"GET /users/{username}/hovercard\"],\n        getGpgKeyForAuthenticated: [\"GET /user/gpg_keys/{gpg_key_id}\"],\n        getPublicSshKeyForAuthenticated: [\"GET /user/keys/{key_id}\"],\n        list: [\"GET /users\"],\n        listBlockedByAuthenticated: [\"GET /user/blocks\"],\n        listEmailsForAuthenticated: [\"GET /user/emails\"],\n        listFollowedByAuthenticated: [\"GET /user/following\"],\n        listFollowersForAuthenticatedUser: [\"GET /user/followers\"],\n        listFollowersForUser: [\"GET /users/{username}/followers\"],\n        listFollowingForUser: [\"GET /users/{username}/following\"],\n        listGpgKeysForAuthenticated: [\"GET /user/gpg_keys\"],\n        listGpgKeysForUser: [\"GET /users/{username}/gpg_keys\"],\n        listPublicEmailsForAuthenticated: [\"GET /user/public_emails\"],\n        listPublicKeysForUser: [\"GET /users/{username}/keys\"],\n        listPublicSshKeysForAuthenticated: [\"GET /user/keys\"],\n        setPrimaryEmailVisibilityForAuthenticated: [\"PATCH /user/email/visibility\"],\n        unblock: [\"DELETE /user/blocks/{username}\"],\n        unfollow: [\"DELETE /user/following/{username}\"],\n        updateAuthenticated: [\"PATCH /user\"],\n    },\n};\nexport default Endpoints;\n", "export const VERSION = \"4.15.1\";\n", "export function endpointsToMethods(octokit, endpointsMap) {\n    const newMethods = {};\n    for (const [scope, endpoints] of Object.entries(endpointsMap)) {\n        for (const [methodName, endpoint] of Object.entries(endpoints)) {\n            const [route, defaults, decorations] = endpoint;\n            const [method, url] = route.split(/ /);\n            const endpointDefaults = Object.assign({ method, url }, defaults);\n            if (!newMethods[scope]) {\n                newMethods[scope] = {};\n            }\n            const scopeMethods = newMethods[scope];\n            if (decorations) {\n                scopeMethods[methodName] = decorate(octokit, scope, methodName, endpointDefaults, decorations);\n                continue;\n            }\n            scopeMethods[methodName] = octokit.request.defaults(endpointDefaults);\n        }\n    }\n    return newMethods;\n}\nfunction decorate(octokit, scope, methodName, defaults, decorations) {\n    const requestWithDefaults = octokit.request.defaults(defaults);\n    /* istanbul ignore next */\n    function withDecorations(...args) {\n        // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n        let options = requestWithDefaults.endpoint.merge(...args);\n        // There are currently no other decorations than `.mapToData`\n        if (decorations.mapToData) {\n            options = Object.assign({}, options, {\n                data: options[decorations.mapToData],\n                [decorations.mapToData]: undefined,\n            });\n            return requestWithDefaults(options);\n        }\n        if (decorations.renamed) {\n            const [newScope, newMethodName] = decorations.renamed;\n            octokit.log.warn(`octokit.${scope}.${methodName}() has been renamed to octokit.${newScope}.${newMethodName}()`);\n        }\n        if (decorations.deprecated) {\n            octokit.log.warn(decorations.deprecated);\n        }\n        if (decorations.renamedParameters) {\n            // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n            const options = requestWithDefaults.endpoint.merge(...args);\n            for (const [name, alias] of Object.entries(decorations.renamedParameters)) {\n                if (name in options) {\n                    octokit.log.warn(`\"${name}\" parameter is deprecated for \"octokit.${scope}.${methodName}()\". Use \"${alias}\" instead`);\n                    if (!(alias in options)) {\n                        options[alias] = options[name];\n                    }\n                    delete options[name];\n                }\n            }\n            return requestWithDefaults(options);\n        }\n        // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n        return requestWithDefaults(...args);\n    }\n    return Object.assign(withDecorations, requestWithDefaults);\n}\n", "import ENDPOINTS from \"./generated/endpoints\";\nimport { VERSION } from \"./version\";\nimport { endpointsToMethods } from \"./endpoints-to-methods\";\nexport function restEndpointMethods(octokit) {\n    const api = endpointsToMethods(octokit, ENDPOINTS);\n    return {\n        ...api,\n        rest: api,\n    };\n}\nrestEndpointMethods.VERSION = VERSION;\n"], "names": ["ENDPOINTS"], "mappings": "AAAA,MAAM,SAAS,GAAG;AAClB,IAAI,OAAO,EAAE;AACb,QAAQ,0BAA0B,EAAE;AACpC,YAAY,4EAA4E;AACxF,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,+BAA+B,EAAE;AACzC,YAAY,yFAAyF;AACrG,SAAS;AACT,QAAQ,uBAAuB,EAAE,CAAC,+CAA+C,CAAC;AAClF,QAAQ,wBAAwB,EAAE;AAClC,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,+DAA+D;AAC3E,SAAS;AACT,QAAQ,uBAAuB,EAAE,CAAC,+CAA+C,CAAC;AAClF,QAAQ,wBAAwB,EAAE;AAClC,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,sBAAsB,EAAE;AAChC,YAAY,uEAAuE;AACnF,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,8DAA8D;AAC1E,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,4FAA4F;AACxG,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,kDAAkD,CAAC;AAC7E,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,gDAAgD;AAC5D,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,oDAAoD,CAAC;AACjF,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,kDAAkD,EAAE;AAC5D,YAAY,qEAAqE;AACjF,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,4EAA4E;AACxF,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,iDAAiD,EAAE;AAC3D,YAAY,kEAAkE;AAC9E,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,kEAAkE;AAC9E,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,2DAA2D,CAAC;AAClF,QAAQ,uBAAuB,EAAE;AACjC,YAAY,sFAAsF;AAClG,SAAS;AACT,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,yFAAyF;AACrG,SAAS;AACT,QAAQ,uCAAuC,EAAE;AACjD,YAAY,qCAAqC;AACjD,SAAS;AACT,QAAQ,qCAAqC,EAAE;AAC/C,YAAY,+CAA+C;AAC3D,SAAS;AACT,QAAQ,oBAAoB,EAAE,CAAC,iDAAiD,CAAC;AACjF,QAAQ,eAAe,EAAE,CAAC,4CAA4C,CAAC;AACvE,QAAQ,YAAY,EAAE,CAAC,+CAA+C,CAAC;AACvE,QAAQ,2BAA2B,EAAE;AACrC,YAAY,qEAAqE;AACjF,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,+CAA+C;AAC3D,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,uCAAuC,CAAC,EAAE;AAC7E,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,sDAAsD,CAAC;AAClF,QAAQ,aAAa,EAAE,CAAC,yDAAyD,CAAC;AAClF,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,yBAAyB,EAAE,CAAC,6CAA6C,CAAC;AAClF,QAAQ,0BAA0B,EAAE;AACpC,YAAY,uDAAuD;AACnE,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,2DAA2D,CAAC;AAClF,QAAQ,cAAc,EAAE,CAAC,iDAAiD,CAAC;AAC3E,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,kEAAkE;AAC9E,SAAS;AACT,QAAQ,oBAAoB,EAAE,CAAC,6CAA6C,CAAC;AAC7E,QAAQ,sBAAsB,EAAE;AAChC,YAAY,2EAA2E;AACvF,SAAS;AACT,QAAQ,sBAAsB,EAAE;AAChC,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,iCAAiC,CAAC;AAC3D,QAAQ,eAAe,EAAE,CAAC,2CAA2C,CAAC;AACtE,QAAQ,iBAAiB,EAAE,CAAC,6CAA6C,CAAC;AAC1E,QAAQ,4BAA4B,EAAE,CAAC,2CAA2C,CAAC;AACnF,QAAQ,6BAA6B,EAAE;AACvC,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,wDAAwD,EAAE;AAClE,YAAY,kDAAkD;AAC9D,SAAS;AACT,QAAQ,2BAA2B,EAAE,CAAC,iCAAiC,CAAC;AACxE,QAAQ,4BAA4B,EAAE,CAAC,2CAA2C,CAAC;AACnF,QAAQ,wBAAwB,EAAE;AAClC,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,uBAAuB,EAAE,CAAC,wCAAwC,CAAC;AAC3E,QAAQ,aAAa,EAAE,CAAC,wDAAwD,CAAC;AACjF,QAAQ,+BAA+B,EAAE;AACzC,YAAY,+EAA+E;AAC3F,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,sEAAsE;AAClF,SAAS;AACT,QAAQ,6BAA6B,EAAE;AACvC,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,uCAAuC,EAAE;AACjD,YAAY,qCAAqC;AACjD,SAAS;AACT,QAAQ,qCAAqC,EAAE;AAC/C,YAAY,+CAA+C;AAC3D,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,uDAAuD,EAAE;AACjE,YAAY,kDAAkD;AAC9D,SAAS;AACT,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,qCAAqC,EAAE,CAAC,kCAAkC,CAAC;AACnF,QAAQ,sBAAsB,EAAE,CAAC,2CAA2C,CAAC;AAC7E,QAAQ,wBAAwB,EAAE;AAClC,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,YAAY,CAAC;AAChC,QAAQ,mBAAmB,EAAE,CAAC,wCAAwC,CAAC;AACvE,QAAQ,SAAS,EAAE,CAAC,wCAAwC,CAAC;AAC7D,QAAQ,yCAAyC,EAAE;AACnD,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,8BAA8B,EAAE,CAAC,8BAA8B,CAAC;AACxE,QAAQ,qCAAqC,EAAE,CAAC,oBAAoB,CAAC;AACrE,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,yCAAyC;AACrD,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,aAAa,CAAC;AACzC,QAAQ,8BAA8B,EAAE,CAAC,qCAAqC,CAAC;AAC/E,QAAQ,uBAAuB,EAAE,CAAC,qCAAqC,CAAC;AACxE,QAAQ,mBAAmB,EAAE,CAAC,wBAAwB,CAAC;AACvD,QAAQ,yBAAyB,EAAE,CAAC,uCAAuC,CAAC;AAC5E,QAAQ,+BAA+B,EAAE;AACzC,YAAY,8CAA8C;AAC1D,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,kCAAkC,CAAC;AAC5D,QAAQ,yCAAyC,EAAE;AACnD,YAAY,yCAAyC;AACrD,SAAS;AACT,QAAQ,mCAAmC,EAAE,CAAC,mBAAmB,CAAC;AAClE,QAAQ,sBAAsB,EAAE,CAAC,+BAA+B,CAAC;AACjE,QAAQ,sBAAsB,EAAE,CAAC,qCAAqC,CAAC;AACvE,QAAQ,qBAAqB,EAAE,CAAC,sCAAsC,CAAC;AACvE,QAAQ,oCAAoC,EAAE,CAAC,yBAAyB,CAAC;AACzE,QAAQ,mBAAmB,EAAE,CAAC,uCAAuC,CAAC;AACtE,QAAQ,uBAAuB,EAAE,CAAC,oBAAoB,CAAC;AACvD,QAAQ,2BAA2B,EAAE,CAAC,yCAAyC,CAAC;AAChF,QAAQ,gBAAgB,EAAE,CAAC,0CAA0C,CAAC;AACtE,QAAQ,mBAAmB,EAAE,CAAC,wCAAwC,CAAC;AACvE,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,4BAA4B,EAAE,CAAC,kCAAkC,CAAC;AAC1E,QAAQ,8BAA8B,EAAE,CAAC,qCAAqC,CAAC;AAC/E,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,wEAAwE;AACpF,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC,sCAAsC,CAAC;AAC5D,QAAQ,uBAAuB,EAAE;AACjC,YAAY,6DAA6D;AACzE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,wCAAwC,CAAC;AACtE,QAAQ,6BAA6B,EAAE;AACvC,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,wCAAwC,CAAC;AACvE,QAAQ,kBAAkB,EAAE,CAAC,6CAA6C,CAAC;AAC3E,QAAQ,WAAW,EAAE,CAAC,wCAAwC,CAAC;AAC/D,QAAQ,gBAAgB,EAAE,CAAC,UAAU,CAAC;AACtC,QAAQ,SAAS,EAAE,CAAC,sBAAsB,CAAC;AAC3C,QAAQ,eAAe,EAAE,CAAC,0CAA0C,CAAC;AACrE,QAAQ,kBAAkB,EAAE,CAAC,8BAA8B,CAAC;AAC5D,QAAQ,mBAAmB,EAAE,CAAC,wCAAwC,CAAC;AACvE,QAAQ,6BAA6B,EAAE;AACvC,YAAY,gDAAgD;AAC5D,SAAS;AACT,QAAQ,oCAAoC,EAAE;AAC9C,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,oCAAoC,CAAC;AACnE,QAAQ,sBAAsB,EAAE,CAAC,sBAAsB,CAAC;AACxD,QAAQ,mBAAmB,EAAE,CAAC,mDAAmD,CAAC;AAClF,QAAQ,0BAA0B,EAAE;AACpC,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,yCAAyC,EAAE;AACnD,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,wBAAwB,CAAC;AACrD,QAAQ,qCAAqC,EAAE,CAAC,yBAAyB,CAAC;AAC1E,QAAQ,SAAS,EAAE,CAAC,gCAAgC,CAAC;AACrD,QAAQ,gBAAgB,EAAE,CAAC,wCAAwC,CAAC;AACpE,QAAQ,iCAAiC,EAAE,CAAC,gCAAgC,CAAC;AAC7E,QAAQ,qCAAqC,EAAE,CAAC,iCAAiC,CAAC;AAClF,QAAQ,4CAA4C,EAAE;AACtD,YAAY,yCAAyC;AACrD,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,2EAA2E;AACvF,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC,uCAAuC,CAAC;AAC7D,QAAQ,6BAA6B,EAAE,CAAC,4BAA4B,CAAC;AACrE,QAAQ,UAAU,EAAE,CAAC,6CAA6C,CAAC;AACnE,QAAQ,mBAAmB,EAAE,CAAC,oDAAoD,CAAC;AACnF,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,uDAAuD;AACnE,SAAS;AACT,QAAQ,yBAAyB,EAAE,CAAC,wBAAwB,CAAC;AAC7D,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,0BAA0B,EAAE,CAAC,0CAA0C,CAAC;AAChF,QAAQ,2BAA2B,EAAE;AACrC,YAAY,gDAAgD;AAC5D,SAAS;AACT,QAAQ,2BAA2B,EAAE,CAAC,2CAA2C,CAAC;AAClF,QAAQ,4BAA4B,EAAE;AACtC,YAAY,iDAAiD;AAC7D,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,iDAAiD;AAC7D,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,uDAAuD;AACnE,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,MAAM,EAAE,CAAC,uCAAuC,CAAC;AACzD,QAAQ,WAAW,EAAE,CAAC,yCAAyC,CAAC;AAChE,QAAQ,GAAG,EAAE,CAAC,qDAAqD,CAAC;AACpE,QAAQ,QAAQ,EAAE,CAAC,yDAAyD,CAAC;AAC7E,QAAQ,eAAe,EAAE;AACzB,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC,oDAAoD,CAAC;AAC1E,QAAQ,YAAY,EAAE;AACtB,YAAY,oEAAoE;AAChF,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,sDAAsD,CAAC;AAClF,QAAQ,cAAc,EAAE;AACxB,YAAY,oEAAoE;AAChF,SAAS;AACT,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,sDAAsD;AAClE,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,uDAAuD,CAAC;AACzE,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,cAAc,EAAE;AACxB,YAAY,oFAAoF;AAChG,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,YAAY,+DAA+D;AAC3E,YAAY,EAAE;AACd,YAAY,EAAE,iBAAiB,EAAE,EAAE,QAAQ,EAAE,cAAc,EAAE,EAAE;AAC/D,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,2DAA2D,CAAC;AAC/E,QAAQ,iBAAiB,EAAE,CAAC,gDAAgD,CAAC;AAC7E,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,yEAAyE;AACrF,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,kDAAkD,CAAC;AAChF,QAAQ,WAAW,EAAE;AACrB,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,iDAAiD,CAAC;AACxE,KAAK;AACL,IAAI,cAAc,EAAE;AACpB,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,uBAAuB;AACnC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,6BAA6B;AACzC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,qDAAqD;AACjE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,EAAE;AACpC,IAAI,eAAe,EAAE;AACrB,QAAQ,kDAAkD,EAAE;AAC5D,YAAY,6EAA6E;AACzF,SAAS;AACT,QAAQ,iDAAiD,EAAE;AAC3D,YAAY,0EAA0E;AACtF,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,oEAAoE;AAChF,SAAS;AACT,QAAQ,qCAAqC,EAAE;AAC/C,YAAY,mDAAmD;AAC/D,SAAS;AACT,QAAQ,uDAAuD,EAAE;AACjE,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,oEAAoE;AAChF,SAAS;AACT,QAAQ,qCAAqC,EAAE;AAC/C,YAAY,mDAAmD;AAC/D,SAAS;AACT,QAAQ,sDAAsD,EAAE;AAChE,YAAY,iEAAiE;AAC7E,SAAS;AACT,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,cAAc,EAAE,CAAC,2BAA2B,CAAC;AACrD,QAAQ,MAAM,EAAE,CAAC,aAAa,CAAC;AAC/B,QAAQ,aAAa,EAAE,CAAC,gCAAgC,CAAC;AACzD,QAAQ,MAAM,EAAE,CAAC,yBAAyB,CAAC;AAC3C,QAAQ,aAAa,EAAE,CAAC,+CAA+C,CAAC;AACxE,QAAQ,IAAI,EAAE,CAAC,6BAA6B,CAAC;AAC7C,QAAQ,GAAG,EAAE,CAAC,sBAAsB,CAAC;AACrC,QAAQ,UAAU,EAAE,CAAC,4CAA4C,CAAC;AAClE,QAAQ,WAAW,EAAE,CAAC,4BAA4B,CAAC;AACnD,QAAQ,IAAI,EAAE,CAAC,YAAY,CAAC;AAC5B,QAAQ,YAAY,EAAE,CAAC,+BAA+B,CAAC;AACvD,QAAQ,WAAW,EAAE,CAAC,8BAA8B,CAAC;AACrD,QAAQ,WAAW,EAAE,CAAC,6BAA6B,CAAC;AACpD,QAAQ,SAAS,EAAE,CAAC,4BAA4B,CAAC;AACjD,QAAQ,UAAU,EAAE,CAAC,mBAAmB,CAAC;AACzC,QAAQ,WAAW,EAAE,CAAC,oBAAoB,CAAC;AAC3C,QAAQ,IAAI,EAAE,CAAC,2BAA2B,CAAC;AAC3C,QAAQ,MAAM,EAAE,CAAC,8BAA8B,CAAC;AAChD,QAAQ,MAAM,EAAE,CAAC,wBAAwB,CAAC;AAC1C,QAAQ,aAAa,EAAE,CAAC,8CAA8C,CAAC;AACvE,KAAK;AACL,IAAI,GAAG,EAAE;AACT,QAAQ,UAAU,EAAE,CAAC,sCAAsC,CAAC;AAC5D,QAAQ,YAAY,EAAE,CAAC,wCAAwC,CAAC;AAChE,QAAQ,SAAS,EAAE,CAAC,qCAAqC,CAAC;AAC1D,QAAQ,SAAS,EAAE,CAAC,qCAAqC,CAAC;AAC1D,QAAQ,UAAU,EAAE,CAAC,sCAAsC,CAAC;AAC5D,QAAQ,SAAS,EAAE,CAAC,6CAA6C,CAAC;AAClE,QAAQ,OAAO,EAAE,CAAC,gDAAgD,CAAC;AACnE,QAAQ,SAAS,EAAE,CAAC,oDAAoD,CAAC;AACzE,QAAQ,MAAM,EAAE,CAAC,yCAAyC,CAAC;AAC3D,QAAQ,MAAM,EAAE,CAAC,8CAA8C,CAAC;AAChE,QAAQ,OAAO,EAAE,CAAC,gDAAgD,CAAC;AACnE,QAAQ,gBAAgB,EAAE,CAAC,mDAAmD,CAAC;AAC/E,QAAQ,SAAS,EAAE,CAAC,4CAA4C,CAAC;AACjE,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,eAAe,EAAE,CAAC,0BAA0B,CAAC;AACrD,QAAQ,WAAW,EAAE,CAAC,iCAAiC,CAAC;AACxD,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,QAAQ,mCAAmC,EAAE,CAAC,8BAA8B,CAAC;AAC7E,QAAQ,qBAAqB,EAAE,CAAC,oCAAoC,CAAC;AACrE,QAAQ,sBAAsB,EAAE,CAAC,8CAA8C,CAAC;AAChF,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,8BAA8B;AAC1C,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,qCAAqC,CAAC,EAAE;AAChF,SAAS;AACT,QAAQ,sCAAsC,EAAE,CAAC,iCAAiC,CAAC;AACnF,QAAQ,wBAAwB,EAAE,CAAC,uCAAuC,CAAC;AAC3E,QAAQ,yBAAyB,EAAE;AACnC,YAAY,iDAAiD;AAC7D,SAAS;AACT,QAAQ,oCAAoC,EAAE;AAC9C,YAAY,iCAAiC;AAC7C,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,wCAAwC,CAAC,EAAE;AACnF,SAAS;AACT,QAAQ,mCAAmC,EAAE,CAAC,8BAA8B,CAAC;AAC7E,QAAQ,qBAAqB,EAAE,CAAC,oCAAoC,CAAC;AACrE,QAAQ,sBAAsB,EAAE,CAAC,8CAA8C,CAAC;AAChF,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,8BAA8B;AAC1C,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,qCAAqC,CAAC,EAAE;AAChF,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,YAAY,EAAE;AACtB,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,yDAAyD,CAAC;AAC9E,QAAQ,sBAAsB,EAAE,CAAC,gDAAgD,CAAC;AAClF,QAAQ,MAAM,EAAE,CAAC,mCAAmC,CAAC;AACrD,QAAQ,aAAa,EAAE;AACvB,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,mCAAmC,CAAC;AAC1D,QAAQ,eAAe,EAAE,CAAC,uCAAuC,CAAC;AAClE,QAAQ,aAAa,EAAE;AACvB,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,4CAA4C,CAAC;AACnE,QAAQ,eAAe,EAAE;AACzB,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,GAAG,EAAE,CAAC,iDAAiD,CAAC;AAChE,QAAQ,UAAU,EAAE,CAAC,wDAAwD,CAAC;AAC9E,QAAQ,QAAQ,EAAE,CAAC,oDAAoD,CAAC;AACxE,QAAQ,QAAQ,EAAE,CAAC,yCAAyC,CAAC;AAC7D,QAAQ,YAAY,EAAE,CAAC,yDAAyD,CAAC;AACjF,QAAQ,IAAI,EAAE,CAAC,aAAa,CAAC;AAC7B,QAAQ,aAAa,EAAE,CAAC,qCAAqC,CAAC;AAC9D,QAAQ,YAAY,EAAE,CAAC,0DAA0D,CAAC;AAClF,QAAQ,mBAAmB,EAAE,CAAC,2CAA2C,CAAC;AAC1E,QAAQ,UAAU,EAAE,CAAC,wDAAwD,CAAC;AAC9E,QAAQ,iBAAiB,EAAE,CAAC,yCAAyC,CAAC;AACtE,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,0DAA0D;AACtE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE;AACxD,SAAS;AACT,QAAQ,wBAAwB,EAAE,CAAC,kBAAkB,CAAC;AACtD,QAAQ,UAAU,EAAE,CAAC,wBAAwB,CAAC;AAC9C,QAAQ,WAAW,EAAE,CAAC,kCAAkC,CAAC;AACzD,QAAQ,sBAAsB,EAAE;AAChC,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,kCAAkC,CAAC;AAC/D,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,sCAAsC,CAAC;AAChE,QAAQ,IAAI,EAAE,CAAC,sDAAsD,CAAC;AACtE,QAAQ,eAAe,EAAE;AACzB,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,8DAA8D;AAC1E,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,kEAAkE;AAC9E,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,wDAAwD,CAAC;AAC7E,QAAQ,MAAM,EAAE,CAAC,yDAAyD,CAAC;AAC3E,QAAQ,MAAM,EAAE,CAAC,mDAAmD,CAAC;AACrE,QAAQ,aAAa,EAAE,CAAC,0DAA0D,CAAC;AACnF,QAAQ,WAAW,EAAE,CAAC,2CAA2C,CAAC;AAClE,QAAQ,eAAe,EAAE;AACzB,YAAY,2DAA2D;AACvE,SAAS;AACT,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,GAAG,EAAE,CAAC,yBAAyB,CAAC;AACxC,QAAQ,kBAAkB,EAAE,CAAC,eAAe,CAAC;AAC7C,QAAQ,UAAU,EAAE,CAAC,mCAAmC,CAAC;AACzD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,MAAM,EAAE,CAAC,gBAAgB,CAAC;AAClC,QAAQ,SAAS,EAAE;AACnB,YAAY,oBAAoB;AAChC,YAAY,EAAE,OAAO,EAAE,EAAE,cAAc,EAAE,2BAA2B,EAAE,EAAE;AACxE,SAAS;AACT,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC;AAC1B,QAAQ,UAAU,EAAE,CAAC,cAAc,CAAC;AACpC,QAAQ,MAAM,EAAE,CAAC,UAAU,CAAC;AAC5B,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,QAAQ,YAAY,EAAE,CAAC,qCAAqC,CAAC;AAC7D,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,gDAAgD;AAC5D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,sDAAsD;AAClE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,mDAAmD;AAC/D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,6CAA6C;AACzD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,0CAA0C,CAAC;AACtE,QAAQ,eAAe,EAAE,CAAC,kCAAkC,CAAC;AAC7D,QAAQ,aAAa,EAAE,CAAC,8CAA8C,CAAC;AACvE,QAAQ,6BAA6B,EAAE;AACvC,YAAY,qCAAqC;AACjD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,2CAA2C;AACvD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,wBAAwB,EAAE;AAClC,YAAY,sBAAsB;AAClC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,4BAA4B;AACxC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,wDAAwD;AACpE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,kDAAkD;AAC9D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,wDAAwD,CAAC;AACnF,QAAQ,gBAAgB,EAAE,CAAC,wCAAwC,CAAC;AACpE,QAAQ,yBAAyB,EAAE,CAAC,uBAAuB,CAAC;AAC5D,QAAQ,WAAW,EAAE,CAAC,6BAA6B,CAAC;AACpD,QAAQ,WAAW,EAAE,CAAC,kCAAkC,CAAC;AACzD,QAAQ,8BAA8B,EAAE;AACxC,YAAY,+DAA+D;AAC3E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,qEAAqE;AACjF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE;AACtD,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,oCAAoC,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,EAAE;AACV,QAAQ,SAAS,EAAE,CAAC,mCAAmC,CAAC;AACxD,QAAQ,gBAAgB,EAAE,CAAC,gDAAgD,CAAC;AAC5E,QAAQ,gBAAgB,EAAE,CAAC,mCAAmC,CAAC;AAC/D,QAAQ,sBAAsB,EAAE,CAAC,oCAAoC,CAAC;AACtE,QAAQ,4BAA4B,EAAE,CAAC,2CAA2C,CAAC;AACnF,QAAQ,kCAAkC,EAAE;AAC5C,YAAY,kDAAkD;AAC9D,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,8BAA8B,CAAC;AAC1D,QAAQ,aAAa,EAAE,CAAC,wBAAwB,CAAC;AACjD,QAAQ,aAAa,EAAE,CAAC,oCAAoC,CAAC;AAC7D,QAAQ,GAAG,EAAE,CAAC,iBAAiB,CAAC;AAChC,QAAQ,iCAAiC,EAAE,CAAC,kCAAkC,CAAC;AAC/E,QAAQ,oBAAoB,EAAE,CAAC,wCAAwC,CAAC;AACxE,QAAQ,UAAU,EAAE,CAAC,iCAAiC,CAAC;AACvD,QAAQ,sBAAsB,EAAE,CAAC,wCAAwC,CAAC;AAC1E,QAAQ,IAAI,EAAE,CAAC,oBAAoB,CAAC;AACpC,QAAQ,oBAAoB,EAAE,CAAC,+BAA+B,CAAC;AAC/D,QAAQ,gBAAgB,EAAE,CAAC,wBAAwB,CAAC;AACpD,QAAQ,qBAAqB,EAAE,CAAC,oCAAoC,CAAC;AACrE,QAAQ,wBAAwB,EAAE,CAAC,gBAAgB,CAAC;AACpD,QAAQ,WAAW,EAAE,CAAC,4BAA4B,CAAC;AACnD,QAAQ,mBAAmB,EAAE,CAAC,mDAAmD,CAAC;AAClF,QAAQ,WAAW,EAAE,CAAC,yBAAyB,CAAC;AAChD,QAAQ,mCAAmC,EAAE,CAAC,4BAA4B,CAAC;AAC3E,QAAQ,wBAAwB,EAAE,CAAC,uCAAuC,CAAC;AAC3E,QAAQ,sBAAsB,EAAE,CAAC,6BAA6B,CAAC;AAC/D,QAAQ,iBAAiB,EAAE,CAAC,gCAAgC,CAAC;AAC7D,QAAQ,YAAY,EAAE,CAAC,uBAAuB,CAAC;AAC/C,QAAQ,WAAW,EAAE,CAAC,wCAAwC,CAAC;AAC/D,QAAQ,YAAY,EAAE,CAAC,uCAAuC,CAAC;AAC/D,QAAQ,uBAAuB,EAAE,CAAC,2CAA2C,CAAC;AAC9E,QAAQ,yBAAyB,EAAE;AACnC,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,0CAA0C,EAAE;AACpD,YAAY,8CAA8C;AAC1D,SAAS;AACT,QAAQ,oBAAoB,EAAE,CAAC,wCAAwC,CAAC;AACxE,QAAQ,uCAAuC,EAAE;AACjD,YAAY,2CAA2C;AACvD,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,sCAAsC,CAAC;AAC7D,QAAQ,MAAM,EAAE,CAAC,mBAAmB,CAAC;AACrC,QAAQ,oCAAoC,EAAE;AAC9C,YAAY,oCAAoC;AAChD,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,mCAAmC,CAAC;AAC5D,QAAQ,yBAAyB,EAAE,CAAC,0CAA0C,CAAC;AAC/E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,qDAAqD;AACjE,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,wCAAwC,EAAE;AAClD,YAAY,mFAAmF;AAC/F,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,yFAAyF;AACrG,SAAS;AACT,QAAQ,4CAA4C,EAAE;AACtD,YAAY,iEAAiE;AAC7E,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,UAAU,EAAE,2CAA2C,CAAC,EAAE;AAClF,SAAS;AACT,QAAQ,2DAA2D,EAAE;AACrE,YAAY,2DAA2D;AACvE,YAAY,EAAE;AACd,YAAY;AACZ,gBAAgB,OAAO,EAAE;AACzB,oBAAoB,UAAU;AAC9B,oBAAoB,yDAAyD;AAC7E,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,uDAAuD,EAAE;AACjE,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,yCAAyC,EAAE;AACnD,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,0CAA0C,EAAE;AACpD,YAAY,uEAAuE;AACnF,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,kDAAkD;AAC9D,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,8DAA8D;AAC1E,SAAS;AACT,QAAQ,qCAAqC,EAAE;AAC/C,YAAY,gFAAgF;AAC5F,SAAS;AACT,QAAQ,gCAAgC,EAAE;AAC1C,YAAY,sFAAsF;AAClG,SAAS;AACT,QAAQ,wBAAwB,EAAE;AAClC,YAAY,4FAA4F;AACxG,SAAS;AACT,QAAQ,kCAAkC,EAAE;AAC5C,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,yEAAyE;AACrF,SAAS;AACT,QAAQ,yCAAyC,EAAE;AACnD,YAAY,yFAAyF;AACrG,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,+FAA+F;AAC3G,SAAS;AACT,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,QAAQ,eAAe,EAAE;AACzB,YAAY,qDAAqD;AACjE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,0CAA0C;AACtD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,qCAAqC;AACjD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,qBAAqB;AACjC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,2BAA2B;AACvC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,aAAa,EAAE;AACvB,YAAY,qCAAqC;AACjD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,YAAY,+BAA+B;AAC3C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,0CAA0C;AACtD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,sCAAsC;AAClD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,GAAG,EAAE;AACb,YAAY,4BAA4B;AACxC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,YAAY,uCAAuC;AACnD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,YAAY,mCAAmC;AAC/C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,gEAAgE;AAC5E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,YAAY,yCAAyC;AACrD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,0CAA0C;AACtD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,oCAAoC;AAChD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,0BAA0B;AACtC,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,oCAAoC;AAChD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,YAAY,gCAAgC;AAC5C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,YAAY,8CAA8C;AAC1D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,0CAA0C;AACtD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,wDAAwD;AACpE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,YAAY,8BAA8B;AAC1C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,YAAY,yCAAyC;AACrD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,qCAAqC;AACjD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,aAAa,EAAE,CAAC,qDAAqD,CAAC;AAC9E,QAAQ,MAAM,EAAE,CAAC,kCAAkC,CAAC;AACpD,QAAQ,2BAA2B,EAAE;AACrC,YAAY,8EAA8E;AAC1F,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,wDAAwD,CAAC;AAChF,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,sEAAsE;AAClF,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,aAAa,EAAE;AACvB,YAAY,8EAA8E;AAC1F,SAAS;AACT,QAAQ,GAAG,EAAE,CAAC,+CAA+C,CAAC;AAC9D,QAAQ,SAAS,EAAE;AACnB,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,gBAAgB,EAAE,CAAC,uDAAuD,CAAC;AACnF,QAAQ,IAAI,EAAE,CAAC,iCAAiC,CAAC;AACjD,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,4EAA4E;AACxF,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,uDAAuD,CAAC;AAC9E,QAAQ,SAAS,EAAE,CAAC,qDAAqD,CAAC;AAC1E,QAAQ,sBAAsB,EAAE;AAChC,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,yBAAyB,EAAE,CAAC,0CAA0C,CAAC;AAC/E,QAAQ,WAAW,EAAE,CAAC,uDAAuD,CAAC;AAC9E,QAAQ,KAAK,EAAE,CAAC,qDAAqD,CAAC;AACtE,QAAQ,wBAAwB,EAAE;AAClC,YAAY,sEAAsE;AAClF,SAAS;AACT,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,oEAAoE;AAChF,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,2EAA2E;AACvF,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,iDAAiD,CAAC;AACnE,QAAQ,YAAY,EAAE;AACtB,YAAY,6DAA6D;AACzE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,yDAAyD;AACrE,SAAS;AACT,KAAK;AACL,IAAI,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,EAAE;AAC3C,IAAI,SAAS,EAAE;AACf,QAAQ,sBAAsB,EAAE;AAChC,YAAY,4DAA4D;AACxE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,4DAA4D;AACxE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,mEAAmE;AAC/E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,kEAAkE;AAC9E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,mCAAmC,EAAE;AAC7C,YAAY,wGAAwG;AACpH,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,8EAA8E;AAC1F,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,sBAAsB,EAAE;AAChC,YAAY,4EAA4E;AACxF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,4EAA4E;AACxF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,mFAAmF;AAC/F,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,kFAAkF;AAC9F,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,uBAAuB,EAAE;AACjC,YAAY,8FAA8F;AAC1G,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,8BAA8B,EAAE;AACxC,YAAY,wHAAwH;AACpI,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,iCAAiC;AAC7C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,YAAY;AACZ,gBAAgB,UAAU,EAAE,qIAAqI;AACjK,aAAa;AACb,SAAS;AACT,QAAQ,oBAAoB,EAAE;AAC9B,YAAY,2DAA2D;AACvE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,2DAA2D;AACvE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,kEAAkE;AAC9E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,+BAA+B,EAAE;AACzC,YAAY,iEAAiE;AAC7E,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,uGAAuG;AACnH,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,6EAA6E;AACzF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE;AAC1D,SAAS;AACT,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,gBAAgB,EAAE,CAAC,oDAAoD,CAAC;AAChF,QAAQ,wBAAwB,EAAE;AAClC,YAAY,2EAA2E;AACvF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE;AACjC,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,oDAAoD,CAAC;AAC/E,QAAQ,sBAAsB,EAAE;AAChC,YAAY,yFAAyF;AACrG,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE;AACrC,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,4EAA4E;AACxF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,4EAA4E;AACxF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,oDAAoD,CAAC;AACjF,QAAQ,wBAAwB,EAAE;AAClC,YAAY,gDAAgD;AAC5D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,mDAAmD,CAAC;AAC7E,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,+BAA+B,EAAE;AACzC,YAAY,6EAA6E;AACzF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,2CAA2C,CAAC;AACzE,QAAQ,eAAe,EAAE,CAAC,iCAAiC,CAAC;AAC5D,QAAQ,gBAAgB,EAAE,CAAC,wCAAwC,CAAC;AACpE,QAAQ,sBAAsB,EAAE;AAChC,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,uCAAuC,CAAC;AACtE,QAAQ,0BAA0B,EAAE,CAAC,kBAAkB,CAAC;AACxD,QAAQ,UAAU,EAAE,CAAC,kCAAkC,CAAC;AACxD,QAAQ,WAAW,EAAE,CAAC,wBAAwB,CAAC;AAC/C,QAAQ,yBAAyB,EAAE;AACnC,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,0BAA0B,EAAE,CAAC,2CAA2C,CAAC;AACjF,QAAQ,eAAe,EAAE;AACzB,YAAY,kCAAkC;AAC9C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE;AACvD,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,qCAAqC,CAAC;AAC9D,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,uDAAuD;AACnE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE;AACrD,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,kCAAkC,CAAC;AAC3D,QAAQ,iBAAiB,EAAE,CAAC,qDAAqD,CAAC;AAClF,QAAQ,MAAM,EAAE,CAAC,8BAA8B,CAAC;AAChD,QAAQ,wBAAwB,EAAE;AAClC,YAAY,wEAAwE;AACpF,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,0EAA0E;AACtF,SAAS;AACT,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,8DAA8D;AAC1E,SAAS;AACT,QAAQ,sBAAsB,EAAE;AAChC,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,oDAAoD,CAAC;AACnF,QAAQ,+BAA+B,EAAE;AACzC,YAAY,+EAA+E;AAC3F,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,4CAA4C,CAAC;AACvE,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,UAAU,EAAE,CAAC,8CAA8C,CAAC;AACpE,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,oCAAoC;AAChD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE;AACvD,SAAS;AACT,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,yFAAyF;AACrG,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,oDAAoD,CAAC;AAC7E,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,8CAA8C,CAAC;AACvE,QAAQ,6BAA6B,EAAE;AACvC,YAAY,uDAAuD;AACnE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,mDAAmD;AAC/D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,yCAAyC;AACrD,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,wBAAwB,CAAC,EAAE;AAC5D,SAAS;AACT,QAAQ,sBAAsB,EAAE,CAAC,yCAAyC,CAAC;AAC3E,QAAQ,sBAAsB,EAAE,CAAC,yCAAyC,CAAC;AAC3E,QAAQ,4BAA4B,EAAE;AACtC,YAAY,oDAAoD;AAChE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,gDAAgD;AAC5D,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnD,SAAS;AACT,QAAQ,GAAG,EAAE,CAAC,2BAA2B,CAAC;AAC1C,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,qEAAqE;AACjF,SAAS;AACT,QAAQ,wBAAwB,EAAE;AAClC,YAAY,uEAAuE;AACnF,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,wCAAwC,CAAC;AACtE,QAAQ,yBAAyB,EAAE;AACnC,YAAY,wFAAwF;AACpG,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,YAAY,kCAAkC;AAC9C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,kCAAkC,EAAE;AAC5C,YAAY,0EAA0E;AACtF,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,6CAA6C,CAAC;AAClE,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,0CAA0C,CAAC;AAC/D,QAAQ,qBAAqB,EAAE,CAAC,gDAAgD,CAAC;AACjF,QAAQ,8BAA8B,EAAE;AACxC,YAAY,+DAA+D;AAC3E,SAAS;AACT,QAAQ,uBAAuB,EAAE,CAAC,gDAAgD,CAAC;AACnF,QAAQ,SAAS,EAAE,CAAC,yCAAyC,CAAC;AAC9D,QAAQ,sBAAsB,EAAE,CAAC,iDAAiD,CAAC;AACnF,QAAQ,gBAAgB,EAAE,CAAC,iDAAiD,CAAC;AAC7E,QAAQ,4BAA4B,EAAE;AACtC,YAAY,4EAA4E;AACxF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,0BAA0B,EAAE,CAAC,6CAA6C,CAAC;AACnF,QAAQ,UAAU,EAAE,CAAC,2CAA2C,CAAC;AACjE,QAAQ,oBAAoB,EAAE,CAAC,8CAA8C,CAAC;AAC9E,QAAQ,YAAY,EAAE,CAAC,yCAAyC,CAAC;AACjE,QAAQ,aAAa,EAAE,CAAC,uDAAuD,CAAC;AAChF,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,4EAA4E;AACxF,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,+CAA+C,CAAC;AAC9E,QAAQ,gBAAgB,EAAE,CAAC,2CAA2C,CAAC;AACvE,QAAQ,QAAQ,EAAE,CAAC,iCAAiC,CAAC;AACrD,QAAQ,aAAa,EAAE,CAAC,mDAAmD,CAAC;AAC5E,QAAQ,qBAAqB,EAAE,CAAC,+CAA+C,CAAC;AAChF,QAAQ,8BAA8B,EAAE;AACxC,YAAY,sFAAsF;AAClG,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,4CAA4C,CAAC;AACzE,QAAQ,SAAS,EAAE,CAAC,kCAAkC,CAAC;AACvD,QAAQ,oBAAoB,EAAE,CAAC,wCAAwC,CAAC;AACxE,QAAQ,UAAU,EAAE,CAAC,iDAAiD,CAAC;AACvE,QAAQ,eAAe,EAAE,CAAC,sDAAsD,CAAC;AACjF,QAAQ,eAAe,EAAE,CAAC,+CAA+C,CAAC;AAC1E,QAAQ,yBAAyB,EAAE;AACnC,YAAY,+EAA+E;AAC3F,SAAS;AACT,QAAQ,mCAAmC,EAAE;AAC7C,YAAY,2EAA2E;AACvF,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,iDAAiD,CAAC;AACxE,QAAQ,eAAe,EAAE,CAAC,qDAAqD,CAAC;AAChF,QAAQ,mCAAmC,EAAE;AAC7C,YAAY,2EAA2E;AACvF,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,yCAAyC,CAAC;AAC7D,QAAQ,UAAU,EAAE,CAAC,2CAA2C,CAAC;AACjE,QAAQ,uBAAuB,EAAE;AACjC,YAAY,kDAAkD;AAC9D,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,oCAAoC,CAAC;AAC5D,QAAQ,yBAAyB,EAAE;AACnC,YAAY,oEAAoE;AAChF,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,yCAAyC,CAAC;AACtE,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,yBAAyB,EAAE,CAAC,oCAAoC,CAAC;AACzE,QAAQ,wBAAwB,EAAE;AAClC,YAAY,kDAAkD;AAC9D,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,mCAAmC,CAAC;AAC1D,QAAQ,gBAAgB,EAAE,CAAC,wCAAwC,CAAC;AACpE,QAAQ,cAAc,EAAE,CAAC,gCAAgC,CAAC;AAC1D,QAAQ,sBAAsB,EAAE;AAChC,YAAY,gEAAgE;AAC5E,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,uCAAuC,CAAC;AAClE,QAAQ,wBAAwB,EAAE,CAAC,iBAAiB,CAAC;AACrD,QAAQ,UAAU,EAAE,CAAC,uBAAuB,CAAC;AAC7C,QAAQ,WAAW,EAAE,CAAC,6BAA6B,CAAC;AACpD,QAAQ,SAAS,EAAE,CAAC,iCAAiC,CAAC;AACtD,QAAQ,eAAe,EAAE,CAAC,uCAAuC,CAAC;AAClE,QAAQ,mCAAmC,EAAE,CAAC,kCAAkC,CAAC;AACjF,QAAQ,aAAa,EAAE,CAAC,qCAAqC,CAAC;AAC9D,QAAQ,eAAe,EAAE,CAAC,wCAAwC,CAAC;AACnE,QAAQ,UAAU,EAAE,CAAC,mBAAmB,CAAC;AACzC,QAAQ,oCAAoC,EAAE;AAC9C,YAAY,sDAAsD;AAClE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,oCAAoC,CAAC;AAC5D,QAAQ,QAAQ,EAAE,CAAC,gCAAgC,CAAC;AACpD,QAAQ,SAAS,EAAE,CAAC,iCAAiC,CAAC;AACtD,QAAQ,YAAY,EAAE,CAAC,iCAAiC,CAAC;AACzD,QAAQ,KAAK,EAAE,CAAC,mCAAmC,CAAC;AACpD,QAAQ,WAAW,EAAE,CAAC,kDAAkD,CAAC;AACzE,QAAQ,2BAA2B,EAAE;AACrC,YAAY,6EAA6E;AACzF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE;AACjC,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,uDAAuD;AACnE,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,2FAA2F;AACvG,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE;AACrC,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,kFAAkF;AAC9F,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,8EAA8E;AAC1F,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,8EAA8E;AAC1F,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,qDAAqD,CAAC;AAC7E,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,kCAAkC;AAC9C,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;AAClD,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,yCAAyC,CAAC;AACtE,QAAQ,wBAAwB,EAAE;AAClC,YAAY,wEAAwE;AACpF,SAAS;AACT,QAAQ,wBAAwB,EAAE;AAClC,YAAY,0EAA0E;AACtF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE;AACjC,SAAS;AACT,QAAQ,sBAAsB,EAAE;AAChC,YAAY,wFAAwF;AACpG,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE;AACrC,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,2EAA2E;AACvF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,2EAA2E;AACvF,YAAY,EAAE;AACd,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE;AAClC,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,kDAAkD,CAAC;AAC7E,QAAQ,QAAQ,EAAE,CAAC,qCAAqC,CAAC;AACzD,QAAQ,MAAM,EAAE,CAAC,6BAA6B,CAAC;AAC/C,QAAQ,sBAAsB,EAAE;AAChC,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,mBAAmB,EAAE,CAAC,mDAAmD,CAAC;AAClF,QAAQ,+BAA+B,EAAE,CAAC,iCAAiC,CAAC;AAC5E,QAAQ,gBAAgB,EAAE;AAC1B,YAAY,yDAAyD;AACrE,SAAS;AACT,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,wFAAwF;AACpG,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,mDAAmD,CAAC;AAC5E,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,0BAA0B,EAAE;AACpC,YAAY,iFAAiF;AAC7F,YAAY,EAAE;AACd,YAAY,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,6BAA6B,CAAC,EAAE;AACjE,SAAS;AACT,QAAQ,2BAA2B,EAAE;AACrC,YAAY,iFAAiF;AAC7F,SAAS;AACT,QAAQ,aAAa,EAAE,CAAC,6CAA6C,CAAC;AACtE,QAAQ,0BAA0B,EAAE;AACpC,YAAY,oDAAoD;AAChE,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,sEAAsE;AAClF,YAAY,EAAE,OAAO,EAAE,4BAA4B,EAAE;AACrD,SAAS;AACT,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,IAAI,EAAE,CAAC,kBAAkB,CAAC;AAClC,QAAQ,OAAO,EAAE,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;AAChF,QAAQ,qBAAqB,EAAE,CAAC,oBAAoB,CAAC;AACrD,QAAQ,MAAM,EAAE,CAAC,oBAAoB,CAAC;AACtC,QAAQ,KAAK,EAAE,CAAC,0BAA0B,CAAC;AAC3C,QAAQ,MAAM,EAAE,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;AAC9E,QAAQ,KAAK,EAAE,CAAC,mBAAmB,CAAC;AACpC,KAAK;AACL,IAAI,cAAc,EAAE;AACpB,QAAQ,QAAQ,EAAE;AAClB,YAAY,iEAAiE;AAC7E,SAAS;AACT,QAAQ,iBAAiB,EAAE,CAAC,kDAAkD,CAAC;AAC/E,QAAQ,WAAW,EAAE;AACrB,YAAY,mEAAmE;AAC/E,SAAS;AACT,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,iCAAiC,EAAE;AAC3C,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,kCAAkC,EAAE;AAC5C,YAAY,yDAAyD;AACrE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,+BAA+B,EAAE;AACzC,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,+BAA+B,EAAE;AACzC,YAAY,yDAAyD;AACrE,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,wDAAwD;AACpE,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,wBAAwB,CAAC;AAC1C,QAAQ,4BAA4B,EAAE;AACtC,YAAY,6EAA6E;AACzF,SAAS;AACT,QAAQ,qBAAqB,EAAE,CAAC,gDAAgD,CAAC;AACjF,QAAQ,4BAA4B,EAAE;AACtC,YAAY,gGAAgG;AAC5G,SAAS;AACT,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,sEAAsE;AAClF,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,sCAAsC,CAAC;AAC7D,QAAQ,SAAS,EAAE,CAAC,mCAAmC,CAAC;AACxD,QAAQ,yBAAyB,EAAE;AACnC,YAAY,6FAA6F;AACzG,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,mEAAmE;AAC/E,SAAS;AACT,QAAQ,yBAAyB,EAAE;AACnC,YAAY,0DAA0D;AACtE,SAAS;AACT,QAAQ,IAAI,EAAE,CAAC,uBAAuB,CAAC;AACvC,QAAQ,cAAc,EAAE,CAAC,yCAAyC,CAAC;AACnE,QAAQ,2BAA2B,EAAE;AACrC,YAAY,4EAA4E;AACxF,SAAS;AACT,QAAQ,oBAAoB,EAAE,CAAC,+CAA+C,CAAC;AAC/E,QAAQ,wBAAwB,EAAE,CAAC,iBAAiB,CAAC;AACrD,QAAQ,gBAAgB,EAAE,CAAC,2CAA2C,CAAC;AACvE,QAAQ,2BAA2B,EAAE;AACrC,YAAY,+CAA+C;AAC3D,SAAS;AACT,QAAQ,iBAAiB,EAAE;AAC3B,YAAY,4CAA4C;AACxD,YAAY,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE;AACpD,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,yCAAyC,CAAC;AACnE,QAAQ,4BAA4B,EAAE;AACtC,YAAY,6DAA6D;AACzE,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,4DAA4D;AACxE,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,YAAY,2DAA2D;AACvE,SAAS;AACT,QAAQ,4BAA4B,EAAE;AACtC,YAAY,+FAA+F;AAC3G,SAAS;AACT,QAAQ,qBAAqB,EAAE;AAC/B,YAAY,qEAAqE;AACjF,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,qCAAqC,CAAC;AAC5D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,QAAQ,wBAAwB,EAAE,CAAC,mBAAmB,CAAC;AACvD,QAAQ,KAAK,EAAE,CAAC,6BAA6B,CAAC;AAC9C,QAAQ,YAAY,EAAE,CAAC,6BAA6B,CAAC;AACrD,QAAQ,qBAAqB,EAAE,CAAC,+CAA+C,CAAC;AAChF,QAAQ,oCAAoC,EAAE,CAAC,gCAAgC,CAAC;AAChF,QAAQ,4BAA4B,EAAE,CAAC,qBAAqB,CAAC;AAC7D,QAAQ,kCAAkC,EAAE,CAAC,iBAAiB,CAAC;AAC/D,QAAQ,2BAA2B,EAAE,CAAC,qBAAqB,CAAC;AAC5D,QAAQ,4BAA4B,EAAE,CAAC,oCAAoC,CAAC;AAC5E,QAAQ,kCAAkC,EAAE,CAAC,4BAA4B,CAAC;AAC1E,QAAQ,MAAM,EAAE,CAAC,gCAAgC,CAAC;AAClD,QAAQ,gBAAgB,EAAE,CAAC,WAAW,CAAC;AACvC,QAAQ,aAAa,EAAE,CAAC,uBAAuB,CAAC;AAChD,QAAQ,iBAAiB,EAAE,CAAC,iCAAiC,CAAC;AAC9D,QAAQ,yBAAyB,EAAE,CAAC,iCAAiC,CAAC;AACtE,QAAQ,+BAA+B,EAAE,CAAC,yBAAyB,CAAC;AACpE,QAAQ,IAAI,EAAE,CAAC,YAAY,CAAC;AAC5B,QAAQ,0BAA0B,EAAE,CAAC,kBAAkB,CAAC;AACxD,QAAQ,0BAA0B,EAAE,CAAC,kBAAkB,CAAC;AACxD,QAAQ,2BAA2B,EAAE,CAAC,qBAAqB,CAAC;AAC5D,QAAQ,iCAAiC,EAAE,CAAC,qBAAqB,CAAC;AAClE,QAAQ,oBAAoB,EAAE,CAAC,iCAAiC,CAAC;AACjE,QAAQ,oBAAoB,EAAE,CAAC,iCAAiC,CAAC;AACjE,QAAQ,2BAA2B,EAAE,CAAC,oBAAoB,CAAC;AAC3D,QAAQ,kBAAkB,EAAE,CAAC,gCAAgC,CAAC;AAC9D,QAAQ,gCAAgC,EAAE,CAAC,yBAAyB,CAAC;AACrE,QAAQ,qBAAqB,EAAE,CAAC,4BAA4B,CAAC;AAC7D,QAAQ,iCAAiC,EAAE,CAAC,gBAAgB,CAAC;AAC7D,QAAQ,yCAAyC,EAAE,CAAC,8BAA8B,CAAC;AACnF,QAAQ,OAAO,EAAE,CAAC,gCAAgC,CAAC;AACnD,QAAQ,QAAQ,EAAE,CAAC,mCAAmC,CAAC;AACvD,QAAQ,mBAAmB,EAAE,CAAC,aAAa,CAAC;AAC5C,KAAK;AACL,CAAC;;AC33CM,MAAM,OAAO,GAAG,mBAAmB,CAAC;;ACApC,SAAS,kBAAkB,CAAC,OAAO,EAAE,YAAY,EAAE;AAC1D,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,KAAK,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACnE,QAAQ,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACxE,YAAY,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC;AAC5D,YAAY,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACnD,YAAY,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC9E,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACpC,gBAAgB,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACvC,aAAa;AACb,YAAY,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACnD,YAAY,IAAI,WAAW,EAAE;AAC7B,gBAAgB,YAAY,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;AAC/G,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,YAAY,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAClF,SAAS;AACT,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC;AACD,SAAS,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE;AACrE,IAAI,MAAM,mBAAmB,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnE;AACA,IAAI,SAAS,eAAe,CAAC,GAAG,IAAI,EAAE;AACtC;AACA,QAAQ,IAAI,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;AAClE;AACA,QAAQ,IAAI,WAAW,CAAC,SAAS,EAAE;AACnC,YAAY,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE;AACjD,gBAAgB,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC;AACpD,gBAAgB,CAAC,WAAW,CAAC,SAAS,GAAG,SAAS;AAClD,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAChD,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,OAAO,EAAE;AACjC,YAAY,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;AAClE,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5H,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE;AACpC,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,iBAAiB,EAAE;AAC3C;AACA,YAAY,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;AACxE,YAAY,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE;AACvF,gBAAgB,IAAI,IAAI,IAAI,OAAO,EAAE;AACrC,oBAAoB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACzI,oBAAoB,IAAI,EAAE,KAAK,IAAI,OAAO,CAAC,EAAE;AAC7C,wBAAwB,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACvD,qBAAqB;AACrB,oBAAoB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACzC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAChD,SAAS;AACT;AACA,QAAQ,OAAO,mBAAmB,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;AAC/D,CAAC;;ACxDM,SAAS,mBAAmB,CAAC,OAAO,EAAE;AAC7C,IAAI,MAAM,GAAG,GAAG,kBAAkB,CAAC,OAAO,EAAEA,SAAS,CAAC,CAAC;AACvD,IAAI,OAAO;AACX,QAAQ,GAAG,GAAG;AACd,QAAQ,IAAI,EAAE,GAAG;AACjB,KAAK,CAAC;AACN,CAAC;AACD,mBAAmB,CAAC,OAAO,GAAG,OAAO,CAAC;;;;"}