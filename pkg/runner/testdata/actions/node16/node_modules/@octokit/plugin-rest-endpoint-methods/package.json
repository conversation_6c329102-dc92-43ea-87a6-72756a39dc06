{"name": "@octokit/plugin-rest-endpoint-methods", "description": "Octokit plugin adding one method for all of api.github.com REST API endpoints", "version": "4.15.1", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["github", "api", "sdk", "toolkit"], "repository": "github:octokit/plugin-rest-endpoint-methods.js", "dependencies": {"@octokit/types": "^6.13.0", "deprecation": "^2.3.1"}, "peerDependencies": {"@octokit/core": ">=3"}, "devDependencies": {"@gimenete/type-writer": "^0.1.5", "@octokit/core": "^3.0.0", "@octokit/graphql": "^4.3.1", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.3.1", "@types/jest": "^26.0.0", "@types/node": "^14.0.4", "fetch-mock": "^9.0.0", "fs-extra": "^9.0.0", "jest": "^26.1.0", "lodash.camelcase": "^4.3.0", "lodash.set": "^4.3.2", "lodash.upperfirst": "^4.3.1", "mustache": "^4.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.0.1", "semantic-release": "^17.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "sort-keys": "^4.0.0", "string-to-jsdoc-comment": "^1.0.0", "ts-jest": "^26.1.3", "typescript": "^4.0.2"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}