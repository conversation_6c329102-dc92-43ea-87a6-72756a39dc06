{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/get-buffer-response.js", "../dist-src/fetch-wrapper.js", "../dist-src/with-defaults.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"5.6.3\";\n", "export default function getBufferResponse(response) {\n    return response.arrayBuffer();\n}\n", "import { isPlainObject } from \"is-plain-object\";\nimport nodeFetch from \"node-fetch\";\nimport { RequestError } from \"@octokit/request-error\";\nimport getBuffer from \"./get-buffer-response\";\nexport default function fetchWrapper(requestOptions) {\n    const log = requestOptions.request && requestOptions.request.log\n        ? requestOptions.request.log\n        : console;\n    if (isPlainObject(requestOptions.body) ||\n        Array.isArray(requestOptions.body)) {\n        requestOptions.body = JSON.stringify(requestOptions.body);\n    }\n    let headers = {};\n    let status;\n    let url;\n    const fetch = (requestOptions.request && requestOptions.request.fetch) || nodeFetch;\n    return fetch(requestOptions.url, Object.assign({\n        method: requestOptions.method,\n        body: requestOptions.body,\n        headers: requestOptions.headers,\n        redirect: requestOptions.redirect,\n    }, \n    // `requestOptions.request.agent` type is incompatible\n    // see https://github.com/octokit/types.ts/pull/264\n    requestOptions.request))\n        .then(async (response) => {\n        url = response.url;\n        status = response.status;\n        for (const keyAndValue of response.headers) {\n            headers[keyAndValue[0]] = keyAndValue[1];\n        }\n        if (\"deprecation\" in headers) {\n            const matches = headers.link && headers.link.match(/<([^>]+)>; rel=\"deprecation\"/);\n            const deprecationLink = matches && matches.pop();\n            log.warn(`[@octokit/request] \"${requestOptions.method} ${requestOptions.url}\" is deprecated. It is scheduled to be removed on ${headers.sunset}${deprecationLink ? `. See ${deprecationLink}` : \"\"}`);\n        }\n        if (status === 204 || status === 205) {\n            return;\n        }\n        // GitHub API returns 200 for HEAD requests\n        if (requestOptions.method === \"HEAD\") {\n            if (status < 400) {\n                return;\n            }\n            throw new RequestError(response.statusText, status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data: undefined,\n                },\n                request: requestOptions,\n            });\n        }\n        if (status === 304) {\n            throw new RequestError(\"Not modified\", status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data: await getResponseData(response),\n                },\n                request: requestOptions,\n            });\n        }\n        if (status >= 400) {\n            const data = await getResponseData(response);\n            const error = new RequestError(toErrorMessage(data), status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data,\n                },\n                request: requestOptions,\n            });\n            throw error;\n        }\n        return getResponseData(response);\n    })\n        .then((data) => {\n        return {\n            status,\n            url,\n            headers,\n            data,\n        };\n    })\n        .catch((error) => {\n        if (error instanceof RequestError)\n            throw error;\n        throw new RequestError(error.message, 500, {\n            request: requestOptions,\n        });\n    });\n}\nasync function getResponseData(response) {\n    const contentType = response.headers.get(\"content-type\");\n    if (/application\\/json/.test(contentType)) {\n        return response.json();\n    }\n    if (!contentType || /^text\\/|charset=utf-8$/.test(contentType)) {\n        return response.text();\n    }\n    return getBuffer(response);\n}\nfunction toErrorMessage(data) {\n    if (typeof data === \"string\")\n        return data;\n    // istanbul ignore else - just in case\n    if (\"message\" in data) {\n        if (Array.isArray(data.errors)) {\n            return `${data.message}: ${data.errors.map(JSON.stringify).join(\", \")}`;\n        }\n        return data.message;\n    }\n    // istanbul ignore next - just in case\n    return `Unknown error: ${JSON.stringify(data)}`;\n}\n", "import fetchWrapper from \"./fetch-wrapper\";\nexport default function withDefaults(oldEndpoint, newDefaults) {\n    const endpoint = oldEndpoint.defaults(newDefaults);\n    const newApi = function (route, parameters) {\n        const endpointOptions = endpoint.merge(route, parameters);\n        if (!endpointOptions.request || !endpointOptions.request.hook) {\n            return fetchWrapper(endpoint.parse(endpointOptions));\n        }\n        const request = (route, parameters) => {\n            return fetchWrapper(endpoint.parse(endpoint.merge(route, parameters)));\n        };\n        Object.assign(request, {\n            endpoint,\n            defaults: withDefaults.bind(null, endpoint),\n        });\n        return endpointOptions.request.hook(request, endpointOptions);\n    };\n    return Object.assign(newApi, {\n        endpoint,\n        defaults: withDefaults.bind(null, endpoint),\n    });\n}\n", "import { endpoint } from \"@octokit/endpoint\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nimport withDefaults from \"./with-defaults\";\nexport const request = withDefaults(endpoint, {\n    headers: {\n        \"user-agent\": `octokit-request.js/${VERSION} ${getUserAgent()}`,\n    },\n});\n"], "names": ["VERSION", "getBufferResponse", "response", "arrayBuffer", "fetchWrapper", "requestOptions", "log", "request", "console", "isPlainObject", "body", "Array", "isArray", "JSON", "stringify", "headers", "status", "url", "fetch", "nodeFetch", "Object", "assign", "method", "redirect", "then", "keyAndValue", "matches", "link", "match", "deprecationLink", "pop", "warn", "sunset", "RequestError", "statusText", "data", "undefined", "getResponseData", "error", "toErrorMessage", "catch", "message", "contentType", "get", "test", "json", "text", "<PERSON><PERSON><PERSON><PERSON>", "errors", "map", "join", "with<PERSON><PERSON><PERSON><PERSON>", "oldEndpoint", "newDefaults", "endpoint", "defaults", "newApi", "route", "parameters", "endpointOptions", "merge", "hook", "parse", "bind", "getUserAgent"], "mappings": ";;;;;;;;;;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;ACAQ,SAASC,iBAAT,CAA2BC,QAA3B,EAAqC;AAChD,SAAOA,QAAQ,CAACC,WAAT,EAAP;AACH;;ACEc,SAASC,YAAT,CAAsBC,cAAtB,EAAsC;AACjD,QAAMC,GAAG,GAAGD,cAAc,CAACE,OAAf,IAA0BF,cAAc,CAACE,OAAf,CAAuBD,GAAjD,GACND,cAAc,CAACE,OAAf,CAAuBD,GADjB,GAENE,OAFN;;AAGA,MAAIC,2BAAa,CAACJ,cAAc,CAACK,IAAhB,CAAb,IACAC,KAAK,CAACC,OAAN,CAAcP,cAAc,CAACK,IAA7B,CADJ,EACwC;AACpCL,IAAAA,cAAc,CAACK,IAAf,GAAsBG,IAAI,CAACC,SAAL,CAAeT,cAAc,CAACK,IAA9B,CAAtB;AACH;;AACD,MAAIK,OAAO,GAAG,EAAd;AACA,MAAIC,MAAJ;AACA,MAAIC,GAAJ;AACA,QAAMC,KAAK,GAAIb,cAAc,CAACE,OAAf,IAA0BF,cAAc,CAACE,OAAf,CAAuBW,KAAlD,IAA4DC,SAA1E;AACA,SAAOD,KAAK,CAACb,cAAc,CAACY,GAAhB,EAAqBG,MAAM,CAACC,MAAP,CAAc;AAC3CC,IAAAA,MAAM,EAAEjB,cAAc,CAACiB,MADoB;AAE3CZ,IAAAA,IAAI,EAAEL,cAAc,CAACK,IAFsB;AAG3CK,IAAAA,OAAO,EAAEV,cAAc,CAACU,OAHmB;AAI3CQ,IAAAA,QAAQ,EAAElB,cAAc,CAACkB;AAJkB,GAAd;AAOjC;AACAlB,EAAAA,cAAc,CAACE,OARkB,CAArB,CAAL,CASFiB,IATE,CASG,MAAOtB,QAAP,IAAoB;AAC1Be,IAAAA,GAAG,GAAGf,QAAQ,CAACe,GAAf;AACAD,IAAAA,MAAM,GAAGd,QAAQ,CAACc,MAAlB;;AACA,SAAK,MAAMS,WAAX,IAA0BvB,QAAQ,CAACa,OAAnC,EAA4C;AACxCA,MAAAA,OAAO,CAACU,WAAW,CAAC,CAAD,CAAZ,CAAP,GAA0BA,WAAW,CAAC,CAAD,CAArC;AACH;;AACD,QAAI,iBAAiBV,OAArB,EAA8B;AAC1B,YAAMW,OAAO,GAAGX,OAAO,CAACY,IAAR,IAAgBZ,OAAO,CAACY,IAAR,CAAaC,KAAb,CAAmB,8BAAnB,CAAhC;AACA,YAAMC,eAAe,GAAGH,OAAO,IAAIA,OAAO,CAACI,GAAR,EAAnC;AACAxB,MAAAA,GAAG,CAACyB,IAAJ,CAAU,uBAAsB1B,cAAc,CAACiB,MAAO,IAAGjB,cAAc,CAACY,GAAI,qDAAoDF,OAAO,CAACiB,MAAO,GAAEH,eAAe,GAAI,SAAQA,eAAgB,EAA5B,GAAgC,EAAG,EAAnM;AACH;;AACD,QAAIb,MAAM,KAAK,GAAX,IAAkBA,MAAM,KAAK,GAAjC,EAAsC;AAClC;AACH,KAbyB;;;AAe1B,QAAIX,cAAc,CAACiB,MAAf,KAA0B,MAA9B,EAAsC;AAClC,UAAIN,MAAM,GAAG,GAAb,EAAkB;AACd;AACH;;AACD,YAAM,IAAIiB,yBAAJ,CAAiB/B,QAAQ,CAACgC,UAA1B,EAAsClB,MAAtC,EAA8C;AAChDd,QAAAA,QAAQ,EAAE;AACNe,UAAAA,GADM;AAEND,UAAAA,MAFM;AAGND,UAAAA,OAHM;AAINoB,UAAAA,IAAI,EAAEC;AAJA,SADsC;AAOhD7B,QAAAA,OAAO,EAAEF;AAPuC,OAA9C,CAAN;AASH;;AACD,QAAIW,MAAM,KAAK,GAAf,EAAoB;AAChB,YAAM,IAAIiB,yBAAJ,CAAiB,cAAjB,EAAiCjB,MAAjC,EAAyC;AAC3Cd,QAAAA,QAAQ,EAAE;AACNe,UAAAA,GADM;AAEND,UAAAA,MAFM;AAGND,UAAAA,OAHM;AAINoB,UAAAA,IAAI,EAAE,MAAME,eAAe,CAACnC,QAAD;AAJrB,SADiC;AAO3CK,QAAAA,OAAO,EAAEF;AAPkC,OAAzC,CAAN;AASH;;AACD,QAAIW,MAAM,IAAI,GAAd,EAAmB;AACf,YAAMmB,IAAI,GAAG,MAAME,eAAe,CAACnC,QAAD,CAAlC;AACA,YAAMoC,KAAK,GAAG,IAAIL,yBAAJ,CAAiBM,cAAc,CAACJ,IAAD,CAA/B,EAAuCnB,MAAvC,EAA+C;AACzDd,QAAAA,QAAQ,EAAE;AACNe,UAAAA,GADM;AAEND,UAAAA,MAFM;AAGND,UAAAA,OAHM;AAINoB,UAAAA;AAJM,SAD+C;AAOzD5B,QAAAA,OAAO,EAAEF;AAPgD,OAA/C,CAAd;AASA,YAAMiC,KAAN;AACH;;AACD,WAAOD,eAAe,CAACnC,QAAD,CAAtB;AACH,GA/DM,EAgEFsB,IAhEE,CAgEIW,IAAD,IAAU;AAChB,WAAO;AACHnB,MAAAA,MADG;AAEHC,MAAAA,GAFG;AAGHF,MAAAA,OAHG;AAIHoB,MAAAA;AAJG,KAAP;AAMH,GAvEM,EAwEFK,KAxEE,CAwEKF,KAAD,IAAW;AAClB,QAAIA,KAAK,YAAYL,yBAArB,EACI,MAAMK,KAAN;AACJ,UAAM,IAAIL,yBAAJ,CAAiBK,KAAK,CAACG,OAAvB,EAAgC,GAAhC,EAAqC;AACvClC,MAAAA,OAAO,EAAEF;AAD8B,KAArC,CAAN;AAGH,GA9EM,CAAP;AA+EH;;AACD,eAAegC,eAAf,CAA+BnC,QAA/B,EAAyC;AACrC,QAAMwC,WAAW,GAAGxC,QAAQ,CAACa,OAAT,CAAiB4B,GAAjB,CAAqB,cAArB,CAApB;;AACA,MAAI,oBAAoBC,IAApB,CAAyBF,WAAzB,CAAJ,EAA2C;AACvC,WAAOxC,QAAQ,CAAC2C,IAAT,EAAP;AACH;;AACD,MAAI,CAACH,WAAD,IAAgB,yBAAyBE,IAAzB,CAA8BF,WAA9B,CAApB,EAAgE;AAC5D,WAAOxC,QAAQ,CAAC4C,IAAT,EAAP;AACH;;AACD,SAAOC,iBAAS,CAAC7C,QAAD,CAAhB;AACH;;AACD,SAASqC,cAAT,CAAwBJ,IAAxB,EAA8B;AAC1B,MAAI,OAAOA,IAAP,KAAgB,QAApB,EACI,OAAOA,IAAP,CAFsB;;AAI1B,MAAI,aAAaA,IAAjB,EAAuB;AACnB,QAAIxB,KAAK,CAACC,OAAN,CAAcuB,IAAI,CAACa,MAAnB,CAAJ,EAAgC;AAC5B,aAAQ,GAAEb,IAAI,CAACM,OAAQ,KAAIN,IAAI,CAACa,MAAL,CAAYC,GAAZ,CAAgBpC,IAAI,CAACC,SAArB,EAAgCoC,IAAhC,CAAqC,IAArC,CAA2C,EAAtE;AACH;;AACD,WAAOf,IAAI,CAACM,OAAZ;AACH,GATyB;;;AAW1B,SAAQ,kBAAiB5B,IAAI,CAACC,SAAL,CAAeqB,IAAf,CAAqB,EAA9C;AACH;;ACrHc,SAASgB,YAAT,CAAsBC,WAAtB,EAAmCC,WAAnC,EAAgD;AAC3D,QAAMC,QAAQ,GAAGF,WAAW,CAACG,QAAZ,CAAqBF,WAArB,CAAjB;;AACA,QAAMG,MAAM,GAAG,UAAUC,KAAV,EAAiBC,UAAjB,EAA6B;AACxC,UAAMC,eAAe,GAAGL,QAAQ,CAACM,KAAT,CAAeH,KAAf,EAAsBC,UAAtB,CAAxB;;AACA,QAAI,CAACC,eAAe,CAACpD,OAAjB,IAA4B,CAACoD,eAAe,CAACpD,OAAhB,CAAwBsD,IAAzD,EAA+D;AAC3D,aAAOzD,YAAY,CAACkD,QAAQ,CAACQ,KAAT,CAAeH,eAAf,CAAD,CAAnB;AACH;;AACD,UAAMpD,OAAO,GAAG,CAACkD,KAAD,EAAQC,UAAR,KAAuB;AACnC,aAAOtD,YAAY,CAACkD,QAAQ,CAACQ,KAAT,CAAeR,QAAQ,CAACM,KAAT,CAAeH,KAAf,EAAsBC,UAAtB,CAAf,CAAD,CAAnB;AACH,KAFD;;AAGAtC,IAAAA,MAAM,CAACC,MAAP,CAAcd,OAAd,EAAuB;AACnB+C,MAAAA,QADmB;AAEnBC,MAAAA,QAAQ,EAAEJ,YAAY,CAACY,IAAb,CAAkB,IAAlB,EAAwBT,QAAxB;AAFS,KAAvB;AAIA,WAAOK,eAAe,CAACpD,OAAhB,CAAwBsD,IAAxB,CAA6BtD,OAA7B,EAAsCoD,eAAtC,CAAP;AACH,GAbD;;AAcA,SAAOvC,MAAM,CAACC,MAAP,CAAcmC,MAAd,EAAsB;AACzBF,IAAAA,QADyB;AAEzBC,IAAAA,QAAQ,EAAEJ,YAAY,CAACY,IAAb,CAAkB,IAAlB,EAAwBT,QAAxB;AAFe,GAAtB,CAAP;AAIH;;MCjBY/C,OAAO,GAAG4C,YAAY,CAACG,iBAAD,EAAW;AAC1CvC,EAAAA,OAAO,EAAE;AACL,kBAAe,sBAAqBf,OAAQ,IAAGgE,+BAAY,EAAG;AADzD;AADiC,CAAX,CAA5B;;;;"}