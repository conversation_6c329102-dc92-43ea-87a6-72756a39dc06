{"name": "@octokit/graphql", "description": "GitHub GraphQL API client for browsers and Node", "version": "4.8.0", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "api", "graphql"], "repository": "github:octokit/graphql.js", "dependencies": {"@octokit/request": "^5.6.0", "@octokit/types": "^6.0.3", "universal-user-agent": "^6.0.0"}, "devDependencies": {"@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.2.5", "@types/jest": "^27.0.0", "@types/node": "^14.0.4", "fetch-mock": "^9.0.0", "jest": "^27.0.0", "prettier": "2.3.2", "semantic-release": "^17.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^27.0.0-next.12", "typescript": "^4.0.0"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}