{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/error.js", "../dist-src/graphql.js", "../dist-src/with-defaults.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"4.8.0\";\n", "function _buildMessageForResponseErrors(data) {\n    return (`Request failed due to following response errors:\\n` +\n        data.errors.map((e) => ` - ${e.message}`).join(\"\\n\"));\n}\nexport class GraphqlResponseError extends Error {\n    constructor(request, headers, response) {\n        super(_buildMessageForResponseErrors(response));\n        this.request = request;\n        this.headers = headers;\n        this.response = response;\n        this.name = \"GraphqlResponseError\";\n        // Expose the errors and response data in their shorthand properties.\n        this.errors = response.errors;\n        this.data = response.data;\n        // Maintains proper stack trace (only available on V8)\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        }\n    }\n}\n", "import { GraphqlResponseError } from \"./error\";\nconst NON_VARIABLE_OPTIONS = [\n    \"method\",\n    \"baseUrl\",\n    \"url\",\n    \"headers\",\n    \"request\",\n    \"query\",\n    \"mediaType\",\n];\nconst FORBIDDEN_VARIABLE_OPTIONS = [\"query\", \"method\", \"url\"];\nconst GHES_V3_SUFFIX_REGEX = /\\/api\\/v3\\/?$/;\nexport function graphql(request, query, options) {\n    if (options) {\n        if (typeof query === \"string\" && \"query\" in options) {\n            return Promise.reject(new Error(`[@octokit/graphql] \"query\" cannot be used as variable name`));\n        }\n        for (const key in options) {\n            if (!FORBIDDEN_VARIABLE_OPTIONS.includes(key))\n                continue;\n            return Promise.reject(new Error(`[@octokit/graphql] \"${key}\" cannot be used as variable name`));\n        }\n    }\n    const parsedOptions = typeof query === \"string\" ? Object.assign({ query }, options) : query;\n    const requestOptions = Object.keys(parsedOptions).reduce((result, key) => {\n        if (NON_VARIABLE_OPTIONS.includes(key)) {\n            result[key] = parsedOptions[key];\n            return result;\n        }\n        if (!result.variables) {\n            result.variables = {};\n        }\n        result.variables[key] = parsedOptions[key];\n        return result;\n    }, {});\n    // workaround for GitHub Enterprise baseUrl set with /api/v3 suffix\n    // https://github.com/octokit/auth-app.js/issues/111#issuecomment-657610451\n    const baseUrl = parsedOptions.baseUrl || request.endpoint.DEFAULTS.baseUrl;\n    if (GHES_V3_SUFFIX_REGEX.test(baseUrl)) {\n        requestOptions.url = baseUrl.replace(GHES_V3_SUFFIX_REGEX, \"/api/graphql\");\n    }\n    return request(requestOptions).then((response) => {\n        if (response.data.errors) {\n            const headers = {};\n            for (const key of Object.keys(response.headers)) {\n                headers[key] = response.headers[key];\n            }\n            throw new GraphqlResponseError(requestOptions, headers, response.data);\n        }\n        return response.data.data;\n    });\n}\n", "import { request as Request } from \"@octokit/request\";\nimport { graphql } from \"./graphql\";\nexport function withDefaults(request, newDefaults) {\n    const newRequest = request.defaults(newDefaults);\n    const newApi = (query, options) => {\n        return graphql(newRequest, query, options);\n    };\n    return Object.assign(newApi, {\n        defaults: withDefaults.bind(null, newRequest),\n        endpoint: Request.endpoint,\n    });\n}\n", "import { request } from \"@octokit/request\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nimport { withDefaults } from \"./with-defaults\";\nexport const graphql = withDefaults(request, {\n    headers: {\n        \"user-agent\": `octokit-graphql.js/${VERSION} ${getUserAgent()}`,\n    },\n    method: \"POST\",\n    url: \"/graphql\",\n});\nexport { GraphqlResponseError } from \"./error\";\nexport function withCustomRequest(customRequest) {\n    return withDefaults(customRequest, {\n        method: \"POST\",\n        url: \"/graphql\",\n    });\n}\n"], "names": ["VERSION", "_buildMessageForResponseErrors", "data", "errors", "map", "e", "message", "join", "GraphqlResponseError", "Error", "constructor", "request", "headers", "response", "name", "captureStackTrace", "NON_VARIABLE_OPTIONS", "FORBIDDEN_VARIABLE_OPTIONS", "GHES_V3_SUFFIX_REGEX", "graphql", "query", "options", "Promise", "reject", "key", "includes", "parsedOptions", "Object", "assign", "requestOptions", "keys", "reduce", "result", "variables", "baseUrl", "endpoint", "DEFAULTS", "test", "url", "replace", "then", "with<PERSON><PERSON><PERSON><PERSON>", "newDefaults", "newRequest", "defaults", "newApi", "bind", "Request", "getUserAgent", "method", "withCustomRequest", "customRequest"], "mappings": ";;;;;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;ACAP,SAASC,8BAAT,CAAwCC,IAAxC,EAA8C;AAC1C,SAAS,oDAAD,GACJA,IAAI,CAACC,MAAL,CAAYC,GAAZ,CAAiBC,CAAD,IAAQ,MAAKA,CAAC,CAACC,OAAQ,EAAvC,EAA0CC,IAA1C,CAA+C,IAA/C,CADJ;AAEH;;AACD,AAAO,MAAMC,oBAAN,SAAmCC,KAAnC,CAAyC;AAC5CC,EAAAA,WAAW,CAACC,OAAD,EAAUC,OAAV,EAAmBC,QAAnB,EAA6B;AACpC,UAAMZ,8BAA8B,CAACY,QAAD,CAApC;AACA,SAAKF,OAAL,GAAeA,OAAf;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,IAAL,GAAY,sBAAZ,CALoC;;AAOpC,SAAKX,MAAL,GAAcU,QAAQ,CAACV,MAAvB;AACA,SAAKD,IAAL,GAAYW,QAAQ,CAACX,IAArB,CARoC;;AAUpC;;AACA,QAAIO,KAAK,CAACM,iBAAV,EAA6B;AACzBN,MAAAA,KAAK,CAACM,iBAAN,CAAwB,IAAxB,EAA8B,KAAKL,WAAnC;AACH;AACJ;;AAf2C;;ACHhD,MAAMM,oBAAoB,GAAG,CACzB,QADyB,EAEzB,SAFyB,EAGzB,KAHyB,EAIzB,SAJyB,EAKzB,SALyB,EAMzB,OANyB,EAOzB,WAPyB,CAA7B;AASA,MAAMC,0BAA0B,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,CAAnC;AACA,MAAMC,oBAAoB,GAAG,eAA7B;AACA,AAAO,SAASC,OAAT,CAAiBR,OAAjB,EAA0BS,KAA1B,EAAiCC,OAAjC,EAA0C;AAC7C,MAAIA,OAAJ,EAAa;AACT,QAAI,OAAOD,KAAP,KAAiB,QAAjB,IAA6B,WAAWC,OAA5C,EAAqD;AACjD,aAAOC,OAAO,CAACC,MAAR,CAAe,IAAId,KAAJ,CAAW,4DAAX,CAAf,CAAP;AACH;;AACD,SAAK,MAAMe,GAAX,IAAkBH,OAAlB,EAA2B;AACvB,UAAI,CAACJ,0BAA0B,CAACQ,QAA3B,CAAoCD,GAApC,CAAL,EACI;AACJ,aAAOF,OAAO,CAACC,MAAR,CAAe,IAAId,KAAJ,CAAW,uBAAsBe,GAAI,mCAArC,CAAf,CAAP;AACH;AACJ;;AACD,QAAME,aAAa,GAAG,OAAON,KAAP,KAAiB,QAAjB,GAA4BO,MAAM,CAACC,MAAP,CAAc;AAAER,IAAAA;AAAF,GAAd,EAAyBC,OAAzB,CAA5B,GAAgED,KAAtF;AACA,QAAMS,cAAc,GAAGF,MAAM,CAACG,IAAP,CAAYJ,aAAZ,EAA2BK,MAA3B,CAAkC,CAACC,MAAD,EAASR,GAAT,KAAiB;AACtE,QAAIR,oBAAoB,CAACS,QAArB,CAA8BD,GAA9B,CAAJ,EAAwC;AACpCQ,MAAAA,MAAM,CAACR,GAAD,CAAN,GAAcE,aAAa,CAACF,GAAD,CAA3B;AACA,aAAOQ,MAAP;AACH;;AACD,QAAI,CAACA,MAAM,CAACC,SAAZ,EAAuB;AACnBD,MAAAA,MAAM,CAACC,SAAP,GAAmB,EAAnB;AACH;;AACDD,IAAAA,MAAM,CAACC,SAAP,CAAiBT,GAAjB,IAAwBE,aAAa,CAACF,GAAD,CAArC;AACA,WAAOQ,MAAP;AACH,GAVsB,EAUpB,EAVoB,CAAvB,CAZ6C;AAwB7C;;AACA,QAAME,OAAO,GAAGR,aAAa,CAACQ,OAAd,IAAyBvB,OAAO,CAACwB,QAAR,CAAiBC,QAAjB,CAA0BF,OAAnE;;AACA,MAAIhB,oBAAoB,CAACmB,IAArB,CAA0BH,OAA1B,CAAJ,EAAwC;AACpCL,IAAAA,cAAc,CAACS,GAAf,GAAqBJ,OAAO,CAACK,OAAR,CAAgBrB,oBAAhB,EAAsC,cAAtC,CAArB;AACH;;AACD,SAAOP,OAAO,CAACkB,cAAD,CAAP,CAAwBW,IAAxB,CAA8B3B,QAAD,IAAc;AAC9C,QAAIA,QAAQ,CAACX,IAAT,CAAcC,MAAlB,EAA0B;AACtB,YAAMS,OAAO,GAAG,EAAhB;;AACA,WAAK,MAAMY,GAAX,IAAkBG,MAAM,CAACG,IAAP,CAAYjB,QAAQ,CAACD,OAArB,CAAlB,EAAiD;AAC7CA,QAAAA,OAAO,CAACY,GAAD,CAAP,GAAeX,QAAQ,CAACD,OAAT,CAAiBY,GAAjB,CAAf;AACH;;AACD,YAAM,IAAIhB,oBAAJ,CAAyBqB,cAAzB,EAAyCjB,OAAzC,EAAkDC,QAAQ,CAACX,IAA3D,CAAN;AACH;;AACD,WAAOW,QAAQ,CAACX,IAAT,CAAcA,IAArB;AACH,GATM,CAAP;AAUH;;ACjDM,SAASuC,YAAT,CAAsB9B,SAAtB,EAA+B+B,WAA/B,EAA4C;AAC/C,QAAMC,UAAU,GAAGhC,SAAO,CAACiC,QAAR,CAAiBF,WAAjB,CAAnB;;AACA,QAAMG,MAAM,GAAG,CAACzB,KAAD,EAAQC,OAAR,KAAoB;AAC/B,WAAOF,OAAO,CAACwB,UAAD,EAAavB,KAAb,EAAoBC,OAApB,CAAd;AACH,GAFD;;AAGA,SAAOM,MAAM,CAACC,MAAP,CAAciB,MAAd,EAAsB;AACzBD,IAAAA,QAAQ,EAAEH,YAAY,CAACK,IAAb,CAAkB,IAAlB,EAAwBH,UAAxB,CADe;AAEzBR,IAAAA,QAAQ,EAAEY,eAAO,CAACZ;AAFO,GAAtB,CAAP;AAIH;;MCPYhB,SAAO,GAAGsB,YAAY,CAAC9B,eAAD,EAAU;AACzCC,EAAAA,OAAO,EAAE;AACL,kBAAe,sBAAqBZ,OAAQ,IAAGgD,+BAAY,EAAG;AADzD,GADgC;AAIzCC,EAAAA,MAAM,EAAE,MAJiC;AAKzCX,EAAAA,GAAG,EAAE;AALoC,CAAV,CAA5B;AAOP,AACO,SAASY,iBAAT,CAA2BC,aAA3B,EAA0C;AAC7C,SAAOV,YAAY,CAACU,aAAD,EAAgB;AAC/BF,IAAAA,MAAM,EAAE,MADuB;AAE/BX,IAAAA,GAAG,EAAE;AAF0B,GAAhB,CAAnB;AAIH;;;;;;"}