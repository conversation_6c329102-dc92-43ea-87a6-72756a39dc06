{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/normalize-paginated-list-response.js", "../dist-src/iterator.js", "../dist-src/paginate.js", "../dist-src/compose-paginate.js", "../dist-src/generated/paginating-endpoints.js", "../dist-src/paginating-endpoints.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"2.21.3\";\n", "/**\n * Some “list” response that can be paginated have a different response structure\n *\n * They have a `total_count` key in the response (search also has `incomplete_results`,\n * /installation/repositories also has `repository_selection`), as well as a key with\n * the list of the items which name varies from endpoint to endpoint.\n *\n * <PERSON><PERSON><PERSON> normalizes these responses so that paginated results are always returned following\n * the same structure. One challenge is that if the list response has only one page, no Link\n * header is provided, so this header alone is not sufficient to check wether a response is\n * paginated or not.\n *\n * We check if a \"total_count\" key is present in the response data, but also make sure that\n * a \"url\" property is not, as the \"Get the combined status for a specific ref\" endpoint would\n * otherwise match: https://developer.github.com/v3/repos/statuses/#get-the-combined-status-for-a-specific-ref\n */\nexport function normalizePaginatedListResponse(response) {\n    // endpoints can respond with 204 if repository is empty\n    if (!response.data) {\n        return {\n            ...response,\n            data: [],\n        };\n    }\n    const responseNeedsNormalization = \"total_count\" in response.data && !(\"url\" in response.data);\n    if (!responseNeedsNormalization)\n        return response;\n    // keep the additional properties intact as there is currently no other way\n    // to retrieve the same information.\n    const incompleteResults = response.data.incomplete_results;\n    const repositorySelection = response.data.repository_selection;\n    const totalCount = response.data.total_count;\n    delete response.data.incomplete_results;\n    delete response.data.repository_selection;\n    delete response.data.total_count;\n    const namespaceKey = Object.keys(response.data)[0];\n    const data = response.data[namespaceKey];\n    response.data = data;\n    if (typeof incompleteResults !== \"undefined\") {\n        response.data.incomplete_results = incompleteResults;\n    }\n    if (typeof repositorySelection !== \"undefined\") {\n        response.data.repository_selection = repositorySelection;\n    }\n    response.data.total_count = totalCount;\n    return response;\n}\n", "import { normalizePaginatedListResponse } from \"./normalize-paginated-list-response\";\nexport function iterator(octokit, route, parameters) {\n    const options = typeof route === \"function\"\n        ? route.endpoint(parameters)\n        : octokit.request.endpoint(route, parameters);\n    const requestMethod = typeof route === \"function\" ? route : octokit.request;\n    const method = options.method;\n    const headers = options.headers;\n    let url = options.url;\n    return {\n        [Symbol.asyncIterator]: () => ({\n            async next() {\n                if (!url)\n                    return { done: true };\n                try {\n                    const response = await requestMethod({ method, url, headers });\n                    const normalizedResponse = normalizePaginatedListResponse(response);\n                    // `response.headers.link` format:\n                    // '<https://api.github.com/users/aseemk/followers?page=2>; rel=\"next\", <https://api.github.com/users/aseemk/followers?page=2>; rel=\"last\"'\n                    // sets `url` to undefined if \"next\" URL is not present or `link` header is not set\n                    url = ((normalizedResponse.headers.link || \"\").match(/<([^>]+)>;\\s*rel=\"next\"/) || [])[1];\n                    return { value: normalizedResponse };\n                }\n                catch (error) {\n                    if (error.status !== 409)\n                        throw error;\n                    url = \"\";\n                    return {\n                        value: {\n                            status: 200,\n                            headers: {},\n                            data: [],\n                        },\n                    };\n                }\n            },\n        }),\n    };\n}\n", "import { iterator } from \"./iterator\";\nexport function paginate(octokit, route, parameters, mapFn) {\n    if (typeof parameters === \"function\") {\n        mapFn = parameters;\n        parameters = undefined;\n    }\n    return gather(octokit, [], iterator(octokit, route, parameters)[Symbol.asyncIterator](), mapFn);\n}\nfunction gather(octokit, results, iterator, mapFn) {\n    return iterator.next().then((result) => {\n        if (result.done) {\n            return results;\n        }\n        let earlyExit = false;\n        function done() {\n            earlyExit = true;\n        }\n        results = results.concat(mapFn ? mapFn(result.value, done) : result.value.data);\n        if (earlyExit) {\n            return results;\n        }\n        return gather(octokit, results, iterator, mapFn);\n    });\n}\n", "import { paginate } from \"./paginate\";\nimport { iterator } from \"./iterator\";\nexport const composePaginateRest = Object.assign(paginate, {\n    iterator,\n});\n", "export const paginatingEndpoints = [\n    \"GET /app/hook/deliveries\",\n    \"GET /app/installations\",\n    \"GET /applications/grants\",\n    \"GET /authorizations\",\n    \"GET /enterprises/{enterprise}/actions/permissions/organizations\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups/{runner_group_id}/organizations\",\n    \"GET /enterprises/{enterprise}/actions/runner-groups/{runner_group_id}/runners\",\n    \"GET /enterprises/{enterprise}/actions/runners\",\n    \"GET /enterprises/{enterprise}/audit-log\",\n    \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n    \"GET /enterprises/{enterprise}/settings/billing/advanced-security\",\n    \"GET /events\",\n    \"GET /gists\",\n    \"GET /gists/public\",\n    \"GET /gists/starred\",\n    \"GET /gists/{gist_id}/comments\",\n    \"GET /gists/{gist_id}/commits\",\n    \"GET /gists/{gist_id}/forks\",\n    \"GET /installation/repositories\",\n    \"GET /issues\",\n    \"GET /licenses\",\n    \"GET /marketplace_listing/plans\",\n    \"GET /marketplace_listing/plans/{plan_id}/accounts\",\n    \"GET /marketplace_listing/stubbed/plans\",\n    \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n    \"GET /networks/{owner}/{repo}/events\",\n    \"GET /notifications\",\n    \"GET /organizations\",\n    \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n    \"GET /orgs/{org}/actions/permissions/repositories\",\n    \"GET /orgs/{org}/actions/runner-groups\",\n    \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories\",\n    \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners\",\n    \"GET /orgs/{org}/actions/runners\",\n    \"GET /orgs/{org}/actions/secrets\",\n    \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    \"GET /orgs/{org}/audit-log\",\n    \"GET /orgs/{org}/blocks\",\n    \"GET /orgs/{org}/code-scanning/alerts\",\n    \"GET /orgs/{org}/codespaces\",\n    \"GET /orgs/{org}/credential-authorizations\",\n    \"GET /orgs/{org}/dependabot/secrets\",\n    \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    \"GET /orgs/{org}/events\",\n    \"GET /orgs/{org}/external-groups\",\n    \"GET /orgs/{org}/failed_invitations\",\n    \"GET /orgs/{org}/hooks\",\n    \"GET /orgs/{org}/hooks/{hook_id}/deliveries\",\n    \"GET /orgs/{org}/installations\",\n    \"GET /orgs/{org}/invitations\",\n    \"GET /orgs/{org}/invitations/{invitation_id}/teams\",\n    \"GET /orgs/{org}/issues\",\n    \"GET /orgs/{org}/members\",\n    \"GET /orgs/{org}/migrations\",\n    \"GET /orgs/{org}/migrations/{migration_id}/repositories\",\n    \"GET /orgs/{org}/outside_collaborators\",\n    \"GET /orgs/{org}/packages\",\n    \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n    \"GET /orgs/{org}/projects\",\n    \"GET /orgs/{org}/public_members\",\n    \"GET /orgs/{org}/repos\",\n    \"GET /orgs/{org}/secret-scanning/alerts\",\n    \"GET /orgs/{org}/settings/billing/advanced-security\",\n    \"GET /orgs/{org}/team-sync/groups\",\n    \"GET /orgs/{org}/teams\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n    \"GET /orgs/{org}/teams/{team_slug}/members\",\n    \"GET /orgs/{org}/teams/{team_slug}/projects\",\n    \"GET /orgs/{org}/teams/{team_slug}/repos\",\n    \"GET /orgs/{org}/teams/{team_slug}/teams\",\n    \"GET /projects/columns/{column_id}/cards\",\n    \"GET /projects/{project_id}/collaborators\",\n    \"GET /projects/{project_id}/columns\",\n    \"GET /repos/{owner}/{repo}/actions/artifacts\",\n    \"GET /repos/{owner}/{repo}/actions/caches\",\n    \"GET /repos/{owner}/{repo}/actions/runners\",\n    \"GET /repos/{owner}/{repo}/actions/runs\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n    \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n    \"GET /repos/{owner}/{repo}/actions/secrets\",\n    \"GET /repos/{owner}/{repo}/actions/workflows\",\n    \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n    \"GET /repos/{owner}/{repo}/assignees\",\n    \"GET /repos/{owner}/{repo}/branches\",\n    \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n    \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n    \"GET /repos/{owner}/{repo}/code-scanning/alerts\",\n    \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n    \"GET /repos/{owner}/{repo}/code-scanning/analyses\",\n    \"GET /repos/{owner}/{repo}/codespaces\",\n    \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n    \"GET /repos/{owner}/{repo}/codespaces/secrets\",\n    \"GET /repos/{owner}/{repo}/collaborators\",\n    \"GET /repos/{owner}/{repo}/comments\",\n    \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/commits\",\n    \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/status\",\n    \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n    \"GET /repos/{owner}/{repo}/contributors\",\n    \"GET /repos/{owner}/{repo}/dependabot/secrets\",\n    \"GET /repos/{owner}/{repo}/deployments\",\n    \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    \"GET /repos/{owner}/{repo}/environments\",\n    \"GET /repos/{owner}/{repo}/events\",\n    \"GET /repos/{owner}/{repo}/forks\",\n    \"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\",\n    \"GET /repos/{owner}/{repo}/hooks\",\n    \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n    \"GET /repos/{owner}/{repo}/invitations\",\n    \"GET /repos/{owner}/{repo}/issues\",\n    \"GET /repos/{owner}/{repo}/issues/comments\",\n    \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/issues/events\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/events\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n    \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n    \"GET /repos/{owner}/{repo}/keys\",\n    \"GET /repos/{owner}/{repo}/labels\",\n    \"GET /repos/{owner}/{repo}/milestones\",\n    \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n    \"GET /repos/{owner}/{repo}/notifications\",\n    \"GET /repos/{owner}/{repo}/pages/builds\",\n    \"GET /repos/{owner}/{repo}/projects\",\n    \"GET /repos/{owner}/{repo}/pulls\",\n    \"GET /repos/{owner}/{repo}/pulls/comments\",\n    \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\",\n    \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n    \"GET /repos/{owner}/{repo}/releases\",\n    \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n    \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    \"GET /repos/{owner}/{repo}/secret-scanning/alerts\",\n    \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n    \"GET /repos/{owner}/{repo}/stargazers\",\n    \"GET /repos/{owner}/{repo}/subscribers\",\n    \"GET /repos/{owner}/{repo}/tags\",\n    \"GET /repos/{owner}/{repo}/teams\",\n    \"GET /repos/{owner}/{repo}/topics\",\n    \"GET /repositories\",\n    \"GET /repositories/{repository_id}/environments/{environment_name}/secrets\",\n    \"GET /search/code\",\n    \"GET /search/commits\",\n    \"GET /search/issues\",\n    \"GET /search/labels\",\n    \"GET /search/repositories\",\n    \"GET /search/topics\",\n    \"GET /search/users\",\n    \"GET /teams/{team_id}/discussions\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/comments\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    \"GET /teams/{team_id}/discussions/{discussion_number}/reactions\",\n    \"GET /teams/{team_id}/invitations\",\n    \"GET /teams/{team_id}/members\",\n    \"GET /teams/{team_id}/projects\",\n    \"GET /teams/{team_id}/repos\",\n    \"GET /teams/{team_id}/teams\",\n    \"GET /user/blocks\",\n    \"GET /user/codespaces\",\n    \"GET /user/codespaces/secrets\",\n    \"GET /user/emails\",\n    \"GET /user/followers\",\n    \"GET /user/following\",\n    \"GET /user/gpg_keys\",\n    \"GET /user/installations\",\n    \"GET /user/installations/{installation_id}/repositories\",\n    \"GET /user/issues\",\n    \"GET /user/keys\",\n    \"GET /user/marketplace_purchases\",\n    \"GET /user/marketplace_purchases/stubbed\",\n    \"GET /user/memberships/orgs\",\n    \"GET /user/migrations\",\n    \"GET /user/migrations/{migration_id}/repositories\",\n    \"GET /user/orgs\",\n    \"GET /user/packages\",\n    \"GET /user/packages/{package_type}/{package_name}/versions\",\n    \"GET /user/public_emails\",\n    \"GET /user/repos\",\n    \"GET /user/repository_invitations\",\n    \"GET /user/starred\",\n    \"GET /user/subscriptions\",\n    \"GET /user/teams\",\n    \"GET /users\",\n    \"GET /users/{username}/events\",\n    \"GET /users/{username}/events/orgs/{org}\",\n    \"GET /users/{username}/events/public\",\n    \"GET /users/{username}/followers\",\n    \"GET /users/{username}/following\",\n    \"GET /users/{username}/gists\",\n    \"GET /users/{username}/gpg_keys\",\n    \"GET /users/{username}/keys\",\n    \"GET /users/{username}/orgs\",\n    \"GET /users/{username}/packages\",\n    \"GET /users/{username}/projects\",\n    \"GET /users/{username}/received_events\",\n    \"GET /users/{username}/received_events/public\",\n    \"GET /users/{username}/repos\",\n    \"GET /users/{username}/starred\",\n    \"GET /users/{username}/subscriptions\",\n];\n", "import { paginatingEndpoints, } from \"./generated/paginating-endpoints\";\nexport { paginatingEndpoints } from \"./generated/paginating-endpoints\";\nexport function isPaginatingEndpoint(arg) {\n    if (typeof arg === \"string\") {\n        return paginatingEndpoints.includes(arg);\n    }\n    else {\n        return false;\n    }\n}\n", "import { VERSION } from \"./version\";\nimport { paginate } from \"./paginate\";\nimport { iterator } from \"./iterator\";\nexport { composePaginateRest } from \"./compose-paginate\";\nexport { isPaginatingEndpoint, paginatingEndpoints, } from \"./paginating-endpoints\";\n/**\n * @param octokit Octokit instance\n * @param options Options passed to Octokit constructor\n */\nexport function paginateRest(octokit) {\n    return {\n        paginate: Object.assign(paginate.bind(null, octokit), {\n            iterator: iterator.bind(null, octokit),\n        }),\n    };\n}\npaginateRest.VERSION = VERSION;\n"], "names": [], "mappings": "AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACA1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,AAAO,SAAS,8BAA8B,CAAC,QAAQ,EAAE;AACzD;AACA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;AACxB,QAAQ,OAAO;AACf,YAAY,GAAG,QAAQ;AACvB,YAAY,IAAI,EAAE,EAAE;AACpB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,MAAM,0BAA0B,GAAG,aAAa,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnG,IAAI,IAAI,CAAC,0BAA0B;AACnC,QAAQ,OAAO,QAAQ,CAAC;AACxB;AACA;AACA,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;AAC/D,IAAI,MAAM,mBAAmB,GAAG,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACnE,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;AACjD,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;AAC5C,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC;AAC9C,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;AACrC,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7C,IAAI,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;AAClD,QAAQ,QAAQ,CAAC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,OAAO,mBAAmB,KAAK,WAAW,EAAE;AACpD,QAAQ,QAAQ,CAAC,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;AACjE,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AAC3C,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;;AC7CM,SAAS,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;AACrD,IAAI,MAAM,OAAO,GAAG,OAAO,KAAK,KAAK,UAAU;AAC/C,UAAU,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;AACpC,UAAU,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACtD,IAAI,MAAM,aAAa,GAAG,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;AAChF,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AAC1B,IAAI,OAAO;AACX,QAAQ,CAAC,MAAM,CAAC,aAAa,GAAG,OAAO;AACvC,YAAY,MAAM,IAAI,GAAG;AACzB,gBAAgB,IAAI,CAAC,GAAG;AACxB,oBAAoB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC1C,gBAAgB,IAAI;AACpB,oBAAoB,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;AACnF,oBAAoB,MAAM,kBAAkB,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC;AACxF;AACA;AACA;AACA,oBAAoB,GAAG,GAAG,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,yBAAyB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9G,oBAAoB,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC;AACzD,iBAAiB;AACjB,gBAAgB,OAAO,KAAK,EAAE;AAC9B,oBAAoB,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG;AAC5C,wBAAwB,MAAM,KAAK,CAAC;AACpC,oBAAoB,GAAG,GAAG,EAAE,CAAC;AAC7B,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK,EAAE;AAC/B,4BAA4B,MAAM,EAAE,GAAG;AACvC,4BAA4B,OAAO,EAAE,EAAE;AACvC,4BAA4B,IAAI,EAAE,EAAE;AACpC,yBAAyB;AACzB,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC;AACV,KAAK,CAAC;AACN,CAAC;;ACrCM,SAAS,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE;AAC5D,IAAI,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;AAC1C,QAAQ,KAAK,GAAG,UAAU,CAAC;AAC3B,QAAQ,UAAU,GAAG,SAAS,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACpG,CAAC;AACD,SAAS,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AACnD,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AAC5C,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE;AACzB,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,SAAS,GAAG,KAAK,CAAC;AAC9B,QAAQ,SAAS,IAAI,GAAG;AACxB,YAAY,SAAS,GAAG,IAAI,CAAC;AAC7B,SAAS;AACT,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxF,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACzD,KAAK,CAAC,CAAC;AACP,CAAC;;ACrBW,MAAC,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC3D,IAAI,QAAQ;AACZ,CAAC,CAAC;;ACJU,MAAC,mBAAmB,GAAG;AACnC,IAAI,0BAA0B;AAC9B,IAAI,wBAAwB;AAC5B,IAAI,0BAA0B;AAC9B,IAAI,qBAAqB;AACzB,IAAI,iEAAiE;AACrE,IAAI,qDAAqD;AACzD,IAAI,qFAAqF;AACzF,IAAI,+EAA+E;AACnF,IAAI,+CAA+C;AACnD,IAAI,yCAAyC;AAC7C,IAAI,sDAAsD;AAC1D,IAAI,kEAAkE;AACtE,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,+BAA+B;AACnC,IAAI,8BAA8B;AAClC,IAAI,4BAA4B;AAChC,IAAI,gCAAgC;AACpC,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,gCAAgC;AACpC,IAAI,mDAAmD;AACvD,IAAI,wCAAwC;AAC5C,IAAI,2DAA2D;AAC/D,IAAI,qCAAqC;AACzC,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,mDAAmD;AACvD,IAAI,kDAAkD;AACtD,IAAI,uCAAuC;AAC3C,IAAI,sEAAsE;AAC1E,IAAI,iEAAiE;AACrE,IAAI,iCAAiC;AACrC,IAAI,iCAAiC;AACrC,IAAI,4DAA4D;AAChE,IAAI,2BAA2B;AAC/B,IAAI,wBAAwB;AAC5B,IAAI,sCAAsC;AAC1C,IAAI,4BAA4B;AAChC,IAAI,2CAA2C;AAC/C,IAAI,oCAAoC;AACxC,IAAI,+DAA+D;AACnE,IAAI,wBAAwB;AAC5B,IAAI,iCAAiC;AACrC,IAAI,oCAAoC;AACxC,IAAI,uBAAuB;AAC3B,IAAI,4CAA4C;AAChD,IAAI,+BAA+B;AACnC,IAAI,6BAA6B;AACjC,IAAI,mDAAmD;AACvD,IAAI,wBAAwB;AAC5B,IAAI,yBAAyB;AAC7B,IAAI,4BAA4B;AAChC,IAAI,wDAAwD;AAC5D,IAAI,uCAAuC;AAC3C,IAAI,0BAA0B;AAC9B,IAAI,iEAAiE;AACrE,IAAI,0BAA0B;AAC9B,IAAI,gCAAgC;AACpC,IAAI,uBAAuB;AAC3B,IAAI,wCAAwC;AAC5C,IAAI,oDAAoD;AACxD,IAAI,kCAAkC;AACtC,IAAI,uBAAuB;AAC3B,IAAI,+CAA+C;AACnD,IAAI,4EAA4E;AAChF,IAAI,uGAAuG;AAC3G,IAAI,6EAA6E;AACjF,IAAI,+CAA+C;AACnD,IAAI,2CAA2C;AAC/C,IAAI,4CAA4C;AAChD,IAAI,yCAAyC;AAC7C,IAAI,yCAAyC;AAC7C,IAAI,yCAAyC;AAC7C,IAAI,0CAA0C;AAC9C,IAAI,oCAAoC;AACxC,IAAI,6CAA6C;AACjD,IAAI,0CAA0C;AAC9C,IAAI,2CAA2C;AAC/C,IAAI,wCAAwC;AAC5C,IAAI,2DAA2D;AAC/D,IAAI,gFAAgF;AACpF,IAAI,sDAAsD;AAC1D,IAAI,2CAA2C;AAC/C,IAAI,6CAA6C;AACjD,IAAI,gEAAgE;AACpE,IAAI,qCAAqC;AACzC,IAAI,oCAAoC;AACxC,IAAI,iEAAiE;AACrE,IAAI,oEAAoE;AACxE,IAAI,gDAAgD;AACpD,IAAI,yEAAyE;AAC7E,IAAI,kDAAkD;AACtD,IAAI,sCAAsC;AAC1C,IAAI,oDAAoD;AACxD,IAAI,8CAA8C;AAClD,IAAI,yCAAyC;AAC7C,IAAI,oCAAoC;AACxC,IAAI,2DAA2D;AAC/D,IAAI,mCAAmC;AACvC,IAAI,yDAAyD;AAC7D,IAAI,sDAAsD;AAC1D,IAAI,oDAAoD;AACxD,IAAI,sDAAsD;AAC1D,IAAI,gDAAgD;AACpD,IAAI,kDAAkD;AACtD,IAAI,wCAAwC;AAC5C,IAAI,8CAA8C;AAClD,IAAI,uCAAuC;AAC3C,IAAI,gEAAgE;AACpE,IAAI,wCAAwC;AAC5C,IAAI,kCAAkC;AACtC,IAAI,iCAAiC;AACrC,IAAI,mDAAmD;AACvD,IAAI,iCAAiC;AACrC,IAAI,sDAAsD;AAC1D,IAAI,uCAAuC;AAC3C,IAAI,kCAAkC;AACtC,IAAI,2CAA2C;AAC/C,IAAI,kEAAkE;AACtE,IAAI,yCAAyC;AAC7C,IAAI,0DAA0D;AAC9D,IAAI,wDAAwD;AAC5D,IAAI,wDAAwD;AAC5D,IAAI,2DAA2D;AAC/D,IAAI,0DAA0D;AAC9D,IAAI,gCAAgC;AACpC,IAAI,kCAAkC;AACtC,IAAI,sCAAsC;AAC1C,IAAI,gEAAgE;AACpE,IAAI,yCAAyC;AAC7C,IAAI,wCAAwC;AAC5C,IAAI,oCAAoC;AACxC,IAAI,iCAAiC;AACrC,IAAI,0CAA0C;AAC9C,IAAI,iEAAiE;AACrE,IAAI,wDAAwD;AAC5D,IAAI,uDAAuD;AAC3D,IAAI,qDAAqD;AACzD,IAAI,mEAAmE;AACvE,IAAI,uDAAuD;AAC3D,IAAI,4EAA4E;AAChF,IAAI,oCAAoC;AACxC,IAAI,wDAAwD;AAC5D,IAAI,2DAA2D;AAC/D,IAAI,kDAAkD;AACtD,IAAI,2EAA2E;AAC/E,IAAI,sCAAsC;AAC1C,IAAI,uCAAuC;AAC3C,IAAI,gCAAgC;AACpC,IAAI,iCAAiC;AACrC,IAAI,kCAAkC;AACtC,IAAI,mBAAmB;AACvB,IAAI,2EAA2E;AAC/E,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,oBAAoB;AACxB,IAAI,0BAA0B;AAC9B,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,kCAAkC;AACtC,IAAI,+DAA+D;AACnE,IAAI,0FAA0F;AAC9F,IAAI,gEAAgE;AACpE,IAAI,kCAAkC;AACtC,IAAI,8BAA8B;AAClC,IAAI,+BAA+B;AACnC,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,kBAAkB;AACtB,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,IAAI,kBAAkB;AACtB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,IAAI,yBAAyB;AAC7B,IAAI,wDAAwD;AAC5D,IAAI,kBAAkB;AACtB,IAAI,gBAAgB;AACpB,IAAI,iCAAiC;AACrC,IAAI,yCAAyC;AAC7C,IAAI,4BAA4B;AAChC,IAAI,sBAAsB;AAC1B,IAAI,kDAAkD;AACtD,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,2DAA2D;AAC/D,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,kCAAkC;AACtC,IAAI,mBAAmB;AACvB,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,8BAA8B;AAClC,IAAI,yCAAyC;AAC7C,IAAI,qCAAqC;AACzC,IAAI,iCAAiC;AACrC,IAAI,iCAAiC;AACrC,IAAI,6BAA6B;AACjC,IAAI,gCAAgC;AACpC,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,gCAAgC;AACpC,IAAI,gCAAgC;AACpC,IAAI,uCAAuC;AAC3C,IAAI,8CAA8C;AAClD,IAAI,6BAA6B;AACjC,IAAI,+BAA+B;AACnC,IAAI,qCAAqC;AACzC,CAAC;;ACrNM,SAAS,oBAAoB,CAAC,GAAG,EAAE;AAC1C,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACjC,QAAQ,OAAO,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjD,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;;ACJD;AACA;AACA;AACA;AACA,AAAO,SAAS,YAAY,CAAC,OAAO,EAAE;AACtC,IAAI,OAAO;AACX,QAAQ,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;AAC9D,YAAY,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AAClD,SAAS,CAAC;AACV,KAAK,CAAC;AACN,CAAC;AACD,YAAY,CAAC,OAAO,GAAG,OAAO,CAAC;;;;"}