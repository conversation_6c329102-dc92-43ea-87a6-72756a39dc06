{"version": 3, "file": "index.js", "sources": ["../dist-src/util/lowercase-keys.js", "../dist-src/util/merge-deep.js", "../dist-src/util/remove-undefined-properties.js", "../dist-src/merge.js", "../dist-src/util/add-query-parameters.js", "../dist-src/util/extract-url-variable-names.js", "../dist-src/util/omit.js", "../dist-src/util/url-template.js", "../dist-src/parse.js", "../dist-src/endpoint-with-defaults.js", "../dist-src/with-defaults.js", "../dist-src/version.js", "../dist-src/defaults.js", "../dist-src/index.js"], "sourcesContent": ["export function lowercaseKeys(object) {\n    if (!object) {\n        return {};\n    }\n    return Object.keys(object).reduce((newObj, key) => {\n        newObj[key.toLowerCase()] = object[key];\n        return newObj;\n    }, {});\n}\n", "import { isPlainObject } from \"is-plain-object\";\nexport function mergeDeep(defaults, options) {\n    const result = Object.assign({}, defaults);\n    Object.keys(options).forEach((key) => {\n        if (isPlainObject(options[key])) {\n            if (!(key in defaults))\n                Object.assign(result, { [key]: options[key] });\n            else\n                result[key] = mergeDeep(defaults[key], options[key]);\n        }\n        else {\n            Object.assign(result, { [key]: options[key] });\n        }\n    });\n    return result;\n}\n", "export function removeUndefinedProperties(obj) {\n    for (const key in obj) {\n        if (obj[key] === undefined) {\n            delete obj[key];\n        }\n    }\n    return obj;\n}\n", "import { lowercaseKeys } from \"./util/lowercase-keys\";\nimport { mergeDeep } from \"./util/merge-deep\";\nimport { removeUndefinedProperties } from \"./util/remove-undefined-properties\";\nexport function merge(defaults, route, options) {\n    if (typeof route === \"string\") {\n        let [method, url] = route.split(\" \");\n        options = Object.assign(url ? { method, url } : { url: method }, options);\n    }\n    else {\n        options = Object.assign({}, route);\n    }\n    // lowercase header names before merging with defaults to avoid duplicates\n    options.headers = lowercaseKeys(options.headers);\n    // remove properties with undefined values before merging\n    removeUndefinedProperties(options);\n    removeUndefinedProperties(options.headers);\n    const mergedOptions = mergeDeep(defaults || {}, options);\n    // mediaType.previews arrays are merged, instead of overwritten\n    if (defaults && defaults.mediaType.previews.length) {\n        mergedOptions.mediaType.previews = defaults.mediaType.previews\n            .filter((preview) => !mergedOptions.mediaType.previews.includes(preview))\n            .concat(mergedOptions.mediaType.previews);\n    }\n    mergedOptions.mediaType.previews = mergedOptions.mediaType.previews.map((preview) => preview.replace(/-preview/, \"\"));\n    return mergedOptions;\n}\n", "export function addQueryParameters(url, parameters) {\n    const separator = /\\?/.test(url) ? \"&\" : \"?\";\n    const names = Object.keys(parameters);\n    if (names.length === 0) {\n        return url;\n    }\n    return (url +\n        separator +\n        names\n            .map((name) => {\n            if (name === \"q\") {\n                return (\"q=\" + parameters.q.split(\"+\").map(encodeURIComponent).join(\"+\"));\n            }\n            return `${name}=${encodeURIComponent(parameters[name])}`;\n        })\n            .join(\"&\"));\n}\n", "const urlVariableRegex = /\\{[^}]+\\}/g;\nfunction removeNonChars(variableName) {\n    return variableName.replace(/^\\W+|\\W+$/g, \"\").split(/,/);\n}\nexport function extractUrlVariableNames(url) {\n    const matches = url.match(urlVariableRegex);\n    if (!matches) {\n        return [];\n    }\n    return matches.map(removeNonChars).reduce((a, b) => a.concat(b), []);\n}\n", "export function omit(object, keysToOmit) {\n    return Object.keys(object)\n        .filter((option) => !keysToOmit.includes(option))\n        .reduce((obj, key) => {\n        obj[key] = object[key];\n        return obj;\n    }, {});\n}\n", "// Based on https://github.com/bramstein/url-template, licensed under BSD\n// TODO: create separate package.\n//\n// Copyright (c) 2012-2014, <PERSON> Stein\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions\n// are met:\n//  1. Redistributions of source code must retain the above copyright\n//     notice, this list of conditions and the following disclaimer.\n//  2. Redistributions in binary form must reproduce the above copyright\n//     notice, this list of conditions and the following disclaimer in the\n//     documentation and/or other materials provided with the distribution.\n//  3. The name of the author may not be used to endorse or promote products\n//     derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE AUTHOR \"AS IS\" AND ANY EXPRESS OR IMPLIED\n// WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO\n// EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n// INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,\n// BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY\n// OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n// NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,\n// EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n/* istanbul ignore file */\nfunction encodeReserved(str) {\n    return str\n        .split(/(%[0-9A-Fa-f]{2})/g)\n        .map(function (part) {\n        if (!/%[0-9A-Fa-f]/.test(part)) {\n            part = encodeURI(part).replace(/%5B/g, \"[\").replace(/%5D/g, \"]\");\n        }\n        return part;\n    })\n        .join(\"\");\n}\nfunction encodeUnreserved(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {\n        return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n    });\n}\nfunction encodeValue(operator, value, key) {\n    value =\n        operator === \"+\" || operator === \"#\"\n            ? encodeReserved(value)\n            : encodeUnreserved(value);\n    if (key) {\n        return encodeUnreserved(key) + \"=\" + value;\n    }\n    else {\n        return value;\n    }\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\nfunction isKeyOperator(operator) {\n    return operator === \";\" || operator === \"&\" || operator === \"?\";\n}\nfunction getValues(context, operator, key, modifier) {\n    var value = context[key], result = [];\n    if (isDefined(value) && value !== \"\") {\n        if (typeof value === \"string\" ||\n            typeof value === \"number\" ||\n            typeof value === \"boolean\") {\n            value = value.toString();\n            if (modifier && modifier !== \"*\") {\n                value = value.substring(0, parseInt(modifier, 10));\n            }\n            result.push(encodeValue(operator, value, isKeyOperator(operator) ? key : \"\"));\n        }\n        else {\n            if (modifier === \"*\") {\n                if (Array.isArray(value)) {\n                    value.filter(isDefined).forEach(function (value) {\n                        result.push(encodeValue(operator, value, isKeyOperator(operator) ? key : \"\"));\n                    });\n                }\n                else {\n                    Object.keys(value).forEach(function (k) {\n                        if (isDefined(value[k])) {\n                            result.push(encodeValue(operator, value[k], k));\n                        }\n                    });\n                }\n            }\n            else {\n                const tmp = [];\n                if (Array.isArray(value)) {\n                    value.filter(isDefined).forEach(function (value) {\n                        tmp.push(encodeValue(operator, value));\n                    });\n                }\n                else {\n                    Object.keys(value).forEach(function (k) {\n                        if (isDefined(value[k])) {\n                            tmp.push(encodeUnreserved(k));\n                            tmp.push(encodeValue(operator, value[k].toString()));\n                        }\n                    });\n                }\n                if (isKeyOperator(operator)) {\n                    result.push(encodeUnreserved(key) + \"=\" + tmp.join(\",\"));\n                }\n                else if (tmp.length !== 0) {\n                    result.push(tmp.join(\",\"));\n                }\n            }\n        }\n    }\n    else {\n        if (operator === \";\") {\n            if (isDefined(value)) {\n                result.push(encodeUnreserved(key));\n            }\n        }\n        else if (value === \"\" && (operator === \"&\" || operator === \"?\")) {\n            result.push(encodeUnreserved(key) + \"=\");\n        }\n        else if (value === \"\") {\n            result.push(\"\");\n        }\n    }\n    return result;\n}\nexport function parseUrl(template) {\n    return {\n        expand: expand.bind(null, template),\n    };\n}\nfunction expand(template, context) {\n    var operators = [\"+\", \"#\", \".\", \"/\", \";\", \"?\", \"&\"];\n    return template.replace(/\\{([^\\{\\}]+)\\}|([^\\{\\}]+)/g, function (_, expression, literal) {\n        if (expression) {\n            let operator = \"\";\n            const values = [];\n            if (operators.indexOf(expression.charAt(0)) !== -1) {\n                operator = expression.charAt(0);\n                expression = expression.substr(1);\n            }\n            expression.split(/,/g).forEach(function (variable) {\n                var tmp = /([^:\\*]*)(?::(\\d+)|(\\*))?/.exec(variable);\n                values.push(getValues(context, operator, tmp[1], tmp[2] || tmp[3]));\n            });\n            if (operator && operator !== \"+\") {\n                var separator = \",\";\n                if (operator === \"?\") {\n                    separator = \"&\";\n                }\n                else if (operator !== \"#\") {\n                    separator = operator;\n                }\n                return (values.length !== 0 ? operator : \"\") + values.join(separator);\n            }\n            else {\n                return values.join(\",\");\n            }\n        }\n        else {\n            return encodeReserved(literal);\n        }\n    });\n}\n", "import { addQueryParameters } from \"./util/add-query-parameters\";\nimport { extractUrlVariableNames } from \"./util/extract-url-variable-names\";\nimport { omit } from \"./util/omit\";\nimport { parseUrl } from \"./util/url-template\";\nexport function parse(options) {\n    // https://fetch.spec.whatwg.org/#methods\n    let method = options.method.toUpperCase();\n    // replace :varname with {varname} to make it RFC 6570 compatible\n    let url = (options.url || \"/\").replace(/:([a-z]\\w+)/g, \"{$1}\");\n    let headers = Object.assign({}, options.headers);\n    let body;\n    let parameters = omit(options, [\n        \"method\",\n        \"baseUrl\",\n        \"url\",\n        \"headers\",\n        \"request\",\n        \"mediaType\",\n    ]);\n    // extract variable names from URL to calculate remaining variables later\n    const urlVariableNames = extractUrlVariableNames(url);\n    url = parseUrl(url).expand(parameters);\n    if (!/^http/.test(url)) {\n        url = options.baseUrl + url;\n    }\n    const omittedParameters = Object.keys(options)\n        .filter((option) => urlVariableNames.includes(option))\n        .concat(\"baseUrl\");\n    const remainingParameters = omit(parameters, omittedParameters);\n    const isBinaryRequest = /application\\/octet-stream/i.test(headers.accept);\n    if (!isBinaryRequest) {\n        if (options.mediaType.format) {\n            // e.g. application/vnd.github.v3+json => application/vnd.github.v3.raw\n            headers.accept = headers.accept\n                .split(/,/)\n                .map((preview) => preview.replace(/application\\/vnd(\\.\\w+)(\\.v3)?(\\.\\w+)?(\\+json)?$/, `application/vnd$1$2.${options.mediaType.format}`))\n                .join(\",\");\n        }\n        if (options.mediaType.previews.length) {\n            const previewsFromAcceptHeader = headers.accept.match(/[\\w-]+(?=-preview)/g) || [];\n            headers.accept = previewsFromAcceptHeader\n                .concat(options.mediaType.previews)\n                .map((preview) => {\n                const format = options.mediaType.format\n                    ? `.${options.mediaType.format}`\n                    : \"+json\";\n                return `application/vnd.github.${preview}-preview${format}`;\n            })\n                .join(\",\");\n        }\n    }\n    // for GET/HEAD requests, set URL query parameters from remaining parameters\n    // for PATCH/POST/PUT/DELETE requests, set request body from remaining parameters\n    if ([\"GET\", \"HEAD\"].includes(method)) {\n        url = addQueryParameters(url, remainingParameters);\n    }\n    else {\n        if (\"data\" in remainingParameters) {\n            body = remainingParameters.data;\n        }\n        else {\n            if (Object.keys(remainingParameters).length) {\n                body = remainingParameters;\n            }\n            else {\n                headers[\"content-length\"] = 0;\n            }\n        }\n    }\n    // default content-type for JSON if body is set\n    if (!headers[\"content-type\"] && typeof body !== \"undefined\") {\n        headers[\"content-type\"] = \"application/json; charset=utf-8\";\n    }\n    // GitHub expects 'content-length: 0' header for PUT/PATCH requests without body.\n    // fetch does not allow to set `content-length` header, but we can set body to an empty string\n    if ([\"PATCH\", \"PUT\"].includes(method) && typeof body === \"undefined\") {\n        body = \"\";\n    }\n    // Only return body/request keys if present\n    return Object.assign({ method, url, headers }, typeof body !== \"undefined\" ? { body } : null, options.request ? { request: options.request } : null);\n}\n", "import { merge } from \"./merge\";\nimport { parse } from \"./parse\";\nexport function endpointWithDefaults(defaults, route, options) {\n    return parse(merge(defaults, route, options));\n}\n", "import { endpointWithDefaults } from \"./endpoint-with-defaults\";\nimport { merge } from \"./merge\";\nimport { parse } from \"./parse\";\nexport function withDefaults(oldDefaults, newDefaults) {\n    const DEFAULTS = merge(oldDefaults, newDefaults);\n    const endpoint = endpointWithDefaults.bind(null, DEFAULTS);\n    return Object.assign(endpoint, {\n        DEFAULTS,\n        defaults: withDefaults.bind(null, DEFAULTS),\n        merge: merge.bind(null, DEFAULTS),\n        parse,\n    });\n}\n", "export const VERSION = \"6.0.12\";\n", "import { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nconst userAgent = `octokit-endpoint.js/${VERSION} ${getUserAgent()}`;\n// DEFAULTS has all properties set that EndpointOptions has, except url.\n// So we use RequestParameters and add method as additional required property.\nexport const DEFAULTS = {\n    method: \"GET\",\n    baseUrl: \"https://api.github.com\",\n    headers: {\n        accept: \"application/vnd.github.v3+json\",\n        \"user-agent\": userAgent,\n    },\n    mediaType: {\n        format: \"\",\n        previews: [],\n    },\n};\n", "import { withDefaults } from \"./with-defaults\";\nimport { DEFAULTS } from \"./defaults\";\nexport const endpoint = withDefaults(null, DEFAULTS);\n"], "names": [], "mappings": ";;;AAAO,SAAS,aAAa,CAAC,MAAM,EAAE;AACtC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK;AACvD,QAAQ,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAChD,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX;;ACPO,SAAS,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC7C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC1C,QAAQ,IAAI,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AACzC,YAAY,IAAI,EAAE,GAAG,IAAI,QAAQ,CAAC;AAClC,gBAAgB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC/D;AACA,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AACrE,SAAS;AACT,aAAa;AACb,YAAY,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3D,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;;ACfM,SAAS,yBAAyB,CAAC,GAAG,EAAE;AAC/C,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AAC3B,QAAQ,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;AACpC,YAAY,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,SAAS;AACT,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;;ACJM,SAAS,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;AAChD,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAQ,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;AAClF,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC3C,KAAK;AACL;AACA,IAAI,OAAO,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD;AACA,IAAI,yBAAyB,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,yBAAyB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/C,IAAI,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;AAC7D;AACA,IAAI,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;AACxD,QAAQ,aAAa,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ;AACtE,aAAa,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrF,aAAa,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,GAAG,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1H,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC;;ACzBM,SAAS,kBAAkB,CAAC,GAAG,EAAE,UAAU,EAAE;AACpD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACjD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,QAAQ,GAAG;AACf,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,aAAa,GAAG,CAAC,CAAC,IAAI,KAAK;AAC3B,YAAY,IAAI,IAAI,KAAK,GAAG,EAAE;AAC9B,gBAAgB,QAAQ,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC1F,aAAa;AACb,YAAY,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,SAAS,CAAC;AACV,aAAa,IAAI,CAAC,GAAG,CAAC,EAAE;AACxB,CAAC;;AChBD,MAAM,gBAAgB,GAAG,YAAY,CAAC;AACtC,SAAS,cAAc,CAAC,YAAY,EAAE;AACtC,IAAI,OAAO,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7D,CAAC;AACD,AAAO,SAAS,uBAAuB,CAAC,GAAG,EAAE;AAC7C,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACzE,CAAC;;ACVM,SAAS,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE;AACzC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC9B,SAAS,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzD,SAAS,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AAC9B,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;;ACPD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,GAAG,EAAE;AAC7B,IAAI,OAAO,GAAG;AACd,SAAS,KAAK,CAAC,oBAAoB,CAAC;AACpC,SAAS,GAAG,CAAC,UAAU,IAAI,EAAE;AAC7B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACxC,YAAY,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC7E,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,CAAC;AACD,SAAS,gBAAgB,CAAC,GAAG,EAAE;AAC/B,IAAI,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AACpE,QAAQ,OAAO,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;AAChE,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;AAC3C,IAAI,KAAK;AACT,QAAQ,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5C,cAAc,cAAc,CAAC,KAAK,CAAC;AACnC,cAAc,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACtC,IAAI,IAAI,GAAG,EAAE;AACb,QAAQ,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACnD,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,IAAI,OAAO,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC;AACjD,CAAC;AACD,SAAS,aAAa,CAAC,QAAQ,EAAE;AACjC,IAAI,OAAO,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,CAAC;AACpE,CAAC;AACD,SAAS,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE;AACrD,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAC1C,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,EAAE;AAC1C,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ;AACrC,YAAY,OAAO,KAAK,KAAK,QAAQ;AACrC,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE;AACxC,YAAY,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACrC,YAAY,IAAI,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;AAC9C,gBAAgB,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACnE,aAAa;AACb,YAAY,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1F,SAAS;AACT,aAAa;AACb,YAAY,IAAI,QAAQ,KAAK,GAAG,EAAE;AAClC,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AACrE,wBAAwB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;AACtG,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC5D,wBAAwB,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACjD,4BAA4B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5E,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,GAAG,EAAE,CAAC;AAC/B,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AACrE,wBAAwB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AAC/D,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC5D,wBAAwB,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AACjD,4BAA4B,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,4BAA4B,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACjF,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,gBAAgB,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC7C,oBAAoB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7E,iBAAiB;AACjB,qBAAqB,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3C,oBAAoB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;AAC9B,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;AAClC,gBAAgB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,aAAa;AACb,SAAS;AACT,aAAa,IAAI,KAAK,KAAK,EAAE,KAAK,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE;AACzE,YAAY,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACrD,SAAS;AACT,aAAa,IAAI,KAAK,KAAK,EAAE,EAAE;AAC/B,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5B,SAAS;AACT,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,AAAO,SAAS,QAAQ,CAAC,QAAQ,EAAE;AACnC,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC3C,KAAK,CAAC;AACN,CAAC;AACD,SAAS,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE;AACnC,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE;AAC5F,QAAQ,IAAI,UAAU,EAAE;AACxB,YAAY,IAAI,QAAQ,GAAG,EAAE,CAAC;AAC9B,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC;AAC9B,YAAY,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;AAChE,gBAAgB,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChD,gBAAgB,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClD,aAAa;AACb,YAAY,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AAC/D,gBAAgB,IAAI,GAAG,GAAG,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrE,gBAAgB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpF,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;AAC9C,gBAAgB,IAAI,SAAS,GAAG,GAAG,CAAC;AACpC,gBAAgB,IAAI,QAAQ,KAAK,GAAG,EAAE;AACtC,oBAAoB,SAAS,GAAG,GAAG,CAAC;AACpC,iBAAiB;AACjB,qBAAqB,IAAI,QAAQ,KAAK,GAAG,EAAE;AAC3C,oBAAoB,SAAS,GAAG,QAAQ,CAAC;AACzC,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtF,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;AAC3C,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC;;AC/JM,SAAS,KAAK,CAAC,OAAO,EAAE;AAC/B;AACA,IAAI,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;AAC9C;AACA,IAAI,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACnE,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,IAAI,IAAI,CAAC;AACb,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE;AACnC,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,SAAS;AACjB,QAAQ,WAAW;AACnB,KAAK,CAAC,CAAC;AACP;AACA,IAAI,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC5B,QAAQ,GAAG,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;AACpC,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAClD,SAAS,MAAM,CAAC,CAAC,MAAM,KAAK,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9D,SAAS,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3B,IAAI,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;AACpE,IAAI,MAAM,eAAe,GAAG,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;AACtC;AACA,YAAY,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;AAC3C,iBAAiB,KAAK,CAAC,GAAG,CAAC;AAC3B,iBAAiB,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,kDAAkD,EAAE,CAAC,oBAAoB,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACzJ,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC/C,YAAY,MAAM,wBAAwB,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;AAC/F,YAAY,OAAO,CAAC,MAAM,GAAG,wBAAwB;AACrD,iBAAiB,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;AACnD,iBAAiB,GAAG,CAAC,CAAC,OAAO,KAAK;AAClC,gBAAgB,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM;AACvD,sBAAsB,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACpD,sBAAsB,OAAO,CAAC;AAC9B,gBAAgB,OAAO,CAAC,uBAAuB,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AAC5E,aAAa,CAAC;AACd,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,SAAS;AACT,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1C,QAAQ,GAAG,GAAG,kBAAkB,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;AAC3D,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,MAAM,IAAI,mBAAmB,EAAE;AAC3C,YAAY,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC;AAC5C,SAAS;AACT,aAAa;AACb,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE;AACzD,gBAAgB,IAAI,GAAG,mBAAmB,CAAC;AAC3C,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC9C,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AACjE,QAAQ,OAAO,CAAC,cAAc,CAAC,GAAG,iCAAiC,CAAC;AACpE,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC1E,QAAQ,IAAI,GAAG,EAAE,CAAC;AAClB,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,IAAI,KAAK,WAAW,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACzJ,CAAC;;AC9EM,SAAS,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;AAC/D,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;;ACDM,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE;AACvD,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACrD,IAAI,MAAM,QAAQ,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC/D,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;AACnC,QAAQ,QAAQ;AAChB,QAAQ,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AACnD,QAAQ,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AACzC,QAAQ,KAAK;AACb,KAAK,CAAC,CAAC;AACP,CAAC;;ACZM,MAAM,OAAO,GAAG,mBAAmB,CAAC;;ACE3C,MAAM,SAAS,GAAG,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AACrE;AACA;AACA,AAAO,MAAM,QAAQ,GAAG;AACxB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,OAAO,EAAE,wBAAwB;AACrC,IAAI,OAAO,EAAE;AACb,QAAQ,MAAM,EAAE,gCAAgC;AAChD,QAAQ,YAAY,EAAE,SAAS;AAC/B,KAAK;AACL,IAAI,SAAS,EAAE;AACf,QAAQ,MAAM,EAAE,EAAE;AAClB,QAAQ,QAAQ,EAAE,EAAE;AACpB,KAAK;AACL,CAAC,CAAC;;ACdU,MAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;;;;"}