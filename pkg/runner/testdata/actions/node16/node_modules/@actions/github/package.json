{"name": "@actions/github", "version": "4.0.0", "description": "Actions github lib", "keywords": ["github", "actions"], "homepage": "https://github.com/actions/toolkit/tree/master/packages/github", "license": "MIT", "main": "lib/github.js", "types": "lib/github.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib", "!.DS_Store"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/actions/toolkit.git", "directory": "packages/github"}, "scripts": {"audit-moderate": "npm install && npm audit --audit-level=moderate", "test": "jest", "build": "tsc", "format": "prettier --write **/*.ts", "format-check": "prettier --check **/*.ts", "tsc": "tsc"}, "bugs": {"url": "https://github.com/actions/toolkit/issues"}, "dependencies": {"@actions/http-client": "^1.0.8", "@octokit/core": "^3.0.0", "@octokit/plugin-paginate-rest": "^2.2.3", "@octokit/plugin-rest-endpoint-methods": "^4.0.0"}, "devDependencies": {"jest": "^25.1.0", "proxy": "^1.0.1"}}