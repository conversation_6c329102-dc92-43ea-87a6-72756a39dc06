{"name": "@actions/http-client", "version": "1.0.11", "description": "Actions Http Client", "main": "index.js", "scripts": {"build": "rm -Rf ./_out && tsc && cp package*.json ./_out && cp *.md ./_out && cp LICENSE ./_out && cp actions.png ./_out", "test": "jest", "format": "prettier --write *.ts && prettier --write **/*.ts", "format-check": "prettier --check *.ts && prettier --check **/*.ts", "audit-check": "npm audit --audit-level=moderate"}, "repository": {"type": "git", "url": "git+https://github.com/actions/http-client.git"}, "keywords": ["Actions", "Http"], "author": "GitHub, Inc.", "license": "MIT", "bugs": {"url": "https://github.com/actions/http-client/issues"}, "homepage": "https://github.com/actions/http-client#readme", "devDependencies": {"@types/jest": "^25.1.4", "@types/node": "^12.12.31", "jest": "^25.1.0", "prettier": "^2.0.4", "proxy": "^1.0.1", "ts-jest": "^25.2.1", "typescript": "^3.8.3"}, "dependencies": {"tunnel": "0.0.6"}}