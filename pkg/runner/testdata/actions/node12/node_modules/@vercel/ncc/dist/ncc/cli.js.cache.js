module.exports=(()=>{var __webpack_modules__={306:t=>{"use strict";t.exports=JSON.parse('{"name":"@vercel/ncc","description":"Simple CLI for compiling a Node.js module into a single file, together with all its dependencies, gcc-style.","version":"0.24.1","repository":"vercel/ncc","license":"MIT","main":"./dist/ncc/index.js","bin":{"ncc":"./dist/ncc/cli.js"},"scripts":{"build":"node scripts/build","build-test-binary":"cd test/binary && node-gyp rebuild && cp build/Release/hello.node ../integration/hello.node","codecov":"codecov","test":"node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest","test-coverage":"node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest --coverage --globals \\"{\\\\\\"coverage\\\\\\":true}\\" && codecov","prepublish":"yarn build"},"devDependencies":{"@azure/cosmos":"^2.0.5","@bugsnag/js":"^5.0.1","@ffmpeg-installer/ffmpeg":"^1.0.17","@google-cloud/bigquery":"^2.0.1","@google-cloud/firestore":"^2.2.0","@sentry/node":"^4.3.0","@tensorflow/tfjs-node":"^0.3.0","@zeit/webpack-asset-relocator-loader":"0.8.0","analytics-node":"^3.3.0","apollo-server-express":"^2.2.2","arg":"^4.1.0","auth0":"^2.14.0","aws-sdk":"^2.356.0","axios":"^0.18.1","azure-storage":"^2.10.2","browserify-middleware":"^8.1.1","bytes":"^3.0.0","canvas":"^2.2.0","chromeless":"^1.5.2","codecov":"^3.6.5","consolidate":"^0.15.1","copy":"^0.3.2","core-js":"^2.5.7","cowsay":"^1.3.1","esm":"^3.2.22","express":"^4.16.4","fetch-h2":"^1.0.2","firebase":"^6.1.1","firebase-admin":"^6.3.0","fluent-ffmpeg":"^2.1.2","fontkit":"^1.7.7","get-folder-size":"^2.0.0","glob":"^7.1.3","got":"^9.3.2","graceful-fs":"^4.1.15","graphql":"^14.0.2","highlights":"^3.1.1","hot-shots":"^5.9.2","in-publish":"^2.0.0","ioredis":"^4.2.0","isomorphic-unfetch":"^3.0.0","jest":"^26.3.0","jimp":"^0.5.6","jugglingdb":"2.0.1","koa":"^2.6.2","leveldown":"^5.6.0","license-webpack-plugin":"^2.3.0","lighthouse":"^5.0.0","loopback":"^3.24.0","mailgun":"^0.5.0","mariadb":"^2.0.1-beta","memcached":"^2.2.2","mkdirp":"^0.5.1","mongoose":"^5.3.12","mysql":"^2.16.0","node-gyp":"^3.8.0","npm":"^6.13.4","oracledb":"^4.2.0","passport":"^0.4.0","passport-google-oauth":"^1.0.0","path-platform":"^0.11.15","pdf2json":"^1.1.8","pdfkit":"^0.8.3","pg":"^7.6.1","pug":"^2.0.3","react":"^16.6.3","react-dom":"^16.6.3","redis":"^2.8.0","request":"^2.88.0","rxjs":"^6.3.3","saslprep":"^1.0.2","sequelize":"^5.8.6","sharp":"^0.25.2","shebang-loader":"^0.0.1","socket.io":"^2.2.0","source-map-support":"^0.5.9","stripe":"^6.15.0","swig":"^1.4.2","terser":"^3.11.0","the-answer":"^1.0.0","tiny-json-http":"^7.0.2","ts-loader":"^5.3.1","tsconfig-paths":"^3.7.0","tsconfig-paths-webpack-plugin":"^3.2.0","twilio":"^3.23.2","typescript":"^3.2.2","vm2":"^3.6.6","vue":"^2.5.17","vue-server-renderer":"^2.5.17","webpack":"5.0.0-beta.28","when":"^3.7.8"}}')},832:t=>{const e=Symbol("arg flag");function arg(t,{argv:r,permissive:n=false,stopAtPositional:i=false}={}){if(!t){throw new Error("Argument specification object is required")}const o={_:[]};r=r||process.argv.slice(2);const s={};const a={};for(const r of Object.keys(t)){if(!r){throw new TypeError("Argument key cannot be an empty string")}if(r[0]!=="-"){throw new TypeError(`Argument key must start with '-' but found: '${r}'`)}if(r.length===1){throw new TypeError(`Argument key must have a name; singular '-' keys are not allowed: ${r}`)}if(typeof t[r]==="string"){s[r]=t[r];continue}let n=t[r];let i=false;if(Array.isArray(n)&&n.length===1&&typeof n[0]==="function"){const[t]=n;n=((e,r,n=[])=>{n.push(t(e,r,n[n.length-1]));return n});i=t===Boolean||t[e]===true}else if(typeof n==="function"){i=n===Boolean||n[e]===true}else{throw new TypeError(`Type missing or not a function or valid array type: ${r}`)}if(r[1]!=="-"&&r.length>2){throw new TypeError(`Short argument keys (with a single hyphen) must have only one character: ${r}`)}a[r]=[n,i]}for(let t=0,e=r.length;t<e;t++){const e=r[t];if(i&&o._.length>0){o._=o._.concat(r.slice(t));break}if(e==="--"){o._=o._.concat(r.slice(t+1));break}if(e.length>1&&e[0]==="-"){const i=e[1]==="-"||e.length===2?[e]:e.slice(1).split("").map(t=>`-${t}`);for(let e=0;e<i.length;e++){const c=i[e];const[u,f]=c[1]==="-"?c.split("=",2):[c,undefined];let h=u;while(h in s){h=s[h]}if(!(h in a)){if(n){o._.push(c);continue}else{const t=new Error(`Unknown or unexpected option: ${u}`);t.code="ARG_UNKNOWN_OPTION";throw t}}const[l,p]=a[h];if(!p&&e+1<i.length){throw new TypeError(`Option requires argument (but was followed by another short argument): ${u}`)}if(p){o[h]=l(true,h,o[h])}else if(f===undefined){if(r.length<t+2||r[t+1].length>1&&r[t+1][0]==="-"){const t=u===h?"":` (alias for ${h})`;throw new Error(`Option requires argument: ${u}${t}`)}o[h]=l(r[t+1],h,o[h]);++t}else{o[h]=l(f,h,o[h])}}}else{o._.push(e)}}return o}arg.flag=(t=>{t[e]=true;return t});arg.COUNT=arg.flag((t,e,r)=>(r||0)+1);t.exports=arg},835:t=>{"use strict";t.exports=balanced;function balanced(t,e,r){if(t instanceof RegExp)t=maybeMatch(t,r);if(e instanceof RegExp)e=maybeMatch(e,r);var n=range(t,e,r);return n&&{start:n[0],end:n[1],pre:r.slice(0,n[0]),body:r.slice(n[0]+t.length,n[1]),post:r.slice(n[1]+e.length)}}function maybeMatch(t,e){var r=e.match(t);return r?r[0]:null}balanced.range=range;function range(t,e,r){var n,i,o,s,a;var c=r.indexOf(t);var u=r.indexOf(e,c+1);var f=c;if(c>=0&&u>0){n=[];o=r.length;while(f>=0&&!a){if(f==c){n.push(f);c=r.indexOf(t,f+1)}else if(n.length==1){a=[n.pop(),u]}else{i=n.pop();if(i<o){o=i;s=u}u=r.indexOf(e,f+1)}f=c<u&&c>=0?c:u}if(n.length){a=[o,s]}}return a}},215:(t,e,r)=>{var n=r(551);var i=r(835);t.exports=expandTop;var o="\0SLASH"+Math.random()+"\0";var s="\0OPEN"+Math.random()+"\0";var a="\0CLOSE"+Math.random()+"\0";var c="\0COMMA"+Math.random()+"\0";var u="\0PERIOD"+Math.random()+"\0";function numeric(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function escapeBraces(t){return t.split("\\\\").join(o).split("\\{").join(s).split("\\}").join(a).split("\\,").join(c).split("\\.").join(u)}function unescapeBraces(t){return t.split(o).join("\\").split(s).join("{").split(a).join("}").split(c).join(",").split(u).join(".")}function parseCommaParts(t){if(!t)return[""];var e=[];var r=i("{","}",t);if(!r)return t.split(",");var n=r.pre;var o=r.body;var s=r.post;var a=n.split(",");a[a.length-1]+="{"+o+"}";var c=parseCommaParts(s);if(s.length){a[a.length-1]+=c.shift();a.push.apply(a,c)}e.push.apply(e,a);return e}function expandTop(t){if(!t)return[];if(t.substr(0,2)==="{}"){t="\\{\\}"+t.substr(2)}return expand(escapeBraces(t),true).map(unescapeBraces)}function identity(t){return t}function embrace(t){return"{"+t+"}"}function isPadded(t){return/^-?0\d/.test(t)}function lte(t,e){return t<=e}function gte(t,e){return t>=e}function expand(t,e){var r=[];var o=i("{","}",t);if(!o||/\$$/.test(o.pre))return[t];var s=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(o.body);var c=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(o.body);var u=s||c;var f=o.body.indexOf(",")>=0;if(!u&&!f){if(o.post.match(/,.*\}/)){t=o.pre+"{"+o.body+a+o.post;return expand(t)}return[t]}var h;if(u){h=o.body.split(/\.\./)}else{h=parseCommaParts(o.body);if(h.length===1){h=expand(h[0],false).map(embrace);if(h.length===1){var l=o.post.length?expand(o.post,false):[""];return l.map(function(t){return o.pre+h[0]+t})}}}var p=o.pre;var l=o.post.length?expand(o.post,false):[""];var d;if(u){var v=numeric(h[0]);var m=numeric(h[1]);var y=Math.max(h[0].length,h[1].length);var b=h.length==3?Math.abs(numeric(h[2])):1;var _=lte;var g=m<v;if(g){b*=-1;_=gte}var w=h.some(isPadded);d=[];for(var k=v;_(k,m);k+=b){var E;if(c){E=String.fromCharCode(k);if(E==="\\")E=""}else{E=String(k);if(w){var S=y-E.length;if(S>0){var O=new Array(S+1).join("0");if(k<0)E="-"+O+E.slice(1);else E=O+E}}}d.push(E)}}else{d=n(h,function(t){return expand(t,false)})}for(var j=0;j<d.length;j++){for(var x=0;x<l.length;x++){var T=p+d[j]+l[x];if(!e||u||T)r.push(T)}}return r}},551:t=>{t.exports=function(t,r){var n=[];for(var i=0;i<t.length;i++){var o=r(t[i],i);if(e(o))n.push.apply(n,o);else n.push(o)}return n};var e=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"}},909:(t,e,r)=>{t.exports=realpath;realpath.realpath=realpath;realpath.sync=realpathSync;realpath.realpathSync=realpathSync;realpath.monkeypatch=monkeypatch;realpath.unmonkeypatch=unmonkeypatch;var n=r(747);var i=n.realpath;var o=n.realpathSync;var s=process.version;var a=/^v[0-5]\./.test(s);var c=r(411);function newError(t){return t&&t.syscall==="realpath"&&(t.code==="ELOOP"||t.code==="ENOMEM"||t.code==="ENAMETOOLONG")}function realpath(t,e,r){if(a){return i(t,e,r)}if(typeof e==="function"){r=e;e=null}i(t,e,function(n,i){if(newError(n)){c.realpath(t,e,r)}else{r(n,i)}})}function realpathSync(t,e){if(a){return o(t,e)}try{return o(t,e)}catch(r){if(newError(r)){return c.realpathSync(t,e)}else{throw r}}}function monkeypatch(){n.realpath=realpath;n.realpathSync=realpathSync}function unmonkeypatch(){n.realpath=i;n.realpathSync=o}},411:(t,e,r)=>{var n=r(622);var i=process.platform==="win32";var o=r(747);var s=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function rethrow(){var t;if(s){var e=new Error;t=debugCallback}else t=missingCallback;return t;function debugCallback(t){if(t){e.message=t.message;t=e;missingCallback(t)}}function missingCallback(t){if(t){if(process.throwDeprecation)throw t;else if(!process.noDeprecation){var e="fs: missing callback "+(t.stack||t.message);if(process.traceDeprecation)console.trace(e);else console.error(e)}}}}function maybeCallback(t){return typeof t==="function"?t:rethrow()}var a=n.normalize;if(i){var c=/(.*?)(?:[\/\\]+|$)/g}else{var c=/(.*?)(?:[\/]+|$)/g}if(i){var u=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/}else{var u=/^[\/]*/}e.realpathSync=function realpathSync(t,e){t=n.resolve(t);if(e&&Object.prototype.hasOwnProperty.call(e,t)){return e[t]}var r=t,s={},a={};var f;var h;var l;var p;start();function start(){var e=u.exec(t);f=e[0].length;h=e[0];l=e[0];p="";if(i&&!a[l]){o.lstatSync(l);a[l]=true}}while(f<t.length){c.lastIndex=f;var d=c.exec(t);p=h;h+=d[0];l=p+d[1];f=c.lastIndex;if(a[l]||e&&e[l]===l){continue}var v;if(e&&Object.prototype.hasOwnProperty.call(e,l)){v=e[l]}else{var m=o.lstatSync(l);if(!m.isSymbolicLink()){a[l]=true;if(e)e[l]=l;continue}var y=null;if(!i){var b=m.dev.toString(32)+":"+m.ino.toString(32);if(s.hasOwnProperty(b)){y=s[b]}}if(y===null){o.statSync(l);y=o.readlinkSync(l)}v=n.resolve(p,y);if(e)e[l]=v;if(!i)s[b]=y}t=n.resolve(v,t.slice(f));start()}if(e)e[r]=t;return t};e.realpath=function realpath(t,e,r){if(typeof r!=="function"){r=maybeCallback(e);e=null}t=n.resolve(t);if(e&&Object.prototype.hasOwnProperty.call(e,t)){return process.nextTick(r.bind(null,null,e[t]))}var s=t,a={},f={};var h;var l;var p;var d;start();function start(){var e=u.exec(t);h=e[0].length;l=e[0];p=e[0];d="";if(i&&!f[p]){o.lstat(p,function(t){if(t)return r(t);f[p]=true;LOOP()})}else{process.nextTick(LOOP)}}function LOOP(){if(h>=t.length){if(e)e[s]=t;return r(null,t)}c.lastIndex=h;var n=c.exec(t);d=l;l+=n[0];p=d+n[1];h=c.lastIndex;if(f[p]||e&&e[p]===p){return process.nextTick(LOOP)}if(e&&Object.prototype.hasOwnProperty.call(e,p)){return gotResolvedLink(e[p])}return o.lstat(p,gotStat)}function gotStat(t,n){if(t)return r(t);if(!n.isSymbolicLink()){f[p]=true;if(e)e[p]=p;return process.nextTick(LOOP)}if(!i){var s=n.dev.toString(32)+":"+n.ino.toString(32);if(a.hasOwnProperty(s)){return gotTarget(null,a[s],p)}}o.stat(p,function(t){if(t)return r(t);o.readlink(p,function(t,e){if(!i)a[s]=e;gotTarget(t,e)})})}function gotTarget(t,i,o){if(t)return r(t);var s=n.resolve(d,i);if(e)e[o]=s;gotResolvedLink(s)}function gotResolvedLink(e){t=n.resolve(e,t.slice(h));start()}}},74:(t,e,r)=>{"use strict";const n=r(747);const i=r(622);const o=r(833);function readSizeRecursive(t,e,r,s){let a;let c;if(!s){a=r;c=null}else{a=s;c=r}n.lstat(e,function lstat(r,s){let u=!r?s.size||0:0;if(s){if(t.has(s.ino)){return a(null,0)}t.add(s.ino)}if(!r&&s.isDirectory()){n.readdir(e,(r,n)=>{if(r){return a(r)}o(n,(r,n)=>{readSizeRecursive(t,i.join(e,r),c,(t,e)=>{if(!t){u+=e}n(t)})},t=>{a(t,u)})})}else{if(c&&c.test(e)){u=0}a(r,u)}})}t.exports=((...t)=>{t.unshift(new Set);return readSizeRecursive(...t)})},744:(t,e,r)=>{e.alphasort=alphasort;e.alphasorti=alphasorti;e.setopts=setopts;e.ownProp=ownProp;e.makeAbs=makeAbs;e.finish=finish;e.mark=mark;e.isIgnored=isIgnored;e.childrenIgnored=childrenIgnored;function ownProp(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var n=r(622);var i=r(642);var o=r(963);var s=i.Minimatch;function alphasorti(t,e){return t.toLowerCase().localeCompare(e.toLowerCase())}function alphasort(t,e){return t.localeCompare(e)}function setupIgnores(t,e){t.ignore=e.ignore||[];if(!Array.isArray(t.ignore))t.ignore=[t.ignore];if(t.ignore.length){t.ignore=t.ignore.map(ignoreMap)}}function ignoreMap(t){var e=null;if(t.slice(-3)==="/**"){var r=t.replace(/(\/\*\*)+$/,"");e=new s(r,{dot:true})}return{matcher:new s(t,{dot:true}),gmatcher:e}}function setopts(t,e,r){if(!r)r={};if(r.matchBase&&-1===e.indexOf("/")){if(r.noglobstar){throw new Error("base matching requires globstar")}e="**/"+e}t.silent=!!r.silent;t.pattern=e;t.strict=r.strict!==false;t.realpath=!!r.realpath;t.realpathCache=r.realpathCache||Object.create(null);t.follow=!!r.follow;t.dot=!!r.dot;t.mark=!!r.mark;t.nodir=!!r.nodir;if(t.nodir)t.mark=true;t.sync=!!r.sync;t.nounique=!!r.nounique;t.nonull=!!r.nonull;t.nosort=!!r.nosort;t.nocase=!!r.nocase;t.stat=!!r.stat;t.noprocess=!!r.noprocess;t.absolute=!!r.absolute;t.maxLength=r.maxLength||Infinity;t.cache=r.cache||Object.create(null);t.statCache=r.statCache||Object.create(null);t.symlinks=r.symlinks||Object.create(null);setupIgnores(t,r);t.changedCwd=false;var i=process.cwd();if(!ownProp(r,"cwd"))t.cwd=i;else{t.cwd=n.resolve(r.cwd);t.changedCwd=t.cwd!==i}t.root=r.root||n.resolve(t.cwd,"/");t.root=n.resolve(t.root);if(process.platform==="win32")t.root=t.root.replace(/\\/g,"/");t.cwdAbs=o(t.cwd)?t.cwd:makeAbs(t,t.cwd);if(process.platform==="win32")t.cwdAbs=t.cwdAbs.replace(/\\/g,"/");t.nomount=!!r.nomount;r.nonegate=true;r.nocomment=true;t.minimatch=new s(e,r);t.options=t.minimatch.options}function finish(t){var e=t.nounique;var r=e?[]:Object.create(null);for(var n=0,i=t.matches.length;n<i;n++){var o=t.matches[n];if(!o||Object.keys(o).length===0){if(t.nonull){var s=t.minimatch.globSet[n];if(e)r.push(s);else r[s]=true}}else{var a=Object.keys(o);if(e)r.push.apply(r,a);else a.forEach(function(t){r[t]=true})}}if(!e)r=Object.keys(r);if(!t.nosort)r=r.sort(t.nocase?alphasorti:alphasort);if(t.mark){for(var n=0;n<r.length;n++){r[n]=t._mark(r[n])}if(t.nodir){r=r.filter(function(e){var r=!/\/$/.test(e);var n=t.cache[e]||t.cache[makeAbs(t,e)];if(r&&n)r=n!=="DIR"&&!Array.isArray(n);return r})}}if(t.ignore.length)r=r.filter(function(e){return!isIgnored(t,e)});t.found=r}function mark(t,e){var r=makeAbs(t,e);var n=t.cache[r];var i=e;if(n){var o=n==="DIR"||Array.isArray(n);var s=e.slice(-1)==="/";if(o&&!s)i+="/";else if(!o&&s)i=i.slice(0,-1);if(i!==e){var a=makeAbs(t,i);t.statCache[a]=t.statCache[r];t.cache[a]=t.cache[r]}}return i}function makeAbs(t,e){var r=e;if(e.charAt(0)==="/"){r=n.join(t.root,e)}else if(o(e)||e===""){r=e}else if(t.changedCwd){r=n.resolve(t.cwd,e)}else{r=n.resolve(e)}if(process.platform==="win32")r=r.replace(/\\/g,"/");return r}function isIgnored(t,e){if(!t.ignore.length)return false;return t.ignore.some(function(t){return t.matcher.match(e)||!!(t.gmatcher&&t.gmatcher.match(e))})}function childrenIgnored(t,e){if(!t.ignore.length)return false;return t.ignore.some(function(t){return!!(t.gmatcher&&t.gmatcher.match(e))})}},750:(t,e,r)=>{t.exports=glob;var n=r(747);var i=r(909);var o=r(642);var s=o.Minimatch;var a=r(309);var c=r(614).EventEmitter;var u=r(622);var f=r(357);var h=r(963);var l=r(381);var p=r(744);var d=p.alphasort;var v=p.alphasorti;var m=p.setopts;var y=p.ownProp;var b=r(753);var _=r(669);var g=p.childrenIgnored;var w=p.isIgnored;var k=r(481);function glob(t,e,r){if(typeof e==="function")r=e,e={};if(!e)e={};if(e.sync){if(r)throw new TypeError("callback provided to sync glob");return l(t,e)}return new Glob(t,e,r)}glob.sync=l;var E=glob.GlobSync=l.GlobSync;glob.glob=glob;function extend(t,e){if(e===null||typeof e!=="object"){return t}var r=Object.keys(e);var n=r.length;while(n--){t[r[n]]=e[r[n]]}return t}glob.hasMagic=function(t,e){var r=extend({},e);r.noprocess=true;var n=new Glob(t,r);var i=n.minimatch.set;if(!t)return false;if(i.length>1)return true;for(var o=0;o<i[0].length;o++){if(typeof i[0][o]!=="string")return true}return false};glob.Glob=Glob;a(Glob,c);function Glob(t,e,r){if(typeof e==="function"){r=e;e=null}if(e&&e.sync){if(r)throw new TypeError("callback provided to sync glob");return new E(t,e)}if(!(this instanceof Glob))return new Glob(t,e,r);m(this,t,e);this._didRealPath=false;var n=this.minimatch.set.length;this.matches=new Array(n);if(typeof r==="function"){r=k(r);this.on("error",r);this.on("end",function(t){r(null,t)})}var i=this;this._processing=0;this._emitQueue=[];this._processQueue=[];this.paused=false;if(this.noprocess)return this;if(n===0)return done();var o=true;for(var s=0;s<n;s++){this._process(this.minimatch.set[s],s,false,done)}o=false;function done(){--i._processing;if(i._processing<=0){if(o){process.nextTick(function(){i._finish()})}else{i._finish()}}}}Glob.prototype._finish=function(){f(this instanceof Glob);if(this.aborted)return;if(this.realpath&&!this._didRealpath)return this._realpath();p.finish(this);this.emit("end",this.found)};Glob.prototype._realpath=function(){if(this._didRealpath)return;this._didRealpath=true;var t=this.matches.length;if(t===0)return this._finish();var e=this;for(var r=0;r<this.matches.length;r++)this._realpathSet(r,next);function next(){if(--t===0)e._finish()}};Glob.prototype._realpathSet=function(t,e){var r=this.matches[t];if(!r)return e();var n=Object.keys(r);var o=this;var s=n.length;if(s===0)return e();var a=this.matches[t]=Object.create(null);n.forEach(function(r,n){r=o._makeAbs(r);i.realpath(r,o.realpathCache,function(n,i){if(!n)a[i]=true;else if(n.syscall==="stat")a[r]=true;else o.emit("error",n);if(--s===0){o.matches[t]=a;e()}})})};Glob.prototype._mark=function(t){return p.mark(this,t)};Glob.prototype._makeAbs=function(t){return p.makeAbs(this,t)};Glob.prototype.abort=function(){this.aborted=true;this.emit("abort")};Glob.prototype.pause=function(){if(!this.paused){this.paused=true;this.emit("pause")}};Glob.prototype.resume=function(){if(this.paused){this.emit("resume");this.paused=false;if(this._emitQueue.length){var t=this._emitQueue.slice(0);this._emitQueue.length=0;for(var e=0;e<t.length;e++){var r=t[e];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var n=this._processQueue.slice(0);this._processQueue.length=0;for(var e=0;e<n.length;e++){var i=n[e];this._processing--;this._process(i[0],i[1],i[2],i[3])}}}};Glob.prototype._process=function(t,e,r,n){f(this instanceof Glob);f(typeof n==="function");if(this.aborted)return;this._processing++;if(this.paused){this._processQueue.push([t,e,r,n]);return}var i=0;while(typeof t[i]==="string"){i++}var s;switch(i){case t.length:this._processSimple(t.join("/"),e,n);return;case 0:s=null;break;default:s=t.slice(0,i).join("/");break}var a=t.slice(i);var c;if(s===null)c=".";else if(h(s)||h(t.join("/"))){if(!s||!h(s))s="/"+s;c=s}else c=s;var u=this._makeAbs(c);if(g(this,c))return n();var l=a[0]===o.GLOBSTAR;if(l)this._processGlobStar(s,c,u,a,e,r,n);else this._processReaddir(s,c,u,a,e,r,n)};Glob.prototype._processReaddir=function(t,e,r,n,i,o,s){var a=this;this._readdir(r,o,function(c,u){return a._processReaddir2(t,e,r,n,i,o,u,s)})};Glob.prototype._processReaddir2=function(t,e,r,n,i,o,s,a){if(!s)return a();var c=n[0];var f=!!this.minimatch.negate;var h=c._glob;var l=this.dot||h.charAt(0)===".";var p=[];for(var d=0;d<s.length;d++){var v=s[d];if(v.charAt(0)!=="."||l){var m;if(f&&!t){m=!v.match(c)}else{m=v.match(c)}if(m)p.push(v)}}var y=p.length;if(y===0)return a();if(n.length===1&&!this.mark&&!this.stat){if(!this.matches[i])this.matches[i]=Object.create(null);for(var d=0;d<y;d++){var v=p[d];if(t){if(t!=="/")v=t+"/"+v;else v=t+v}if(v.charAt(0)==="/"&&!this.nomount){v=u.join(this.root,v)}this._emitMatch(i,v)}return a()}n.shift();for(var d=0;d<y;d++){var v=p[d];var b;if(t){if(t!=="/")v=t+"/"+v;else v=t+v}this._process([v].concat(n),i,o,a)}a()};Glob.prototype._emitMatch=function(t,e){if(this.aborted)return;if(w(this,e))return;if(this.paused){this._emitQueue.push([t,e]);return}var r=h(e)?e:this._makeAbs(e);if(this.mark)e=this._mark(e);if(this.absolute)e=r;if(this.matches[t][e])return;if(this.nodir){var n=this.cache[r];if(n==="DIR"||Array.isArray(n))return}this.matches[t][e]=true;var i=this.statCache[r];if(i)this.emit("stat",e,i);this.emit("match",e)};Glob.prototype._readdirInGlobStar=function(t,e){if(this.aborted)return;if(this.follow)return this._readdir(t,false,e);var r="lstat\0"+t;var i=this;var o=b(r,lstatcb_);if(o)n.lstat(t,o);function lstatcb_(r,n){if(r&&r.code==="ENOENT")return e();var o=n&&n.isSymbolicLink();i.symlinks[t]=o;if(!o&&n&&!n.isDirectory()){i.cache[t]="FILE";e()}else i._readdir(t,false,e)}};Glob.prototype._readdir=function(t,e,r){if(this.aborted)return;r=b("readdir\0"+t+"\0"+e,r);if(!r)return;if(e&&!y(this.symlinks,t))return this._readdirInGlobStar(t,r);if(y(this.cache,t)){var i=this.cache[t];if(!i||i==="FILE")return r();if(Array.isArray(i))return r(null,i)}var o=this;n.readdir(t,readdirCb(this,t,r))};function readdirCb(t,e,r){return function(n,i){if(n)t._readdirError(e,n,r);else t._readdirEntries(e,i,r)}}Glob.prototype._readdirEntries=function(t,e,r){if(this.aborted)return;if(!this.mark&&!this.stat){for(var n=0;n<e.length;n++){var i=e[n];if(t==="/")i=t+i;else i=t+"/"+i;this.cache[i]=true}}this.cache[t]=e;return r(null,e)};Glob.prototype._readdirError=function(t,e,r){if(this.aborted)return;switch(e.code){case"ENOTSUP":case"ENOTDIR":var n=this._makeAbs(t);this.cache[n]="FILE";if(n===this.cwdAbs){var i=new Error(e.code+" invalid cwd "+this.cwd);i.path=this.cwd;i.code=e.code;this.emit("error",i);this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=false;break;default:this.cache[this._makeAbs(t)]=false;if(this.strict){this.emit("error",e);this.abort()}if(!this.silent)console.error("glob error",e);break}return r()};Glob.prototype._processGlobStar=function(t,e,r,n,i,o,s){var a=this;this._readdir(r,o,function(c,u){a._processGlobStar2(t,e,r,n,i,o,u,s)})};Glob.prototype._processGlobStar2=function(t,e,r,n,i,o,s,a){if(!s)return a();var c=n.slice(1);var u=t?[t]:[];var f=u.concat(c);this._process(f,i,false,a);var h=this.symlinks[r];var l=s.length;if(h&&o)return a();for(var p=0;p<l;p++){var d=s[p];if(d.charAt(0)==="."&&!this.dot)continue;var v=u.concat(s[p],c);this._process(v,i,true,a);var m=u.concat(s[p],n);this._process(m,i,true,a)}a()};Glob.prototype._processSimple=function(t,e,r){var n=this;this._stat(t,function(i,o){n._processSimple2(t,e,i,o,r)})};Glob.prototype._processSimple2=function(t,e,r,n,i){if(!this.matches[e])this.matches[e]=Object.create(null);if(!n)return i();if(t&&h(t)&&!this.nomount){var o=/[\/\\]$/.test(t);if(t.charAt(0)==="/"){t=u.join(this.root,t)}else{t=u.resolve(this.root,t);if(o)t+="/"}}if(process.platform==="win32")t=t.replace(/\\/g,"/");this._emitMatch(e,t);i()};Glob.prototype._stat=function(t,e){var r=this._makeAbs(t);var i=t.slice(-1)==="/";if(t.length>this.maxLength)return e();if(!this.stat&&y(this.cache,r)){var o=this.cache[r];if(Array.isArray(o))o="DIR";if(!i||o==="DIR")return e(null,o);if(i&&o==="FILE")return e()}var s;var a=this.statCache[r];if(a!==undefined){if(a===false)return e(null,a);else{var c=a.isDirectory()?"DIR":"FILE";if(i&&c==="FILE")return e();else return e(null,c,a)}}var u=this;var f=b("stat\0"+r,lstatcb_);if(f)n.lstat(r,f);function lstatcb_(i,o){if(o&&o.isSymbolicLink()){return n.stat(r,function(n,i){if(n)u._stat2(t,r,null,o,e);else u._stat2(t,r,n,i,e)})}else{u._stat2(t,r,i,o,e)}}};Glob.prototype._stat2=function(t,e,r,n,i){if(r&&(r.code==="ENOENT"||r.code==="ENOTDIR")){this.statCache[e]=false;return i()}var o=t.slice(-1)==="/";this.statCache[e]=n;if(e.slice(-1)==="/"&&n&&!n.isDirectory())return i(null,false,n);var s=true;if(n)s=n.isDirectory()?"DIR":"FILE";this.cache[e]=this.cache[e]||s;if(o&&s==="FILE")return i();return i(null,s,n)}},381:(t,e,r)=>{t.exports=globSync;globSync.GlobSync=GlobSync;var n=r(747);var i=r(909);var o=r(642);var s=o.Minimatch;var a=r(750).Glob;var c=r(669);var u=r(622);var f=r(357);var h=r(963);var l=r(744);var p=l.alphasort;var d=l.alphasorti;var v=l.setopts;var m=l.ownProp;var y=l.childrenIgnored;var b=l.isIgnored;function globSync(t,e){if(typeof e==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");return new GlobSync(t,e).found}function GlobSync(t,e){if(!t)throw new Error("must provide pattern");if(typeof e==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof GlobSync))return new GlobSync(t,e);v(this,t,e);if(this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var n=0;n<r;n++){this._process(this.minimatch.set[n],n,false)}this._finish()}GlobSync.prototype._finish=function(){f(this instanceof GlobSync);if(this.realpath){var t=this;this.matches.forEach(function(e,r){var n=t.matches[r]=Object.create(null);for(var o in e){try{o=t._makeAbs(o);var s=i.realpathSync(o,t.realpathCache);n[s]=true}catch(e){if(e.syscall==="stat")n[t._makeAbs(o)]=true;else throw e}}})}l.finish(this)};GlobSync.prototype._process=function(t,e,r){f(this instanceof GlobSync);var n=0;while(typeof t[n]==="string"){n++}var i;switch(n){case t.length:this._processSimple(t.join("/"),e);return;case 0:i=null;break;default:i=t.slice(0,n).join("/");break}var s=t.slice(n);var a;if(i===null)a=".";else if(h(i)||h(t.join("/"))){if(!i||!h(i))i="/"+i;a=i}else a=i;var c=this._makeAbs(a);if(y(this,a))return;var u=s[0]===o.GLOBSTAR;if(u)this._processGlobStar(i,a,c,s,e,r);else this._processReaddir(i,a,c,s,e,r)};GlobSync.prototype._processReaddir=function(t,e,r,n,i,o){var s=this._readdir(r,o);if(!s)return;var a=n[0];var c=!!this.minimatch.negate;var f=a._glob;var h=this.dot||f.charAt(0)===".";var l=[];for(var p=0;p<s.length;p++){var d=s[p];if(d.charAt(0)!=="."||h){var v;if(c&&!t){v=!d.match(a)}else{v=d.match(a)}if(v)l.push(d)}}var m=l.length;if(m===0)return;if(n.length===1&&!this.mark&&!this.stat){if(!this.matches[i])this.matches[i]=Object.create(null);for(var p=0;p<m;p++){var d=l[p];if(t){if(t.slice(-1)!=="/")d=t+"/"+d;else d=t+d}if(d.charAt(0)==="/"&&!this.nomount){d=u.join(this.root,d)}this._emitMatch(i,d)}return}n.shift();for(var p=0;p<m;p++){var d=l[p];var y;if(t)y=[t,d];else y=[d];this._process(y.concat(n),i,o)}};GlobSync.prototype._emitMatch=function(t,e){if(b(this,e))return;var r=this._makeAbs(e);if(this.mark)e=this._mark(e);if(this.absolute){e=r}if(this.matches[t][e])return;if(this.nodir){var n=this.cache[r];if(n==="DIR"||Array.isArray(n))return}this.matches[t][e]=true;if(this.stat)this._stat(e)};GlobSync.prototype._readdirInGlobStar=function(t){if(this.follow)return this._readdir(t,false);var e;var r;var i;try{r=n.lstatSync(t)}catch(t){if(t.code==="ENOENT"){return null}}var o=r&&r.isSymbolicLink();this.symlinks[t]=o;if(!o&&r&&!r.isDirectory())this.cache[t]="FILE";else e=this._readdir(t,false);return e};GlobSync.prototype._readdir=function(t,e){var r;if(e&&!m(this.symlinks,t))return this._readdirInGlobStar(t);if(m(this.cache,t)){var i=this.cache[t];if(!i||i==="FILE")return null;if(Array.isArray(i))return i}try{return this._readdirEntries(t,n.readdirSync(t))}catch(e){this._readdirError(t,e);return null}};GlobSync.prototype._readdirEntries=function(t,e){if(!this.mark&&!this.stat){for(var r=0;r<e.length;r++){var n=e[r];if(t==="/")n=t+n;else n=t+"/"+n;this.cache[n]=true}}this.cache[t]=e;return e};GlobSync.prototype._readdirError=function(t,e){switch(e.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(t);this.cache[r]="FILE";if(r===this.cwdAbs){var n=new Error(e.code+" invalid cwd "+this.cwd);n.path=this.cwd;n.code=e.code;throw n}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=false;break;default:this.cache[this._makeAbs(t)]=false;if(this.strict)throw e;if(!this.silent)console.error("glob error",e);break}};GlobSync.prototype._processGlobStar=function(t,e,r,n,i,o){var s=this._readdir(r,o);if(!s)return;var a=n.slice(1);var c=t?[t]:[];var u=c.concat(a);this._process(u,i,false);var f=s.length;var h=this.symlinks[r];if(h&&o)return;for(var l=0;l<f;l++){var p=s[l];if(p.charAt(0)==="."&&!this.dot)continue;var d=c.concat(s[l],a);this._process(d,i,true);var v=c.concat(s[l],n);this._process(v,i,true)}};GlobSync.prototype._processSimple=function(t,e){var r=this._stat(t);if(!this.matches[e])this.matches[e]=Object.create(null);if(!r)return;if(t&&h(t)&&!this.nomount){var n=/[\/\\]$/.test(t);if(t.charAt(0)==="/"){t=u.join(this.root,t)}else{t=u.resolve(this.root,t);if(n)t+="/"}}if(process.platform==="win32")t=t.replace(/\\/g,"/");this._emitMatch(e,t)};GlobSync.prototype._stat=function(t){var e=this._makeAbs(t);var r=t.slice(-1)==="/";if(t.length>this.maxLength)return false;if(!this.stat&&m(this.cache,e)){var i=this.cache[e];if(Array.isArray(i))i="DIR";if(!r||i==="DIR")return i;if(r&&i==="FILE")return false}var o;var s=this.statCache[e];if(!s){var a;try{a=n.lstatSync(e)}catch(t){if(t&&(t.code==="ENOENT"||t.code==="ENOTDIR")){this.statCache[e]=false;return false}}if(a&&a.isSymbolicLink()){try{s=n.statSync(e)}catch(t){s=a}}else{s=a}}this.statCache[e]=s;var i=true;if(s)i=s.isDirectory()?"DIR":"FILE";this.cache[e]=this.cache[e]||i;if(r&&i==="FILE")return false;return i};GlobSync.prototype._mark=function(t){return l.mark(this,t)};GlobSync.prototype._makeAbs=function(t){return l.makeAbs(this,t)}},753:(t,e,r)=>{var n=r(687);var i=Object.create(null);var o=r(481);t.exports=n(inflight);function inflight(t,e){if(i[t]){i[t].push(e);return null}else{i[t]=[e];return makeres(t)}}function makeres(t){return o(function RES(){var e=i[t];var r=e.length;var n=slice(arguments);try{for(var o=0;o<r;o++){e[o].apply(null,n)}}finally{if(e.length>r){e.splice(0,r);process.nextTick(function(){RES.apply(null,n)})}else{delete i[t]}}})}function slice(t){var e=t.length;var r=[];for(var n=0;n<e;n++)r[n]=t[n];return r}},309:(t,e,r)=>{try{var n=r(669);if(typeof n.inherits!=="function")throw"";t.exports=n.inherits}catch(e){t.exports=r(474)}},474:t=>{if(typeof Object.create==="function"){t.exports=function inherits(t,e){if(e){t.super_=e;t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:false,writable:true,configurable:true}})}}}else{t.exports=function inherits(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype;t.prototype=new r;t.prototype.constructor=t}}}},642:(t,e,r)=>{t.exports=minimatch;minimatch.Minimatch=Minimatch;var n={sep:"/"};try{n=r(622)}catch(t){}var i=minimatch.GLOBSTAR=Minimatch.GLOBSTAR={};var o=r(215);var s={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}};var a="[^/]";var c=a+"*?";var u="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?";var f="(?:(?!(?:\\/|^)\\.).)*?";var h=charSet("().*{}+?[]^$\\!");function charSet(t){return t.split("").reduce(function(t,e){t[e]=true;return t},{})}var l=/\/+/;minimatch.filter=filter;function filter(t,e){e=e||{};return function(r,n,i){return minimatch(r,t,e)}}function ext(t,e){t=t||{};e=e||{};var r={};Object.keys(e).forEach(function(t){r[t]=e[t]});Object.keys(t).forEach(function(e){r[e]=t[e]});return r}minimatch.defaults=function(t){if(!t||!Object.keys(t).length)return minimatch;var e=minimatch;var r=function minimatch(r,n,i){return e.minimatch(r,n,ext(t,i))};r.Minimatch=function Minimatch(r,n){return new e.Minimatch(r,ext(t,n))};return r};Minimatch.defaults=function(t){if(!t||!Object.keys(t).length)return Minimatch;return minimatch.defaults(t).Minimatch};function minimatch(t,e,r){if(typeof e!=="string"){throw new TypeError("glob pattern string required")}if(!r)r={};if(!r.nocomment&&e.charAt(0)==="#"){return false}if(e.trim()==="")return t==="";return new Minimatch(e,r).match(t)}function Minimatch(t,e){if(!(this instanceof Minimatch)){return new Minimatch(t,e)}if(typeof t!=="string"){throw new TypeError("glob pattern string required")}if(!e)e={};t=t.trim();if(n.sep!=="/"){t=t.split(n.sep).join("/")}this.options=e;this.set=[];this.pattern=t;this.regexp=null;this.negate=false;this.comment=false;this.empty=false;this.make()}Minimatch.prototype.debug=function(){};Minimatch.prototype.make=make;function make(){if(this._made)return;var t=this.pattern;var e=this.options;if(!e.nocomment&&t.charAt(0)==="#"){this.comment=true;return}if(!t){this.empty=true;return}this.parseNegate();var r=this.globSet=this.braceExpand();if(e.debug)this.debug=console.error;this.debug(this.pattern,r);r=this.globParts=r.map(function(t){return t.split(l)});this.debug(this.pattern,r);r=r.map(function(t,e,r){return t.map(this.parse,this)},this);this.debug(this.pattern,r);r=r.filter(function(t){return t.indexOf(false)===-1});this.debug(this.pattern,r);this.set=r}Minimatch.prototype.parseNegate=parseNegate;function parseNegate(){var t=this.pattern;var e=false;var r=this.options;var n=0;if(r.nonegate)return;for(var i=0,o=t.length;i<o&&t.charAt(i)==="!";i++){e=!e;n++}if(n)this.pattern=t.substr(n);this.negate=e}minimatch.braceExpand=function(t,e){return braceExpand(t,e)};Minimatch.prototype.braceExpand=braceExpand;function braceExpand(t,e){if(!e){if(this instanceof Minimatch){e=this.options}else{e={}}}t=typeof t==="undefined"?this.pattern:t;if(typeof t==="undefined"){throw new TypeError("undefined pattern")}if(e.nobrace||!t.match(/\{.*\}/)){return[t]}return o(t)}Minimatch.prototype.parse=parse;var p={};function parse(t,e){if(t.length>1024*64){throw new TypeError("pattern is too long")}var r=this.options;if(!r.noglobstar&&t==="**")return i;if(t==="")return"";var n="";var o=!!r.nocase;var u=false;var f=[];var l=[];var d;var v=false;var m=-1;var y=-1;var b=t.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)";var _=this;function clearStateChar(){if(d){switch(d){case"*":n+=c;o=true;break;case"?":n+=a;o=true;break;default:n+="\\"+d;break}_.debug("clearStateChar %j %j",d,n);d=false}}for(var g=0,w=t.length,k;g<w&&(k=t.charAt(g));g++){this.debug("%s\t%s %s %j",t,g,n,k);if(u&&h[k]){n+="\\"+k;u=false;continue}switch(k){case"/":return false;case"\\":clearStateChar();u=true;continue;case"?":case"*":case"+":case"@":case"!":this.debug("%s\t%s %s %j <-- stateChar",t,g,n,k);if(v){this.debug("  in class");if(k==="!"&&g===y+1)k="^";n+=k;continue}_.debug("call clearStateChar %j",d);clearStateChar();d=k;if(r.noext)clearStateChar();continue;case"(":if(v){n+="(";continue}if(!d){n+="\\(";continue}f.push({type:d,start:g-1,reStart:n.length,open:s[d].open,close:s[d].close});n+=d==="!"?"(?:(?!(?:":"(?:";this.debug("plType %j %j",d,n);d=false;continue;case")":if(v||!f.length){n+="\\)";continue}clearStateChar();o=true;var E=f.pop();n+=E.close;if(E.type==="!"){l.push(E)}E.reEnd=n.length;continue;case"|":if(v||!f.length||u){n+="\\|";u=false;continue}clearStateChar();n+="|";continue;case"[":clearStateChar();if(v){n+="\\"+k;continue}v=true;y=g;m=n.length;n+=k;continue;case"]":if(g===y+1||!v){n+="\\"+k;u=false;continue}if(v){var S=t.substring(y+1,g);try{RegExp("["+S+"]")}catch(t){var O=this.parse(S,p);n=n.substr(0,m)+"\\["+O[0]+"\\]";o=o||O[1];v=false;continue}}o=true;v=false;n+=k;continue;default:clearStateChar();if(u){u=false}else if(h[k]&&!(k==="^"&&v)){n+="\\"}n+=k}}if(v){S=t.substr(y+1);O=this.parse(S,p);n=n.substr(0,m)+"\\["+O[0];o=o||O[1]}for(E=f.pop();E;E=f.pop()){var j=n.slice(E.reStart+E.open.length);this.debug("setting tail",n,E);j=j.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(t,e,r){if(!r){r="\\"}return e+e+r+"|"});this.debug("tail=%j\n   %s",j,j,E,n);var x=E.type==="*"?c:E.type==="?"?a:"\\"+E.type;o=true;n=n.slice(0,E.reStart)+x+"\\("+j}clearStateChar();if(u){n+="\\\\"}var T=false;switch(n.charAt(0)){case".":case"[":case"(":T=true}for(var G=l.length-1;G>-1;G--){var M=l[G];var I=n.slice(0,M.reStart);var C=n.slice(M.reStart,M.reEnd-8);var D=n.slice(M.reEnd-8,M.reEnd);var R=n.slice(M.reEnd);D+=R;var P=I.split("(").length-1;var $=R;for(g=0;g<P;g++){$=$.replace(/\)[+*?]?/,"")}R=$;var q="";if(R===""&&e!==p){q="$"}var A=I+C+R+q+D;n=A}if(n!==""&&o){n="(?=.)"+n}if(T){n=b+n}if(e===p){return[n,o]}if(!o){return globUnescape(t)}var L=r.nocase?"i":"";try{var B=new RegExp("^"+n+"$",L)}catch(t){return new RegExp("$.")}B._glob=t;B._src=n;return B}minimatch.makeRe=function(t,e){return new Minimatch(t,e||{}).makeRe()};Minimatch.prototype.makeRe=makeRe;function makeRe(){if(this.regexp||this.regexp===false)return this.regexp;var t=this.set;if(!t.length){this.regexp=false;return this.regexp}var e=this.options;var r=e.noglobstar?c:e.dot?u:f;var n=e.nocase?"i":"";var o=t.map(function(t){return t.map(function(t){return t===i?r:typeof t==="string"?regExpEscape(t):t._src}).join("\\/")}).join("|");o="^(?:"+o+")$";if(this.negate)o="^(?!"+o+").*$";try{this.regexp=new RegExp(o,n)}catch(t){this.regexp=false}return this.regexp}minimatch.match=function(t,e,r){r=r||{};var n=new Minimatch(e,r);t=t.filter(function(t){return n.match(t)});if(n.options.nonull&&!t.length){t.push(e)}return t};Minimatch.prototype.match=match;function match(t,e){this.debug("match",t,this.pattern);if(this.comment)return false;if(this.empty)return t==="";if(t==="/"&&e)return true;var r=this.options;if(n.sep!=="/"){t=t.split(n.sep).join("/")}t=t.split(l);this.debug(this.pattern,"split",t);var i=this.set;this.debug(this.pattern,"set",i);var o;var s;for(s=t.length-1;s>=0;s--){o=t[s];if(o)break}for(s=0;s<i.length;s++){var a=i[s];var c=t;if(r.matchBase&&a.length===1){c=[o]}var u=this.matchOne(c,a,e);if(u){if(r.flipNegate)return true;return!this.negate}}if(r.flipNegate)return false;return this.negate}Minimatch.prototype.matchOne=function(t,e,r){var n=this.options;this.debug("matchOne",{this:this,file:t,pattern:e});this.debug("matchOne",t.length,e.length);for(var o=0,s=0,a=t.length,c=e.length;o<a&&s<c;o++,s++){this.debug("matchOne loop");var u=e[s];var f=t[o];this.debug(e,u,f);if(u===false)return false;if(u===i){this.debug("GLOBSTAR",[e,u,f]);var h=o;var l=s+1;if(l===c){this.debug("** at the end");for(;o<a;o++){if(t[o]==="."||t[o]===".."||!n.dot&&t[o].charAt(0)===".")return false}return true}while(h<a){var p=t[h];this.debug("\nglobstar while",t,h,e,l,p);if(this.matchOne(t.slice(h),e.slice(l),r)){this.debug("globstar found match!",h,a,p);return true}else{if(p==="."||p===".."||!n.dot&&p.charAt(0)==="."){this.debug("dot detected!",t,h,e,l);break}this.debug("globstar swallow a segment, and continue");h++}}if(r){this.debug("\n>>> no match, partial?",t,h,e,l);if(h===a)return true}return false}var d;if(typeof u==="string"){if(n.nocase){d=f.toLowerCase()===u.toLowerCase()}else{d=f===u}this.debug("string match",u,f,d)}else{d=f.match(u);this.debug("pattern match",u,f,d)}if(!d)return false}if(o===a&&s===c){return true}else if(o===a){return r}else if(s===c){var v=o===a-1&&t[o]==="";return v}throw new Error("wtf?")};function globUnescape(t){return t.replace(/\\(.)/g,"$1")}function regExpEscape(t){return t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}},485:(t,e,r)=>{var n=r(622);var i=r(747);var o=parseInt("0777",8);t.exports=mkdirP.mkdirp=mkdirP.mkdirP=mkdirP;function mkdirP(t,e,r,s){if(typeof e==="function"){r=e;e={}}else if(!e||typeof e!=="object"){e={mode:e}}var a=e.mode;var c=e.fs||i;if(a===undefined){a=o}if(!s)s=null;var u=r||function(){};t=n.resolve(t);c.mkdir(t,a,function(r){if(!r){s=s||t;return u(null,s)}switch(r.code){case"ENOENT":if(n.dirname(t)===t)return u(r);mkdirP(n.dirname(t),e,function(r,n){if(r)u(r,n);else mkdirP(t,e,u,n)});break;default:c.stat(t,function(t,e){if(t||!e.isDirectory())u(r,s);else u(null,s)});break}})}mkdirP.sync=function sync(t,e,r){if(!e||typeof e!=="object"){e={mode:e}}var s=e.mode;var a=e.fs||i;if(s===undefined){s=o}if(!r)r=null;t=n.resolve(t);try{a.mkdirSync(t,s);r=r||t}catch(i){switch(i.code){case"ENOENT":r=sync(n.dirname(t),e,r);sync(t,e,r);break;default:var c;try{c=a.statSync(t)}catch(t){throw i}if(!c.isDirectory())throw i;break}}return r}},481:(t,e,r)=>{var n=r(687);t.exports=n(once);t.exports.strict=n(onceStrict);once.proto=once(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return once(this)},configurable:true});Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return onceStrict(this)},configurable:true})});function once(t){var e=function(){if(e.called)return e.value;e.called=true;return e.value=t.apply(this,arguments)};e.called=false;return e}function onceStrict(t){var e=function(){if(e.called)throw new Error(e.onceError);e.called=true;return e.value=t.apply(this,arguments)};var r=t.name||"Function wrapped with `once`";e.onceError=r+" shouldn't be called more than once";e.called=false;return e}},963:t=>{"use strict";function posix(t){return t.charAt(0)==="/"}function win32(t){var e=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var r=e.exec(t);var n=r[1]||"";var i=Boolean(n&&n.charAt(1)!==":");return Boolean(r[2]||i)}t.exports=process.platform==="win32"?win32:posix;t.exports.posix=posix;t.exports.win32=win32},8:(t,e,r)=>{t.exports=rimraf;rimraf.sync=rimrafSync;var n=r(357);var i=r(622);var o=r(747);var s=undefined;try{s=r(750)}catch(t){}var a=parseInt("666",8);var c={nosort:true,silent:true};var u=0;var f=process.platform==="win32";function defaults(t){var e=["unlink","chmod","stat","lstat","rmdir","readdir"];e.forEach(function(e){t[e]=t[e]||o[e];e=e+"Sync";t[e]=t[e]||o[e]});t.maxBusyTries=t.maxBusyTries||3;t.emfileWait=t.emfileWait||1e3;if(t.glob===false){t.disableGlob=true}if(t.disableGlob!==true&&s===undefined){throw Error("glob dependency not found, set `options.disableGlob = true` if intentional")}t.disableGlob=t.disableGlob||false;t.glob=t.glob||c}function rimraf(t,e,r){if(typeof e==="function"){r=e;e={}}n(t,"rimraf: missing path");n.equal(typeof t,"string","rimraf: path should be a string");n.equal(typeof r,"function","rimraf: callback function required");n(e,"rimraf: invalid options argument provided");n.equal(typeof e,"object","rimraf: options should be object");defaults(e);var i=0;var o=null;var a=0;if(e.disableGlob||!s.hasMagic(t))return afterGlob(null,[t]);e.lstat(t,function(r,n){if(!r)return afterGlob(null,[t]);s(t,e.glob,afterGlob)});function next(t){o=o||t;if(--a===0)r(o)}function afterGlob(t,n){if(t)return r(t);a=n.length;if(a===0)return r();n.forEach(function(t){rimraf_(t,e,function CB(r){if(r){if((r.code==="EBUSY"||r.code==="ENOTEMPTY"||r.code==="EPERM")&&i<e.maxBusyTries){i++;var n=i*100;return setTimeout(function(){rimraf_(t,e,CB)},n)}if(r.code==="EMFILE"&&u<e.emfileWait){return setTimeout(function(){rimraf_(t,e,CB)},u++)}if(r.code==="ENOENT")r=null}u=0;next(r)})})}}function rimraf_(t,e,r){n(t);n(e);n(typeof r==="function");e.lstat(t,function(n,i){if(n&&n.code==="ENOENT")return r(null);if(n&&n.code==="EPERM"&&f)fixWinEPERM(t,e,n,r);if(i&&i.isDirectory())return rmdir(t,e,n,r);e.unlink(t,function(n){if(n){if(n.code==="ENOENT")return r(null);if(n.code==="EPERM")return f?fixWinEPERM(t,e,n,r):rmdir(t,e,n,r);if(n.code==="EISDIR")return rmdir(t,e,n,r)}return r(n)})})}function fixWinEPERM(t,e,r,i){n(t);n(e);n(typeof i==="function");if(r)n(r instanceof Error);e.chmod(t,a,function(n){if(n)i(n.code==="ENOENT"?null:r);else e.stat(t,function(n,o){if(n)i(n.code==="ENOENT"?null:r);else if(o.isDirectory())rmdir(t,e,r,i);else e.unlink(t,i)})})}function fixWinEPERMSync(t,e,r){n(t);n(e);if(r)n(r instanceof Error);try{e.chmodSync(t,a)}catch(t){if(t.code==="ENOENT")return;else throw r}try{var i=e.statSync(t)}catch(t){if(t.code==="ENOENT")return;else throw r}if(i.isDirectory())rmdirSync(t,e,r);else e.unlinkSync(t)}function rmdir(t,e,r,i){n(t);n(e);if(r)n(r instanceof Error);n(typeof i==="function");e.rmdir(t,function(n){if(n&&(n.code==="ENOTEMPTY"||n.code==="EEXIST"||n.code==="EPERM"))rmkids(t,e,i);else if(n&&n.code==="ENOTDIR")i(r);else i(n)})}function rmkids(t,e,r){n(t);n(e);n(typeof r==="function");e.readdir(t,function(n,o){if(n)return r(n);var s=o.length;if(s===0)return e.rmdir(t,r);var a;o.forEach(function(n){rimraf(i.join(t,n),e,function(n){if(a)return;if(n)return r(a=n);if(--s===0)e.rmdir(t,r)})})})}function rimrafSync(t,e){e=e||{};defaults(e);n(t,"rimraf: missing path");n.equal(typeof t,"string","rimraf: path should be a string");n(e,"rimraf: missing options");n.equal(typeof e,"object","rimraf: options should be object");var r;if(e.disableGlob||!s.hasMagic(t)){r=[t]}else{try{e.lstatSync(t);r=[t]}catch(n){r=s.sync(t,e.glob)}}if(!r.length)return;for(var i=0;i<r.length;i++){var t=r[i];try{var o=e.lstatSync(t)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM"&&f)fixWinEPERMSync(t,e,r)}try{if(o&&o.isDirectory())rmdirSync(t,e,null);else e.unlinkSync(t)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM")return f?fixWinEPERMSync(t,e,r):rmdirSync(t,e,r);if(r.code!=="EISDIR")throw r;rmdirSync(t,e,r)}}}function rmdirSync(t,e,r){n(t);n(e);if(r)n(r instanceof Error);try{e.rmdirSync(t)}catch(n){if(n.code==="ENOENT")return;if(n.code==="ENOTDIR")throw r;if(n.code==="ENOTEMPTY"||n.code==="EEXIST"||n.code==="EPERM")rmkidsSync(t,e)}}function rmkidsSync(t,e){n(t);n(e);e.readdirSync(t).forEach(function(r){rimrafSync(i.join(t,r),e)});var r=f?100:1;var o=0;do{var s=true;try{var a=e.rmdirSync(t,e);s=false;return a}finally{if(++o<r&&s)continue}}while(true)}},833:t=>{"use strict";t.exports=function eachAsync(t,e,r,n){var i=0;var o=0;var s=t.length-1;var a=false;var c;var u;var f;if(typeof e==="number"){c=e;f=r;u=n||function noop(){}}else{f=e;u=r||function noop(){};c=t.length}if(!t.length){return u()}var h=f.length;var l=function shouldCallNextIterator(){return!a&&i<c&&o<s};var p=function iteratorCallback(t){if(a){return}i--;if(t||o===s&&!i){a=true;u(t)}else if(l()){d(++o)}};var d=function processIterator(){i++;var e=h===2?[t[o],p]:[t[o],o,p];f.apply(null,e);if(l()){processIterator(++o)}};d()}},687:t=>{t.exports=wrappy;function wrappy(t,e){if(t&&e)return wrappy(t)(e);if(typeof t!=="function")throw new TypeError("need wrapper function");Object.keys(t).forEach(function(e){wrapper[e]=t[e]});return wrapper;function wrapper(){var e=new Array(arguments.length);for(var r=0;r<e.length;r++){e[r]=arguments[r]}var n=t.apply(this,e);var i=e[e.length-1];if(typeof n==="function"&&n!==i){Object.keys(i).forEach(function(t){n[t]=i[t]})}return n}}},819:(module,__unused_webpack_exports,__webpack_require__)=>{const{resolve:resolve,relative:relative,dirname:dirname,sep:sep,extname:extname}=__webpack_require__(622);const glob=__webpack_require__(750);const shebangRegEx=__webpack_require__(681);const rimraf=__webpack_require__(8);const crypto=__webpack_require__(417);const{writeFileSync:writeFileSync,unlink:unlink,existsSync:existsSync,symlinkSync:symlinkSync}=__webpack_require__(747);const mkdirp=__webpack_require__(485);const{version:nccVersion}=__webpack_require__(306);process.noDeprecation=true;const usage=`Usage: ncc <cmd> <opts>\n\nCommands:\n  build <input-file> [opts]\n  run <input-file> [opts]\n  cache clean|dir|size\n  help\n  version\n\nOptions:\n  -o, --out [file]         Output directory for build (defaults to dist)\n  -m, --minify             Minify output\n  -C, --no-cache           Skip build cache population\n  -s, --source-map         Generate source map\n  --no-source-map-register Skip source-map-register source map support\n  -e, --external [mod]     Skip bundling 'mod'. Can be used many times\n  -q, --quiet              Disable build summaries / non-error outputs\n  -w, --watch              Start a watched build\n  -t, --transpile-only     Use transpileOnly option with the ts-loader\n  --v8-cache               Emit a build using the v8 compile cache\n  --license [file]         Adds a file containing licensing information to the output\n  --stats-out [file]       Emit webpack stats as json to the specified output file\n`;let api=false;if(require.main===require.cache[eval("__filename")]){runCmd(process.argv.slice(2),process.stdout,process.stderr).then(t=>{if(!t)process.exit()}).catch(t=>{if(!t.silent)console.error(t.nccError?t.message:t);process.exit(t.exitCode||1)})}else{module.exports=runCmd;api=true}function renderSummary(t,e,r,n,i,o){if(i&&!i.endsWith(sep))i+=sep;const s=Math.round(Buffer.byteLength(t,"utf8")/1024);const a=e?Math.round(Buffer.byteLength(e,"utf8")/1024):0;const c=Object.create(null);let u=s;let f=8+(e?4:0);for(const t of Object.keys(r)){const e=r[t].source;const n=Math.round((e.byteLength||Buffer.byteLength(e,"utf8"))/1024);c[t]=n;u+=n;if(t.length>f)f=t.length}const h=Object.keys(r).sort((t,e)=>c[t]>c[e]?1:-1);const l=u.toString().length;let p=`${s.toString().padStart(l," ")}kB  ${i}index${n}`;let d=e?`${a.toString().padStart(l," ")}kB  ${i}index${n}.map`:"";let v="",m=true;for(const t of h){if(m)m=false;else v+="\n";if(s<c[t]&&p){v+=p+"\n";p=null}if(a&&a<c[t]&&d){v+=d+"\n";d=null}v+=`${c[t].toString().padStart(l," ")}kB  ${i}${t}`}if(p){v+=(m?"":"\n")+p;m=false}if(d)v+=(m?"":"\n")+d;v+=`\n${u}kB  [${o}ms] - ncc ${nccVersion}`;return v}function nccError(t,e=1){const r=new Error(t);r.nccError=true;r.exitCode=e;throw r}async function runCmd(argv,stdout,stderr){let args;try{args=__webpack_require__(832)({"--debug":Boolean,"-d":"--debug","--external":[String],"-e":"--external","--out":String,"-o":"--out","--minify":Boolean,"-m":"--minify","--source-map":Boolean,"-s":"--source-map","--no-cache":Boolean,"-C":"--no-cache","--no-source-map-register":Boolean,"--quiet":Boolean,"-q":"--quiet","--watch":Boolean,"-w":"--watch","--v8-cache":Boolean,"--transpile-only":Boolean,"-t":"--transpile-only","--license":String,"--stats-out":String},{permissive:false,argv:argv})}catch(t){if(t.message.indexOf("Unknown or unexpected option")===-1)throw t;nccError(t.message+`\n${usage}`,2)}if(args._.length===0)nccError(`Error: No command specified\n${usage}`,2);let run=false;let outDir=args["--out"];const quiet=args["--quiet"];const statsOutFile=args["--stats-out"];switch(args._[0]){case"cache":if(args._.length>2)errTooManyArguments("cache");const flags=Object.keys(args).filter(t=>t.startsWith("--"));if(flags.length)errFlagNotCompatible(flags[0],"cache");const cacheDir=__webpack_require__(946);switch(args._[1]){case"clean":rimraf.sync(cacheDir);break;case"dir":stdout.write(cacheDir+"\n");break;case"size":__webpack_require__(74)(cacheDir,(t,e)=>{if(t){if(t.code==="ENOENT"){stdout.write("0MB\n");return}throw t}stdout.write(`${(e/1024/1024).toFixed(2)}MB\n`)});break;default:errInvalidCommand("cache "+args._[1])}break;case"run":if(args._.length>2)errTooManyArguments("run");if(args["--out"])errFlagNotCompatible("--out","run");if(args["--watch"])errFlagNotCompatible("--watch","run");outDir=resolve(__webpack_require__(87).tmpdir(),crypto.createHash("md5").update(resolve(args._[1]||".")).digest("hex"));if(existsSync(outDir))rimraf.sync(outDir);run=true;case"build":if(args._.length>2)errTooManyArguments("build");let startTime=Date.now();let ps;const buildFile=eval("require.resolve")(resolve(args._[1]||"."));const ext=buildFile.endsWith(".cjs")?".cjs":".js";const ncc=__webpack_require__(612)(buildFile,{debugLog:args["--debug"],minify:args["--minify"],externals:args["--external"],sourceMap:args["--source-map"]||run,sourceMapRegister:args["--no-source-map-register"]?false:undefined,cache:args["--no-cache"]?false:undefined,watch:args["--watch"],v8cache:args["--v8-cache"],transpileOnly:args["--transpile-only"],license:args["--license"],quiet:quiet});async function handler({err:err,code:code,map:map,assets:assets,symlinks:symlinks,stats:stats}){if(err){stderr.write(err+"\n");stdout.write("Watching for changes...\n");return}outDir=outDir||resolve(eval("'dist'"));mkdirp.sync(outDir);await Promise.all((await new Promise((t,e)=>glob(outDir+"/**/*.(js|cjs)",(r,n)=>r?e(r):t(n)))).map(t=>new Promise((e,r)=>unlink(t,t=>t?r(t):e()))));writeFileSync(`${outDir}/index${ext}`,code,{mode:code.match(shebangRegEx)?511:438});if(map)writeFileSync(`${outDir}/index${ext}.map`,map);for(const t of Object.keys(assets)){const e=outDir+"/"+t;mkdirp.sync(dirname(e));writeFileSync(e,assets[t].source,{mode:assets[t].permissions})}for(const t of Object.keys(symlinks)){const e=outDir+"/"+t;symlinkSync(symlinks[t],e)}if(!quiet){stdout.write(renderSummary(code,map,assets,ext,run?"":relative(process.cwd(),outDir),Date.now()-startTime)+"\n");if(args["--watch"])stdout.write("Watching for changes...\n")}if(statsOutFile)writeFileSync(statsOutFile,JSON.stringify(stats.toJson()));if(run){const t=resolve("/node_modules");let e=dirname(buildFile)+"/node_modules";do{if(e===t){e=undefined;break}if(existsSync(e))break}while(e=resolve(e,"../../node_modules"));if(e)symlinkSync(e,outDir+"/node_modules","junction");ps=__webpack_require__(129).fork(`${outDir}/index${ext}`,{stdio:api?"pipe":"inherit"});if(api){ps.stdout.pipe(stdout);ps.stderr.pipe(stderr)}return new Promise((t,e)=>{function exit(r){__webpack_require__(8).sync(outDir);if(r===0)t();else e({silent:true,exitCode:r});process.off("SIGTERM",exit);process.off("SIGINT",exit)}ps.on("exit",exit);process.on("SIGTERM",exit);process.on("SIGINT",exit)})}}if(args["--watch"]){ncc.handler(handler);ncc.rebuild(()=>{if(ps)ps.kill();startTime=Date.now();stdout.write("File change, rebuilding...\n")});return true}else{return ncc.then(handler)}break;case"help":nccError(usage,2);case"version":stdout.write(__webpack_require__(306).version+"\n");break;default:errInvalidCommand(args._[0],2)}function errTooManyArguments(t){nccError(`Error: Too many ${t} arguments provided\n${usage}`,2)}function errFlagNotCompatible(t,e){nccError(`Error: ${t} flag is not compatible with ncc ${e}\n${usage}`,2)}function errInvalidCommand(t){nccError(`Error: Invalid command "${t}"\n${usage}`,2)}process.on("unhandledRejection",t=>{throw t})}},946:(t,e,r)=>{t.exports=r(87).tmpdir()+"/ncc-cache"},681:t=>{t.exports=/^#![^\n\r]*[\r\n]/},612:t=>{"use strict";t.exports=require("./index.js")},357:t=>{"use strict";t.exports=require("assert")},129:t=>{"use strict";t.exports=require("child_process")},417:t=>{"use strict";t.exports=require("crypto")},614:t=>{"use strict";t.exports=require("events")},747:t=>{"use strict";t.exports=require("fs")},87:t=>{"use strict";t.exports=require("os")},622:t=>{"use strict";t.exports=require("path")},669:t=>{"use strict";t.exports=require("util")}};var __webpack_module_cache__={};function __webpack_require__(t){if(__webpack_module_cache__[t]){return __webpack_module_cache__[t].exports}var e=__webpack_module_cache__[t]={exports:{}};var r=true;try{__webpack_modules__[t](e,e.exports,__webpack_require__);r=false}finally{if(r)delete __webpack_module_cache__[t]}return e.exports}__webpack_require__.ab=__dirname+"/";return __webpack_require__(819)})();