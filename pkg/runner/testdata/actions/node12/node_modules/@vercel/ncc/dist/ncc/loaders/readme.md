# About this directory

This directory will contain:

- `relocate-loader.js` the ncc loader for handling CommonJS asset and reference relocations
- `shebang-loader.js` the ncc loader to ensure proper hash bang support in Node.js CLI files
- `ts-loader.js` the ncc loader for handling TypeScript

These are generated by the `build` step defined in `../../../package.json`.

These files are published to npm.
