{"name": "@vercel/ncc", "description": "Simple CLI for compiling a Node.js module into a single file, together with all its dependencies, gcc-style.", "version": "0.24.1", "repository": "vercel/ncc", "license": "MIT", "main": "./dist/ncc/index.js", "bin": {"ncc": "./dist/ncc/cli.js"}, "scripts": {"build": "node scripts/build", "build-test-binary": "cd test/binary && node-gyp rebuild && cp build/Release/hello.node ../integration/hello.node", "codecov": "codecov", "test": "node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest", "test-coverage": "node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest --coverage --globals \"{\\\"coverage\\\":true}\" && codecov", "prepublish": "yarn build"}, "devDependencies": {"@azure/cosmos": "^2.0.5", "@bugsnag/js": "^5.0.1", "@ffmpeg-installer/ffmpeg": "^1.0.17", "@google-cloud/bigquery": "^2.0.1", "@google-cloud/firestore": "^2.2.0", "@sentry/node": "^4.3.0", "@tensorflow/tfjs-node": "^0.3.0", "@zeit/webpack-asset-relocator-loader": "0.8.0", "analytics-node": "^3.3.0", "apollo-server-express": "^2.2.2", "arg": "^4.1.0", "auth0": "^2.14.0", "aws-sdk": "^2.356.0", "axios": "^0.18.1", "azure-storage": "^2.10.2", "browserify-middleware": "^8.1.1", "bytes": "^3.0.0", "canvas": "^2.2.0", "chromeless": "^1.5.2", "codecov": "^3.6.5", "consolidate": "^0.15.1", "copy": "^0.3.2", "core-js": "^2.5.7", "cowsay": "^1.3.1", "esm": "^3.2.22", "express": "^4.16.4", "fetch-h2": "^1.0.2", "firebase": "^6.1.1", "firebase-admin": "^6.3.0", "fluent-ffmpeg": "^2.1.2", "fontkit": "^1.7.7", "get-folder-size": "^2.0.0", "glob": "^7.1.3", "got": "^9.3.2", "graceful-fs": "^4.1.15", "graphql": "^14.0.2", "highlights": "^3.1.1", "hot-shots": "^5.9.2", "in-publish": "^2.0.0", "ioredis": "^4.2.0", "isomorphic-unfetch": "^3.0.0", "jest": "^26.3.0", "jimp": "^0.5.6", "jugglingdb": "2.0.1", "koa": "^2.6.2", "leveldown": "^5.6.0", "license-webpack-plugin": "^2.3.0", "lighthouse": "^5.0.0", "loopback": "^3.24.0", "mailgun": "^0.5.0", "mariadb": "^2.0.1-beta", "memcached": "^2.2.2", "mkdirp": "^0.5.1", "mongoose": "^5.3.12", "mysql": "^2.16.0", "node-gyp": "^3.8.0", "npm": "^6.13.4", "oracledb": "^4.2.0", "passport": "^0.4.0", "passport-google-oauth": "^1.0.0", "path-platform": "^0.11.15", "pdf2json": "^1.1.8", "pdfkit": "^0.8.3", "pg": "^7.6.1", "pug": "^2.0.3", "react": "^16.6.3", "react-dom": "^16.6.3", "redis": "^2.8.0", "request": "^2.88.0", "rxjs": "^6.3.3", "saslprep": "^1.0.2", "sequelize": "^5.8.6", "sharp": "^0.25.2", "shebang-loader": "^0.0.1", "socket.io": "^2.2.0", "source-map-support": "^0.5.9", "stripe": "^6.15.0", "swig": "^1.4.2", "terser": "^3.11.0", "the-answer": "^1.0.0", "tiny-json-http": "^7.0.2", "ts-loader": "^5.3.1", "tsconfig-paths": "^3.7.0", "tsconfig-paths-webpack-plugin": "^3.2.0", "twilio": "^3.23.2", "typescript": "^3.2.2", "vm2": "^3.6.6", "vue": "^2.5.17", "vue-server-renderer": "^2.5.17", "webpack": "5.0.0-beta.28", "when": "^3.7.8"}}