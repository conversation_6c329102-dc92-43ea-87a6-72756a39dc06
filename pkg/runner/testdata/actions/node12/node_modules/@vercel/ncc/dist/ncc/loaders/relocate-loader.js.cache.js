module.exports=(()=>{var __webpack_modules__={225:(module,__unused_webpack_exports,__webpack_require__)=>{module.exports=function(e,t){"use strict";var r={};function __nested_webpack_require_220__(t){if(r[t]){return r[t].exports}var i=r[t]={i:t,l:false,exports:{}};e[t].call(i.exports,i,i.exports,__nested_webpack_require_220__);i.l=true;return i.exports}function startup(){return __nested_webpack_require_220__(379)}return startup()}({12:function(e){"use strict";if(!process.version||process.version.indexOf("v0.")===0||process.version.indexOf("v1.")===0&&process.version.indexOf("v1.8.")!==0){e.exports={nextTick:nextTick}}else{e.exports=process}function nextTick(e,t,r,i){if(typeof e!=="function"){throw new TypeError('"callback" argument must be a function')}var n=arguments.length;var s,a;switch(n){case 0:case 1:return process.nextTick(e);case 2:return process.nextTick(function afterTickOne(){e.call(null,t)});case 3:return process.nextTick(function afterTickTwo(){e.call(null,t,r)});case 4:return process.nextTick(function afterTickThree(){e.call(null,t,r,i)});default:s=new Array(n-1);a=0;while(a<s.length){s[a++]=arguments[a]}return process.nextTick(function afterTick(){e.apply(null,s)})}}},16:function(e){e.exports=wrappy;function wrappy(e,t){if(e&&t)return wrappy(e)(t);if(typeof e!=="function")throw new TypeError("need wrapper function");Object.keys(e).forEach(function(t){wrapper[t]=e[t]});return wrapper;function wrapper(){var t=new Array(arguments.length);for(var r=0;r<t.length;r++){t[r]=arguments[r]}var i=e.apply(this,t);var n=t[t.length-1];if(typeof i==="function"&&i!==n){Object.keys(n).forEach(function(e){i[e]=n[e]})}return i}}},20:function(e){e.exports=function(e,r){var i=[];for(var n=0;n<e.length;n++){var s=r(e[n],n);if(t(s))i.push.apply(i,s);else i.push(s)}return i};var t=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},22:function(e,t,r){"use strict";var i=r(656);var n=r(817);var s=r(211);e.exports=function(e){if(typeof e!=="string"||e.length===0){return 0}var t=0;e=i(e);for(var r=0;r<e.length;r++){var a=n(e,r);if(a<=31||a>=127&&a<=159){continue}if(a>=65536){r++}if(s(a)){t+=2}else{t++}}return t}},30:function(e,t,r){"use strict";var i=r(64);var n=r(285);var s=r(468);var a=r(358);var u=e.exports=function(e,t,r){n.Transform.call(this,r);this.tracker=new a(e,t);this.name=e;this.id=this.tracker.id;this.tracker.on("change",delegateChange(this))};i.inherits(u,n.Transform);function delegateChange(e){return function(t,r,i){e.emit("change",t,r,e)}}u.prototype._transform=function(e,t,r){this.tracker.completeWork(e.length?e.length:1);this.push(e);r()};u.prototype._flush=function(e){this.tracker.finish();e()};s(u.prototype,"tracker").method("completed").method("addWork").method("finish")},55:function(e,t,r){var i=process.versions&&process.versions.node&&process.versions.node.split(".")||[];function specifierIncluded(e){var t=e.split(" ");var r=t.length>1?t[0]:"=";var n=(t.length>1?t[1]:t[0]).split(".");for(var s=0;s<3;++s){var a=Number(i[s]||0);var u=Number(n[s]||0);if(a===u){continue}if(r==="<"){return a<u}else if(r===">="){return a>=u}else{return false}}return r===">="}function matchesRange(e){var t=e.split(/ ?&& ?/);if(t.length===0){return false}for(var r=0;r<t.length;++r){if(!specifierIncluded(t[r])){return false}}return true}function versionIncluded(e){if(typeof e==="boolean"){return e}if(e&&typeof e==="object"){for(var t=0;t<e.length;++t){if(matchesRange(e[t])){return true}}return false}return matchesRange(e)}var n=r(347);var s={};for(var a in n){if(Object.prototype.hasOwnProperty.call(n,a)){s[a]=versionIncluded(n[a])}}e.exports=s},64:function(e){e.exports=__webpack_require__(669)},66:function(e){e.exports=__webpack_require__(747)},68:function(e){e.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t};var t=(new Error).stack;Error.prepareStackTrace=e;return t[2].getFileName()}},72:function(e,t,r){"use strict";const i=r(589);function isUrlRequest(e,t){if(/^[a-z][a-z0-9+.-]*:/i.test(e)&&!i.win32.isAbsolute(e)){return false}if(/^\/\//.test(e)){return false}if(/^[{}[\]#*;,'§$%&(=?`´^°<>]/.test(e)){return false}if((t===undefined||t===false)&&/^\//.test(e)){return false}return true}e.exports=isUrlRequest},77:function(e){e.exports=__webpack_require__(835)},83:function(e,t,r){var i=r(16);e.exports=i(once);e.exports.strict=i(onceStrict);once.proto=once(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return once(this)},configurable:true});Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return onceStrict(this)},configurable:true})});function once(e){var t=function(){if(t.called)return t.value;t.called=true;return t.value=e.apply(this,arguments)};t.called=false;return t}function onceStrict(e){var t=function(){if(t.called)throw new Error(t.onceError);t.called=true;return t.value=e.apply(this,arguments)};var r=e.name||"Function wrapped with `once`";t.onceError=r+" shouldn't be called more than once";t.called=false;return t}},86:function(e,t,r){"use strict";var i=r(12);e.exports=Writable;function WriteReq(e,t,r){this.chunk=e;this.encoding=t;this.callback=r;this.next=null}function CorkedRequest(e){var t=this;this.next=null;this.entry=null;this.finish=function(){onCorkedFinish(t,e)}}var n=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:i.nextTick;var s;Writable.WritableState=WritableState;var a=r(683);a.inherits=r(207);var u={deprecate:r(507)};var o=r(569);var l=r(945).Buffer;var f=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return l.from(e)}function _isUint8Array(e){return l.isBuffer(e)||e instanceof f}var h=r(972);a.inherits(Writable,o);function nop(){}function WritableState(e,t){s=s||r(98);e=e||{};var i=t instanceof s;this.objectMode=!!e.objectMode;if(i)this.objectMode=this.objectMode||!!e.writableObjectMode;var n=e.highWaterMark;var a=e.writableHighWaterMark;var u=this.objectMode?16:16*1024;if(n||n===0)this.highWaterMark=n;else if(i&&(a||a===0))this.highWaterMark=a;else this.highWaterMark=u;this.highWaterMark=Math.floor(this.highWaterMark);this.finalCalled=false;this.needDrain=false;this.ending=false;this.ended=false;this.finished=false;this.destroyed=false;var o=e.decodeStrings===false;this.decodeStrings=!o;this.defaultEncoding=e.defaultEncoding||"utf8";this.length=0;this.writing=false;this.corked=0;this.sync=true;this.bufferProcessing=false;this.onwrite=function(e){onwrite(t,e)};this.writecb=null;this.writelen=0;this.bufferedRequest=null;this.lastBufferedRequest=null;this.pendingcb=0;this.prefinished=false;this.errorEmitted=false;this.bufferedRequestCount=0;this.corkedRequestsFree=new CorkedRequest(this)}WritableState.prototype.getBuffer=function getBuffer(){var e=this.bufferedRequest;var t=[];while(e){t.push(e);e=e.next}return t};(function(){try{Object.defineProperty(WritableState.prototype,"buffer",{get:u.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer "+"instead.","DEP0003")})}catch(e){}})();var c;if(typeof Symbol==="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]==="function"){c=Function.prototype[Symbol.hasInstance];Object.defineProperty(Writable,Symbol.hasInstance,{value:function(e){if(c.call(this,e))return true;if(this!==Writable)return false;return e&&e._writableState instanceof WritableState}})}else{c=function(e){return e instanceof this}}function Writable(e){s=s||r(98);if(!c.call(Writable,this)&&!(this instanceof s)){return new Writable(e)}this._writableState=new WritableState(e,this);this.writable=true;if(e){if(typeof e.write==="function")this._write=e.write;if(typeof e.writev==="function")this._writev=e.writev;if(typeof e.destroy==="function")this._destroy=e.destroy;if(typeof e.final==="function")this._final=e.final}o.call(this)}Writable.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))};function writeAfterEnd(e,t){var r=new Error("write after end");e.emit("error",r);i.nextTick(t,r)}function validChunk(e,t,r,n){var s=true;var a=false;if(r===null){a=new TypeError("May not write null values to stream")}else if(typeof r!=="string"&&r!==undefined&&!t.objectMode){a=new TypeError("Invalid non-string/buffer chunk")}if(a){e.emit("error",a);i.nextTick(n,a);s=false}return s}Writable.prototype.write=function(e,t,r){var i=this._writableState;var n=false;var s=!i.objectMode&&_isUint8Array(e);if(s&&!l.isBuffer(e)){e=_uint8ArrayToBuffer(e)}if(typeof t==="function"){r=t;t=null}if(s)t="buffer";else if(!t)t=i.defaultEncoding;if(typeof r!=="function")r=nop;if(i.ended)writeAfterEnd(this,r);else if(s||validChunk(this,i,e,r)){i.pendingcb++;n=writeOrBuffer(this,i,s,e,t,r)}return n};Writable.prototype.cork=function(){var e=this._writableState;e.corked++};Writable.prototype.uncork=function(){var e=this._writableState;if(e.corked){e.corked--;if(!e.writing&&!e.corked&&!e.finished&&!e.bufferProcessing&&e.bufferedRequest)clearBuffer(this,e)}};Writable.prototype.setDefaultEncoding=function setDefaultEncoding(e){if(typeof e==="string")e=e.toLowerCase();if(!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);this._writableState.defaultEncoding=e;return this};function decodeChunk(e,t,r){if(!e.objectMode&&e.decodeStrings!==false&&typeof t==="string"){t=l.from(t,r)}return t}Object.defineProperty(Writable.prototype,"writableHighWaterMark",{enumerable:false,get:function(){return this._writableState.highWaterMark}});function writeOrBuffer(e,t,r,i,n,s){if(!r){var a=decodeChunk(t,i,n);if(i!==a){r=true;n="buffer";i=a}}var u=t.objectMode?1:i.length;t.length+=u;var o=t.length<t.highWaterMark;if(!o)t.needDrain=true;if(t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:n,isBuf:r,callback:s,next:null};if(l){l.next=t.lastBufferedRequest}else{t.bufferedRequest=t.lastBufferedRequest}t.bufferedRequestCount+=1}else{doWrite(e,t,false,u,i,n,s)}return o}function doWrite(e,t,r,i,n,s,a){t.writelen=i;t.writecb=a;t.writing=true;t.sync=true;if(r)e._writev(n,t.onwrite);else e._write(n,s,t.onwrite);t.sync=false}function onwriteError(e,t,r,n,s){--t.pendingcb;if(r){i.nextTick(s,n);i.nextTick(finishMaybe,e,t);e._writableState.errorEmitted=true;e.emit("error",n)}else{s(n);e._writableState.errorEmitted=true;e.emit("error",n);finishMaybe(e,t)}}function onwriteStateUpdate(e){e.writing=false;e.writecb=null;e.length-=e.writelen;e.writelen=0}function onwrite(e,t){var r=e._writableState;var i=r.sync;var s=r.writecb;onwriteStateUpdate(r);if(t)onwriteError(e,r,i,t,s);else{var a=needFinish(r);if(!a&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest){clearBuffer(e,r)}if(i){n(afterWrite,e,r,a,s)}else{afterWrite(e,r,a,s)}}}function afterWrite(e,t,r,i){if(!r)onwriteDrain(e,t);t.pendingcb--;i();finishMaybe(e,t)}function onwriteDrain(e,t){if(t.length===0&&t.needDrain){t.needDrain=false;e.emit("drain")}}function clearBuffer(e,t){t.bufferProcessing=true;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var i=t.bufferedRequestCount;var n=new Array(i);var s=t.corkedRequestsFree;s.entry=r;var a=0;var u=true;while(r){n[a]=r;if(!r.isBuf)u=false;r=r.next;a+=1}n.allBuffers=u;doWrite(e,t,true,t.length,n,"",s.finish);t.pendingcb++;t.lastBufferedRequest=null;if(s.next){t.corkedRequestsFree=s.next;s.next=null}else{t.corkedRequestsFree=new CorkedRequest(t)}t.bufferedRequestCount=0}else{while(r){var o=r.chunk;var l=r.encoding;var f=r.callback;var h=t.objectMode?1:o.length;doWrite(e,t,false,h,o,l,f);r=r.next;t.bufferedRequestCount--;if(t.writing){break}}if(r===null)t.lastBufferedRequest=null}t.bufferedRequest=r;t.bufferProcessing=false}Writable.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))};Writable.prototype._writev=null;Writable.prototype.end=function(e,t,r){var i=this._writableState;if(typeof e==="function"){r=e;e=null;t=null}else if(typeof t==="function"){r=t;t=null}if(e!==null&&e!==undefined)this.write(e,t);if(i.corked){i.corked=1;this.uncork()}if(!i.ending&&!i.finished)endWritable(this,i,r)};function needFinish(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function callFinal(e,t){e._final(function(r){t.pendingcb--;if(r){e.emit("error",r)}t.prefinished=true;e.emit("prefinish");finishMaybe(e,t)})}function prefinish(e,t){if(!t.prefinished&&!t.finalCalled){if(typeof e._final==="function"){t.pendingcb++;t.finalCalled=true;i.nextTick(callFinal,e,t)}else{t.prefinished=true;e.emit("prefinish")}}}function finishMaybe(e,t){var r=needFinish(t);if(r){prefinish(e,t);if(t.pendingcb===0){t.finished=true;e.emit("finish")}}return r}function endWritable(e,t,r){t.ending=true;finishMaybe(e,t);if(r){if(t.finished)i.nextTick(r);else e.once("finish",r)}t.ended=true;e.writable=false}function onCorkedFinish(e,t,r){var i=e.entry;e.entry=null;while(i){var n=i.callback;t.pendingcb--;n(r);i=i.next}if(t.corkedRequestsFree){t.corkedRequestsFree.next=e}else{t.corkedRequestsFree=e}}Object.defineProperty(Writable.prototype,"destroyed",{get:function(){if(this._writableState===undefined){return false}return this._writableState.destroyed},set:function(e){if(!this._writableState){return}this._writableState.destroyed=e}});Writable.prototype.destroy=h.destroy;Writable.prototype._undestroy=h.undestroy;Writable.prototype._destroy=function(e,t){this.end();t(e)}},98:function(e,t,r){"use strict";var i=r(12);var n=Object.keys||function(e){var t=[];for(var r in e){t.push(r)}return t};e.exports=Duplex;var s=r(683);s.inherits=r(207);var a=r(923);var u=r(86);s.inherits(Duplex,a);{var o=n(u.prototype);for(var l=0;l<o.length;l++){var f=o[l];if(!Duplex.prototype[f])Duplex.prototype[f]=u.prototype[f]}}function Duplex(e){if(!(this instanceof Duplex))return new Duplex(e);a.call(this,e);u.call(this,e);if(e&&e.readable===false)this.readable=false;if(e&&e.writable===false)this.writable=false;this.allowHalfOpen=true;if(e&&e.allowHalfOpen===false)this.allowHalfOpen=false;this.once("end",onend)}Object.defineProperty(Duplex.prototype,"writableHighWaterMark",{enumerable:false,get:function(){return this._writableState.highWaterMark}});function onend(){if(this.allowHalfOpen||this._writableState.ended)return;i.nextTick(onEndNT,this)}function onEndNT(e){e.end()}Object.defineProperty(Duplex.prototype,"destroyed",{get:function(){if(this._readableState===undefined||this._writableState===undefined){return false}return this._readableState.destroyed&&this._writableState.destroyed},set:function(e){if(this._readableState===undefined||this._writableState===undefined){return}this._readableState.destroyed=e;this._writableState.destroyed=e}});Duplex.prototype._destroy=function(e,t){this.push(null);this.end();i.nextTick(t,e)}},106:function(e,t,r){"use strict";const i=r(146);function getOptions(e){const t=e.query;if(typeof t==="string"&&t!==""){return i(e.query)}if(!t||typeof t!=="object"){return null}return t}e.exports=getOptions},113:function(e){"use strict";const t=/^[A-Z]:[/\\]|^\\\\/i;function urlToRequest(e,r){if(e===""){return""}const i=/^[^?]*~/;let n;if(t.test(e)){n=e}else if(r!==undefined&&r!==false&&/^\//.test(e)){switch(typeof r){case"string":if(i.test(r)){n=r.replace(/([^~/])$/,"$1/")+e.slice(1)}else{n=r+e}break;case"boolean":n=e;break;default:throw new Error("Unexpected parameters to loader-utils 'urlToRequest': url = "+e+", root = "+r+".")}}else if(/^\.\.?\//.test(e)){n=e}else{n="./"+e}if(i.test(n)){n=n.replace(i,"")}return n}e.exports=urlToRequest},129:function(e,t,r){e.exports=realpath;realpath.realpath=realpath;realpath.sync=realpathSync;realpath.realpathSync=realpathSync;realpath.monkeypatch=monkeypatch;realpath.unmonkeypatch=unmonkeypatch;var i=r(66);var n=i.realpath;var s=i.realpathSync;var a=process.version;var u=/^v[0-5]\./.test(a);var o=r(544);function newError(e){return e&&e.syscall==="realpath"&&(e.code==="ELOOP"||e.code==="ENOMEM"||e.code==="ENAMETOOLONG")}function realpath(e,t,r){if(u){return n(e,t,r)}if(typeof t==="function"){r=t;t=null}n(e,t,function(i,n){if(newError(i)){o.realpath(e,t,r)}else{r(i,n)}})}function realpathSync(e,t){if(u){return s(e,t)}try{return s(e,t)}catch(r){if(newError(r)){return o.realpathSync(e,t)}else{throw r}}}function monkeypatch(){i.realpath=realpath;i.realpathSync=realpathSync}function unmonkeypatch(){i.realpath=n;i.realpathSync=s}},131:function(e,t,r){var i=r(589).sep||"/";e.exports=fileUriToPath;function fileUriToPath(e){if("string"!=typeof e||e.length<=7||"file://"!=e.substring(0,7)){throw new TypeError("must pass in a file:// URI to convert to a file path")}var t=decodeURI(e.substring(7));var r=t.indexOf("/");var n=t.substring(0,r);var s=t.substring(r+1);if("localhost"==n)n="";if(n){n=i+i+n}s=s.replace(/^(.+)\|/,"$1:");if(i=="\\"){s=s.replace(/\//g,"\\")}if(/^.+\:/.test(s)){}else{s=i+s}return n+s}},140:function(e,t,r){"use strict";const i=Object.getPrototypeOf||(e=>e.__proto__);const n=e=>{if(e.acorn)return e.acorn;const t=r(996);if(t.version.indexOf("6.")!=0&&t.version.indexOf("6.0.")==0&&t.version.indexOf("7.")!=0){throw new Error(`acorn-private-class-elements requires acorn@^6.1.0 or acorn@7.0.0, not ${t.version}`)}for(let r=e;r&&r!==t.Parser;r=i(r)){if(r!==t.Parser){throw new Error("acorn-private-class-elements does not support mixing different acorn copies")}}return t};e.exports=function(e){if(e.prototype.parsePrivateName){return e}const t=n(e);e=class extends e{_branch(){this.__branch=this.__branch||new e({ecmaVersion:this.options.ecmaVersion},this.input);this.__branch.end=this.end;this.__branch.pos=this.pos;this.__branch.type=this.type;this.__branch.value=this.value;this.__branch.containsEsc=this.containsEsc;return this.__branch}parsePrivateClassElementName(e){e.computed=false;e.key=this.parsePrivateName();if(e.key.name=="constructor")this.raise(e.key.start,"Classes may not have a private element named constructor");const t={get:"set",set:"get"}[e.kind];const r=this._privateBoundNames;if(Object.prototype.hasOwnProperty.call(r,e.key.name)&&r[e.key.name]!==t){this.raise(e.start,"Duplicate private element")}r[e.key.name]=e.kind||true;delete this._unresolvedPrivateNames[e.key.name];return e.key}parsePrivateName(){const e=this.startNode();e.name=this.value;this.next();this.finishNode(e,"PrivateName");if(this.options.allowReserved=="never")this.checkUnreserved(e);return e}getTokenFromCode(e){if(e===35){++this.pos;const e=this.readWord1();return this.finishToken(this.privateNameToken,e)}return super.getTokenFromCode(e)}parseClass(e,t){const r=this._outerPrivateBoundNames;this._outerPrivateBoundNames=this._privateBoundNames;this._privateBoundNames=Object.create(this._privateBoundNames||null);const i=this._outerUnresolvedPrivateNames;this._outerUnresolvedPrivateNames=this._unresolvedPrivateNames;this._unresolvedPrivateNames=Object.create(null);const n=super.parseClass(e,t);const s=this._unresolvedPrivateNames;this._privateBoundNames=this._outerPrivateBoundNames;this._outerPrivateBoundNames=r;this._unresolvedPrivateNames=this._outerUnresolvedPrivateNames;this._outerUnresolvedPrivateNames=i;if(!this._unresolvedPrivateNames){const e=Object.keys(s);if(e.length){e.sort((e,t)=>s[e]-s[t]);this.raise(s[e[0]],"Usage of undeclared private name")}}else Object.assign(this._unresolvedPrivateNames,s);return n}parseClassSuper(e){const t=this._privateBoundNames;this._privateBoundNames=this._outerPrivateBoundNames;const r=this._unresolvedPrivateNames;this._unresolvedPrivateNames=this._outerUnresolvedPrivateNames;const i=super.parseClassSuper(e);this._privateBoundNames=t;this._unresolvedPrivateNames=r;return i}parseSubscript(e,r,i,n,s){if(!this.eat(t.tokTypes.dot)){return super.parseSubscript(e,r,i,n,s)}let a=this.startNodeAt(r,i);a.object=e;a.computed=false;if(this.type==this.privateNameToken){if(e.type=="Super"){this.raise(this.start,"Cannot access private element on super")}a.property=this.parsePrivateName();if(!this._privateBoundNames||!this._privateBoundNames[a.property.name]){if(!this._unresolvedPrivateNames){this.raise(a.property.start,"Usage of undeclared private name")}this._unresolvedPrivateNames[a.property.name]=a.property.start}}else{a.property=this.parseIdent(true)}return this.finishNode(a,"MemberExpression")}parseMaybeUnary(e,t){const r=super.parseMaybeUnary(e,t);if(r.operator=="delete"){if(r.argument.type=="MemberExpression"&&r.argument.property.type=="PrivateName"){this.raise(r.start,"Private elements may not be deleted")}}return r}};e.prototype.privateNameToken=new t.TokenType("privateName");return e}},146:function(e,t,r){"use strict";const i=r(722);const n={null:null,true:true,false:false};function parseQuery(e){if(e.substr(0,1)!=="?"){throw new Error("A valid query string passed to parseQuery should begin with '?'")}e=e.substr(1);if(!e){return{}}if(e.substr(0,1)==="{"&&e.substr(-1)==="}"){return i.parse(e)}const t=e.split(/[,&]/g);const r={};t.forEach(e=>{const t=e.indexOf("=");if(t>=0){let i=e.substr(0,t);let s=decodeURIComponent(e.substr(t+1));if(n.hasOwnProperty(s)){s=n[s]}if(i.substr(-2)==="[]"){i=decodeURIComponent(i.substr(0,i.length-2));if(!Array.isArray(r[i])){r[i]=[]}r[i].push(s)}else{i=decodeURIComponent(i);r[i]=s}}else{if(e.substr(0,1)==="-"){r[decodeURIComponent(e.substr(1))]=false}else if(e.substr(0,1)==="+"){r[decodeURIComponent(e.substr(1))]=true}else{r[decodeURIComponent(e)]=true}}});return r}e.exports=parseQuery},150:function(e){"use strict";e.exports=function spin(e,t){return e[t%e.length]}},156:function(e){"use strict";var t=Object.getOwnPropertySymbols;var r=Object.prototype.hasOwnProperty;var i=Object.prototype.propertyIsEnumerable;function toObject(e){if(e===null||e===undefined){throw new TypeError("Object.assign cannot be called with null or undefined")}return Object(e)}function shouldUseNative(){try{if(!Object.assign){return false}var e=new String("abc");e[5]="de";if(Object.getOwnPropertyNames(e)[0]==="5"){return false}var t={};for(var r=0;r<10;r++){t["_"+String.fromCharCode(r)]=r}var i=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if(i.join("")!=="0123456789"){return false}var n={};"abcdefghijklmnopqrst".split("").forEach(function(e){n[e]=e});if(Object.keys(Object.assign({},n)).join("")!=="abcdefghijklmnopqrst"){return false}return true}catch(e){return false}}e.exports=shouldUseNative()?Object.assign:function(e,n){var s;var a=toObject(e);var u;for(var o=1;o<arguments.length;o++){s=Object(arguments[o]);for(var l in s){if(r.call(s,l)){a[l]=s[l]}}if(t){u=t(s);for(var f=0;f<u.length;f++){if(i.call(s,u[f])){a[u[f]]=s[u[f]]}}}}return a}},160:function(e,t,r){"use strict";var i=r(156);e.exports=function(){return n.newThemeSet()};var n={};n.baseTheme=r(609);n.newTheme=function(e,t){if(!t){t=e;e=this.baseTheme}return i({},e,t)};n.getThemeNames=function(){return Object.keys(this.themes)};n.addTheme=function(e,t,r){this.themes[e]=this.newTheme(t,r)};n.addToAllThemes=function(e){var t=this.themes;Object.keys(t).forEach(function(r){i(t[r],e)});i(this.baseTheme,e)};n.getTheme=function(e){if(!this.themes[e])throw this.newMissingThemeError(e);return this.themes[e]};n.setDefault=function(e,t){if(t==null){t=e;e={}}var r=e.platform==null?"fallback":e.platform;var i=!!e.hasUnicode;var n=!!e.hasColor;if(!this.defaults[r])this.defaults[r]={true:{},false:{}};this.defaults[r][i][n]=t};n.getDefault=function(e){if(!e)e={};var t=e.platform||process.platform;var r=this.defaults[t]||this.defaults.fallback;var n=!!e.hasUnicode;var s=!!e.hasColor;if(!r)throw this.newMissingDefaultThemeError(t,n,s);if(!r[n][s]){if(n&&s&&r[!n][s]){n=false}else if(n&&s&&r[n][!s]){s=false}else if(n&&s&&r[!n][!s]){n=false;s=false}else if(n&&!s&&r[!n][s]){n=false}else if(!n&&s&&r[n][!s]){s=false}else if(r===this.defaults.fallback){throw this.newMissingDefaultThemeError(t,n,s)}}if(r[n][s]){return this.getTheme(r[n][s])}else{return this.getDefault(i({},e,{platform:"fallback"}))}};n.newMissingThemeError=function newMissingThemeError(e){var t=new Error('Could not find a gauge theme named "'+e+'"');Error.captureStackTrace.call(t,newMissingThemeError);t.theme=e;t.code="EMISSINGTHEME";return t};n.newMissingDefaultThemeError=function newMissingDefaultThemeError(e,t,r){var i=new Error("Could not find a gauge theme for your platform/unicode/color use combo:\n"+"    platform = "+e+"\n"+"    hasUnicode = "+t+"\n"+"    hasColor = "+r);Error.captureStackTrace.call(i,newMissingDefaultThemeError);i.platform=e;i.hasUnicode=t;i.hasColor=r;i.code="EMISSINGTHEME";return i};n.newThemeSet=function(){var e=function(t){return e.getDefault(t)};return i(e,n,{themes:i({},this.themes),baseTheme:i({},this.baseTheme),defaults:JSON.parse(JSON.stringify(this.defaults||{}))})}},203:function(e,t,r){"use strict";var i=r(814);var n=r(765);var s=r(649);var a=r(22);e.exports=function(e,t,r){i("ONN",[e,t,r]);if(r<0)r=0;if(r>1)r=1;if(t<=0)return"";var s=Math.round(t*r);var a=t-s;var u=[{type:"complete",value:repeat(e.complete,s),length:s},{type:"remaining",value:repeat(e.remaining,a),length:a}];return n(t,u,e)};function repeat(e,t){var r="";var i=t;do{if(i%2){r+=e}i=Math.floor(i/2);e+=e}while(i&&a(r)<t);return s(r,t)}},204:function(e){e.exports=__webpack_require__(129)},205:function(e,t,r){"use strict";var i=r(64);var n=t.User=function User(e){var t=new Error(e);Error.captureStackTrace(t,User);t.code="EGAUGE";return t};t.MissingTemplateValue=function MissingTemplateValue(e,t){var r=new n(i.format('Missing template value "%s"',e.type));Error.captureStackTrace(r,MissingTemplateValue);r.template=e;r.values=t;return r};t.Internal=function Internal(e){var t=new Error(e);Error.captureStackTrace(t,Internal);t.code="EGAUGEINTERNAL";return t}},207:function(e,t,r){try{var i=r(64);if(typeof i.inherits!=="function")throw"";e.exports=i.inherits}catch(t){e.exports=r(714)}},211:function(e,t,r){"use strict";var i=r(877);e.exports=function(e){if(i(e)){return false}if(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141)){return true}return false}},228:function(e,t,r){const{encode:i,decode:n}=r(899);function traceSegment(e,t,r,i,n){const s=t[r];if(!s)return null;let a=0;let u=s.length-1;while(a<=u){const t=a+u>>1;const r=s[t];if(r[0]===i){return{source:r[1],line:r[2],column:r[3],name:e.names[r[4]]||n}}if(r[0]>i)u=t-1;else a=t+1}return null}e.exports=function(e,t){const r=[];const s=[];const a=[];const u=[];const o=n(e.mappings);for(const i of n(t.mappings)){const n=[];for(const u of i){const i=traceSegment(e,o,u[2],u[3],t.names[u[4]]);if(i){const t=e.sources[i.source];let o=r.lastIndexOf(t);if(o===-1){o=r.length;r.push(t);s[o]=e.sourcesContent[i.source]}else if(s[o]==null){s[o]=e.sourcesContent[i.source]}const l=[u[0],o,i.line,i.column];if(i.name){let e=a.indexOf(i.name);if(e===-1){e=a.length;a.push(i.name)}l[4]=e}n.push(l)}}u.push(n)}return{version:3,file:null,sources:r,mappings:i(u),names:a,sourcesContent:s}}},238:function(e,t,r){"use strict";t.TrackerGroup=r(604);t.Tracker=r(358);t.TrackerStream=r(30)},254:function(e,t,r){"use strict";var i=r(354);var n=r(160);var s=e.exports=new n;s.addTheme("ASCII",{preProgressbar:"[",postProgressbar:"]",progressbarTheme:{complete:"#",remaining:"."},activityIndicatorTheme:"-\\|/",preSubsection:">"});s.addTheme("colorASCII",s.getTheme("ASCII"),{progressbarTheme:{preComplete:i.color("inverse"),complete:" ",postComplete:i.color("stopInverse"),preRemaining:i.color("brightBlack"),remaining:".",postRemaining:i.color("reset")}});s.addTheme("brailleSpinner",{preProgressbar:"⸨",postProgressbar:"⸩",progressbarTheme:{complete:"░",remaining:"⠂"},activityIndicatorTheme:"⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏",preSubsection:">"});s.addTheme("colorBrailleSpinner",s.getTheme("brailleSpinner"),{progressbarTheme:{preComplete:i.color("inverse"),complete:" ",postComplete:i.color("stopInverse"),preRemaining:i.color("brightBlack"),remaining:"░",postRemaining:i.color("reset")}});s.setDefault({},"ASCII");s.setDefault({hasColor:true},"colorASCII");s.setDefault({platform:"darwin",hasUnicode:true},"brailleSpinner");s.setDefault({platform:"darwin",hasUnicode:true,hasColor:true},"colorBrailleSpinner")},285:function(e,t,r){var i=r(688);if(process.env.READABLE_STREAM==="disable"&&i){e.exports=i;t=e.exports=i.Readable;t.Readable=i.Readable;t.Writable=i.Writable;t.Duplex=i.Duplex;t.Transform=i.Transform;t.PassThrough=i.PassThrough;t.Stream=i}else{t=e.exports=r(923);t.Stream=i||t;t.Readable=t;t.Writable=r(86);t.Duplex=r(98);t.Transform=r(955);t.PassThrough=r(502)}},287:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=parse;var n=r(320);var s=_interopRequireWildcard(n);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}var a=void 0;var u=void 0;var o=void 0;var l=void 0;var f=void 0;var h=void 0;var c=void 0;var p=void 0;var d=void 0;function parse(e,t){a=String(e);u="start";o=[];l=0;f=1;h=0;c=undefined;p=undefined;d=undefined;do{c=lex();E[u]()}while(c.type!=="eof");if(typeof t==="function"){return internalize({"":d},"",t)}return d}function internalize(e,t,r){var n=e[t];if(n!=null&&(typeof n==="undefined"?"undefined":i(n))==="object"){for(var s in n){var a=internalize(n,s,r);if(a===undefined){delete n[s]}else{n[s]=a}}}return r.call(e,t,n)}var v=void 0;var y=void 0;var g=void 0;var b=void 0;var _=void 0;function lex(){v="default";y="";g=false;b=1;for(;;){_=peek();var e=m[v]();if(e){return e}}}function peek(){if(a[l]){return String.fromCodePoint(a.codePointAt(l))}}function read(){var e=peek();if(e==="\n"){f++;h=0}else if(e){h+=e.length}else{h++}if(e){l+=e.length}return e}var m={default:function _default(){switch(_){case"\t":case"\v":case"\f":case" ":case" ":case"\ufeff":case"\n":case"\r":case"\u2028":case"\u2029":read();return;case"/":read();v="comment";return;case undefined:read();return newToken("eof")}if(s.isSpaceSeparator(_)){read();return}return m[u]()},comment:function comment(){switch(_){case"*":read();v="multiLineComment";return;case"/":read();v="singleLineComment";return}throw invalidChar(read())},multiLineComment:function multiLineComment(){switch(_){case"*":read();v="multiLineCommentAsterisk";return;case undefined:throw invalidChar(read())}read()},multiLineCommentAsterisk:function multiLineCommentAsterisk(){switch(_){case"*":read();return;case"/":read();v="default";return;case undefined:throw invalidChar(read())}read();v="multiLineComment"},singleLineComment:function singleLineComment(){switch(_){case"\n":case"\r":case"\u2028":case"\u2029":read();v="default";return;case undefined:read();return newToken("eof")}read()},value:function value(){switch(_){case"{":case"[":return newToken("punctuator",read());case"n":read();literal("ull");return newToken("null",null);case"t":read();literal("rue");return newToken("boolean",true);case"f":read();literal("alse");return newToken("boolean",false);case"-":case"+":if(read()==="-"){b=-1}v="sign";return;case".":y=read();v="decimalPointLeading";return;case"0":y=read();v="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":y=read();v="decimalInteger";return;case"I":read();literal("nfinity");return newToken("numeric",Infinity);case"N":read();literal("aN");return newToken("numeric",NaN);case'"':case"'":g=read()==='"';y="";v="string";return}throw invalidChar(read())},identifierNameStartEscape:function identifierNameStartEscape(){if(_!=="u"){throw invalidChar(read())}read();var e=unicodeEscape();switch(e){case"$":case"_":break;default:if(!s.isIdStartChar(e)){throw invalidIdentifier()}break}y+=e;v="identifierName"},identifierName:function identifierName(){switch(_){case"$":case"_":case"‌":case"‍":y+=read();return;case"\\":read();v="identifierNameEscape";return}if(s.isIdContinueChar(_)){y+=read();return}return newToken("identifier",y)},identifierNameEscape:function identifierNameEscape(){if(_!=="u"){throw invalidChar(read())}read();var e=unicodeEscape();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!s.isIdContinueChar(e)){throw invalidIdentifier()}break}y+=e;v="identifierName"},sign:function sign(){switch(_){case".":y=read();v="decimalPointLeading";return;case"0":y=read();v="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":y=read();v="decimalInteger";return;case"I":read();literal("nfinity");return newToken("numeric",b*Infinity);case"N":read();literal("aN");return newToken("numeric",NaN)}throw invalidChar(read())},zero:function zero(){switch(_){case".":y+=read();v="decimalPoint";return;case"e":case"E":y+=read();v="decimalExponent";return;case"x":case"X":y+=read();v="hexadecimal";return}return newToken("numeric",b*0)},decimalInteger:function decimalInteger(){switch(_){case".":y+=read();v="decimalPoint";return;case"e":case"E":y+=read();v="decimalExponent";return}if(s.isDigit(_)){y+=read();return}return newToken("numeric",b*Number(y))},decimalPointLeading:function decimalPointLeading(){if(s.isDigit(_)){y+=read();v="decimalFraction";return}throw invalidChar(read())},decimalPoint:function decimalPoint(){switch(_){case"e":case"E":y+=read();v="decimalExponent";return}if(s.isDigit(_)){y+=read();v="decimalFraction";return}return newToken("numeric",b*Number(y))},decimalFraction:function decimalFraction(){switch(_){case"e":case"E":y+=read();v="decimalExponent";return}if(s.isDigit(_)){y+=read();return}return newToken("numeric",b*Number(y))},decimalExponent:function decimalExponent(){switch(_){case"+":case"-":y+=read();v="decimalExponentSign";return}if(s.isDigit(_)){y+=read();v="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentSign:function decimalExponentSign(){if(s.isDigit(_)){y+=read();v="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentInteger:function decimalExponentInteger(){if(s.isDigit(_)){y+=read();return}return newToken("numeric",b*Number(y))},hexadecimal:function hexadecimal(){if(s.isHexDigit(_)){y+=read();v="hexadecimalInteger";return}throw invalidChar(read())},hexadecimalInteger:function hexadecimalInteger(){if(s.isHexDigit(_)){y+=read();return}return newToken("numeric",b*Number(y))},string:function string(){switch(_){case"\\":read();y+=escape();return;case'"':if(g){read();return newToken("string",y)}y+=read();return;case"'":if(!g){read();return newToken("string",y)}y+=read();return;case"\n":case"\r":throw invalidChar(read());case"\u2028":case"\u2029":separatorChar(_);break;case undefined:throw invalidChar(read())}y+=read()},start:function start(){switch(_){case"{":case"[":return newToken("punctuator",read())}v="value"},beforePropertyName:function beforePropertyName(){switch(_){case"$":case"_":y=read();v="identifierName";return;case"\\":read();v="identifierNameStartEscape";return;case"}":return newToken("punctuator",read());case'"':case"'":g=read()==='"';v="string";return}if(s.isIdStartChar(_)){y+=read();v="identifierName";return}throw invalidChar(read())},afterPropertyName:function afterPropertyName(){if(_===":"){return newToken("punctuator",read())}throw invalidChar(read())},beforePropertyValue:function beforePropertyValue(){v="value"},afterPropertyValue:function afterPropertyValue(){switch(_){case",":case"}":return newToken("punctuator",read())}throw invalidChar(read())},beforeArrayValue:function beforeArrayValue(){if(_==="]"){return newToken("punctuator",read())}v="value"},afterArrayValue:function afterArrayValue(){switch(_){case",":case"]":return newToken("punctuator",read())}throw invalidChar(read())},end:function end(){throw invalidChar(read())}};function newToken(e,t){return{type:e,value:t,line:f,column:h}}function literal(e){var t=true;var r=false;var i=undefined;try{for(var n=e[Symbol.iterator](),s;!(t=(s=n.next()).done);t=true){var a=s.value;var u=peek();if(u!==a){throw invalidChar(read())}read()}}catch(e){r=true;i=e}finally{try{if(!t&&n.return){n.return()}}finally{if(r){throw i}}}}function escape(){var e=peek();switch(e){case"b":read();return"\b";case"f":read();return"\f";case"n":read();return"\n";case"r":read();return"\r";case"t":read();return"\t";case"v":read();return"\v";case"0":read();if(s.isDigit(peek())){throw invalidChar(read())}return"\0";case"x":read();return hexEscape();case"u":read();return unicodeEscape();case"\n":case"\u2028":case"\u2029":read();return"";case"\r":read();if(peek()==="\n"){read()}return"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw invalidChar(read());case undefined:throw invalidChar(read())}return read()}function hexEscape(){var e="";var t=peek();if(!s.isHexDigit(t)){throw invalidChar(read())}e+=read();t=peek();if(!s.isHexDigit(t)){throw invalidChar(read())}e+=read();return String.fromCodePoint(parseInt(e,16))}function unicodeEscape(){var e="";var t=4;while(t-- >0){var r=peek();if(!s.isHexDigit(r)){throw invalidChar(read())}e+=read()}return String.fromCodePoint(parseInt(e,16))}var E={start:function start(){if(c.type==="eof"){throw invalidEOF()}push()},beforePropertyName:function beforePropertyName(){switch(c.type){case"identifier":case"string":p=c.value;u="afterPropertyName";return;case"punctuator":pop();return;case"eof":throw invalidEOF()}},afterPropertyName:function afterPropertyName(){if(c.type==="eof"){throw invalidEOF()}u="beforePropertyValue"},beforePropertyValue:function beforePropertyValue(){if(c.type==="eof"){throw invalidEOF()}push()},beforeArrayValue:function beforeArrayValue(){if(c.type==="eof"){throw invalidEOF()}if(c.type==="punctuator"&&c.value==="]"){pop();return}push()},afterPropertyValue:function afterPropertyValue(){if(c.type==="eof"){throw invalidEOF()}switch(c.value){case",":u="beforePropertyName";return;case"}":pop()}},afterArrayValue:function afterArrayValue(){if(c.type==="eof"){throw invalidEOF()}switch(c.value){case",":u="beforeArrayValue";return;case"]":pop()}},end:function end(){}};function push(){var e=void 0;switch(c.type){case"punctuator":switch(c.value){case"{":e={};break;case"[":e=[];break}break;case"null":case"boolean":case"numeric":case"string":e=c.value;break}if(d===undefined){d=e}else{var t=o[o.length-1];if(Array.isArray(t)){t.push(e)}else{t[p]=e}}if(e!==null&&(typeof e==="undefined"?"undefined":i(e))==="object"){o.push(e);if(Array.isArray(e)){u="beforeArrayValue"}else{u="beforePropertyName"}}else{var r=o[o.length-1];if(r==null){u="end"}else if(Array.isArray(r)){u="afterArrayValue"}else{u="afterPropertyValue"}}}function pop(){o.pop();var e=o[o.length-1];if(e==null){u="end"}else if(Array.isArray(e)){u="afterArrayValue"}else{u="afterPropertyValue"}}function invalidChar(e){if(e===undefined){return syntaxError("JSON5: invalid end of input at "+f+":"+h)}return syntaxError("JSON5: invalid character '"+formatChar(e)+"' at "+f+":"+h)}function invalidEOF(){return syntaxError("JSON5: invalid end of input at "+f+":"+h)}function invalidIdentifier(){h-=5;return syntaxError("JSON5: invalid identifier character at "+f+":"+h)}function separatorChar(e){console.warn("JSON5: '"+e+"' is not valid ECMAScript; consider escaping")}function formatChar(e){var t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e]){return t[e]}if(e<" "){var r=e.charCodeAt(0).toString(16);return"\\x"+("00"+r).substring(r.length)}return e}function syntaxError(e){var t=new SyntaxError(e);t.lineNumber=f;t.columnNumber=h;return t}e.exports=t["default"]},288:function(e,t,r){const i=r(589);e.exports=function(e,t,r){const n=i.extname(e);let s=e,a=0;while(s in r&&r[s]!==t)s=e.substr(0,e.length-n.length)+ ++a+n;r[s]=t;return s}},298:function(e){e.exports=__webpack_require__(417)},300:function(e){"use strict";function getRemainingRequest(e){if(e.remainingRequest){return e.remainingRequest}const t=e.loaders.slice(e.loaderIndex+1).map(e=>e.request).concat([e.resource]);return t.join("!")}e.exports=getRemainingRequest},308:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});function _interopDefault(e){return e&&typeof e==="object"&&"default"in e?e["default"]:e}var i=r(589);var n=_interopDefault(i);var s=r(825);var a=_interopDefault(r(64));const u=function addExtension(e,t=".js"){if(!i.extname(e))e+=t;return e};const o={ArrayPattern(e,t){for(const r of t.elements){if(r)o[r.type](e,r)}},AssignmentPattern(e,t){o[t.left.type](e,t.left)},Identifier(e,t){e.push(t.name)},MemberExpression(){},ObjectPattern(e,t){for(const r of t.properties){if(r.type==="RestElement"){o.RestElement(e,r)}else{o[r.value.type](e,r.value)}}},RestElement(e,t){o[t.argument.type](e,t.argument)}};const l=function extractAssignedNames(e){const t=[];o[e.type](t,e);return t};const f={const:true,let:true};class Scope{constructor(e={}){this.parent=e.parent;this.isBlockScope=!!e.block;this.declarations=Object.create(null);if(e.params){e.params.forEach(e=>{l(e).forEach(e=>{this.declarations[e]=true})})}}addDeclaration(e,t,r){if(!t&&this.isBlockScope){this.parent.addDeclaration(e,t,r)}else if(e.id){l(e.id).forEach(e=>{this.declarations[e]=true})}}contains(e){return this.declarations[e]||(this.parent?this.parent.contains(e):false)}}const h=function attachScopes(e,t="scope"){let r=new Scope;s.walk(e,{enter(e,i){if(/(Function|Class)Declaration/.test(e.type)){r.addDeclaration(e,false,false)}if(e.type==="VariableDeclaration"){const t=e.kind;const i=f[t];e.declarations.forEach(e=>{r.addDeclaration(e,i,true)})}let n;if(/Function/.test(e.type)){n=new Scope({parent:r,block:false,params:e.params});if(e.type==="FunctionExpression"&&e.id){n.addDeclaration(e,false,false)}}if(e.type==="BlockStatement"&&!/Function/.test(i.type)){n=new Scope({parent:r,block:true})}if(e.type==="CatchClause"){n=new Scope({parent:r,params:e.param?[e.param]:[],block:true})}if(n){Object.defineProperty(e,t,{value:n,configurable:true});r=n}},leave(e){if(e[t])r=r.parent}});return r};function createCommonjsModule(e,t){return t={exports:{}},e(t,t.exports),t.exports}var c=createCommonjsModule(function(e,t){t.isInteger=(e=>{if(typeof e==="number"){return Number.isInteger(e)}if(typeof e==="string"&&e.trim()!==""){return Number.isInteger(Number(e))}return false});t.find=((e,t)=>e.nodes.find(e=>e.type===t));t.exceedsLimit=((e,r,i=1,n)=>{if(n===false)return false;if(!t.isInteger(e)||!t.isInteger(r))return false;return(Number(r)-Number(e))/Number(i)>=n});t.escapeNode=((e,t=0,r)=>{let i=e.nodes[t];if(!i)return;if(r&&i.type===r||i.type==="open"||i.type==="close"){if(i.escaped!==true){i.value="\\"+i.value;i.escaped=true}}});t.encloseBrace=(e=>{if(e.type!=="brace")return false;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}return false});t.isInvalidBrace=(e=>{if(e.type!=="brace")return false;if(e.invalid===true||e.dollar)return true;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}if(e.open!==true||e.close!==true){e.invalid=true;return true}return false});t.isOpenOrClose=(e=>{if(e.type==="open"||e.type==="close"){return true}return e.open===true||e.close===true});t.reduce=(e=>e.reduce((e,t)=>{if(t.type==="text")e.push(t.value);if(t.type==="range")t.type="text";return e},[]));t.flatten=((...e)=>{const t=[];const r=e=>{for(let i=0;i<e.length;i++){let n=e[i];Array.isArray(n)?r(n,t):n!==void 0&&t.push(n)}return t};r(e);return t})});var p=c.isInteger;var d=c.find;var v=c.exceedsLimit;var y=c.escapeNode;var g=c.encloseBrace;var b=c.isInvalidBrace;var _=c.isOpenOrClose;var m=c.reduce;var E=c.flatten;var D=(e,t={})=>{let r=(e,i={})=>{let n=t.escapeInvalid&&c.isInvalidBrace(i);let s=e.invalid===true&&t.escapeInvalid===true;let a="";if(e.value){if((n||s)&&c.isOpenOrClose(e)){return"\\"+e.value}return e.value}if(e.value){return e.value}if(e.nodes){for(let t of e.nodes){a+=r(t)}}return a};return r(e)};var w=function(e){if(typeof e==="number"){return e-e===0}if(typeof e==="string"&&e.trim()!==""){return Number.isFinite?Number.isFinite(+e):isFinite(+e)}return false};const A=(e,t,r)=>{if(w(e)===false){throw new TypeError("toRegexRange: expected the first argument to be a number")}if(t===void 0||e===t){return String(e)}if(w(t)===false){throw new TypeError("toRegexRange: expected the second argument to be a number.")}let i=Object.assign({relaxZeros:true},r);if(typeof i.strictZeros==="boolean"){i.relaxZeros=i.strictZeros===false}let n=String(i.relaxZeros);let s=String(i.shorthand);let a=String(i.capture);let u=String(i.wrap);let o=e+":"+t+"="+n+s+a+u;if(A.cache.hasOwnProperty(o)){return A.cache[o].result}let l=Math.min(e,t);let f=Math.max(e,t);if(Math.abs(l-f)===1){let r=e+"|"+t;if(i.capture){return`(${r})`}if(i.wrap===false){return r}return`(?:${r})`}let h=hasPadding(e)||hasPadding(t);let c={min:e,max:t,a:l,b:f};let p=[];let d=[];if(h){c.isPadded=h;c.maxLen=String(c.max).length}if(l<0){let e=f<0?Math.abs(f):1;d=splitToPatterns(e,Math.abs(l),c,i);l=c.a=0}if(f>=0){p=splitToPatterns(l,f,c,i)}c.negatives=d;c.positives=p;c.result=collatePatterns(d,p,i);if(i.capture===true){c.result=`(${c.result})`}else if(i.wrap!==false&&p.length+d.length>1){c.result=`(?:${c.result})`}A.cache[o]=c;return c.result};function collatePatterns(e,t,r){let i=filterPatterns(e,t,"-",false,r)||[];let n=filterPatterns(t,e,"",false,r)||[];let s=filterPatterns(e,t,"-?",true,r)||[];let a=i.concat(s).concat(n);return a.join("|")}function splitToRanges(e,t){let r=1;let i=1;let n=countNines(e,r);let s=new Set([t]);while(e<=n&&n<=t){s.add(n);r+=1;n=countNines(e,r)}n=countZeros(t+1,i)-1;while(e<n&&n<=t){s.add(n);i+=1;n=countZeros(t+1,i)-1}s=[...s];s.sort(compare);return s}function rangeToPattern(e,t,r){if(e===t){return{pattern:e,count:[],digits:0}}let i=zip(e,t);let n=i.length;let s="";let a=0;for(let e=0;e<n;e++){let[t,n]=i[e];if(t===n){s+=t}else if(t!=="0"||n!=="9"){s+=toCharacterClass(t,n,r)}else{a++}}if(a){s+=r.shorthand===true?"\\d":"[0-9]"}return{pattern:s,count:[a],digits:n}}function splitToPatterns(e,t,r,i){let n=splitToRanges(e,t);let s=[];let a=e;let u;for(let e=0;e<n.length;e++){let t=n[e];let o=rangeToPattern(String(a),String(t),i);let l="";if(!r.isPadded&&u&&u.pattern===o.pattern){if(u.count.length>1){u.count.pop()}u.count.push(o.count[0]);u.string=u.pattern+toQuantifier(u.count);a=t+1;continue}if(r.isPadded){l=padZeros(t,r,i)}o.string=l+o.pattern+toQuantifier(o.count);s.push(o);a=t+1;u=o}return s}function filterPatterns(e,t,r,i,n){let s=[];for(let n of e){let{string:e}=n;if(!i&&!contains(t,"string",e)){s.push(r+e)}if(i&&contains(t,"string",e)){s.push(r+e)}}return s}function zip(e,t){let r=[];for(let i=0;i<e.length;i++)r.push([e[i],t[i]]);return r}function compare(e,t){return e>t?1:t>e?-1:0}function contains(e,t,r){return e.some(e=>e[t]===r)}function countNines(e,t){return Number(String(e).slice(0,-t)+"9".repeat(t))}function countZeros(e,t){return e-e%Math.pow(10,t)}function toQuantifier(e){let[t=0,r=""]=e;if(r||t>1){return`{${t+(r?","+r:"")}}`}return""}function toCharacterClass(e,t,r){return`[${e}${t-e===1?"":"-"}${t}]`}function hasPadding(e){return/^-?(0+)\d/.test(e)}function padZeros(e,t,r){if(!t.isPadded){return e}let i=Math.abs(t.maxLen-String(e).length);let n=r.relaxZeros!==false;switch(i){case 0:return"";case 1:return n?"0?":"0";case 2:return n?"0{0,2}":"00";default:{return n?`0{0,${i}}`:`0{${i}}`}}}A.cache={};A.clearCache=(()=>A.cache={});var C=A;const x=e=>e!==null&&typeof e==="object"&&!Array.isArray(e);const S=e=>{return t=>e===true?Number(t):String(t)};const k=e=>{return typeof e==="number"||typeof e==="string"&&e!==""};const F=e=>Number.isInteger(+e);const R=e=>{let t=`${e}`;let r=-1;if(t[0]==="-")t=t.slice(1);if(t==="0")return false;while(t[++r]==="0");return r>0};const T=(e,t,r)=>{if(typeof e==="string"||typeof t==="string"){return true}return r.stringify===true};const O=(e,t,r)=>{if(t>0){let r=e[0]==="-"?"-":"";if(r)e=e.slice(1);e=r+e.padStart(r?t-1:t,"0")}if(r===false){return String(e)}return e};const I=(e,t)=>{let r=e[0]==="-"?"-":"";if(r){e=e.slice(1);t--}while(e.length<t)e="0"+e;return r?"-"+e:e};const B=(e,t)=>{e.negatives.sort((e,t)=>e<t?-1:e>t?1:0);e.positives.sort((e,t)=>e<t?-1:e>t?1:0);let r=t.capture?"":"?:";let i="";let n="";let s;if(e.positives.length){i=e.positives.join("|")}if(e.negatives.length){n=`-(${r}${e.negatives.join("|")})`}if(i&&n){s=`${i}|${n}`}else{s=i||n}if(t.wrap){return`(${r}${s})`}return s};const N=(e,t,r,i)=>{if(r){return C(e,t,Object.assign({wrap:false},i))}let n=String.fromCharCode(e);if(e===t)return n;let s=String.fromCharCode(t);return`[${n}-${s}]`};const L=(e,t,r)=>{if(Array.isArray(e)){let t=r.wrap===true;let i=r.capture?"":"?:";return t?`(${i}${e.join("|")})`:e.join("|")}return C(e,t,r)};const P=(...e)=>{return new RangeError("Invalid range arguments: "+a.inspect(...e))};const j=(e,t,r)=>{if(r.strictRanges===true)throw P([e,t]);return[]};const q=(e,t)=>{if(t.strictRanges===true){throw new TypeError(`Expected step "${e}" to be a number`)}return[]};const $=(e,t,r=1,i={})=>{let n=Number(e);let s=Number(t);if(!Number.isInteger(n)||!Number.isInteger(s)){if(i.strictRanges===true)throw P([e,t]);return[]}if(n===0)n=0;if(s===0)s=0;let a=n>s;let u=String(e);let o=String(t);let l=String(r);r=Math.max(Math.abs(r),1);let f=R(u)||R(o)||R(l);let h=f?Math.max(u.length,o.length,l.length):0;let c=f===false&&T(e,t,i)===false;let p=i.transform||S(c);if(i.toRegex&&r===1){return N(I(e,h),I(t,h),true,i)}let d={negatives:[],positives:[]};let v=e=>d[e<0?"negatives":"positives"].push(Math.abs(e));let y=[];let g=0;while(a?n>=s:n<=s){if(i.toRegex===true&&r>1){v(n)}else{y.push(O(p(n,g),h,c))}n=a?n-r:n+r;g++}if(i.toRegex===true){return r>1?B(d,i):L(y,null,Object.assign({wrap:false},i))}return y};const M=(e,t,r=1,i={})=>{if(!F(e)&&e.length>1||!F(t)&&t.length>1){return j(e,t,i)}let n=i.transform||(e=>String.fromCharCode(e));let s=`${e}`.charCodeAt(0);let a=`${t}`.charCodeAt(0);let u=s>a;let o=Math.min(s,a);let l=Math.max(s,a);if(i.toRegex&&r===1){return N(o,l,false,i)}let f=[];let h=0;while(u?s>=a:s<=a){f.push(n(s,h));s=u?s-r:s+r;h++}if(i.toRegex===true){return L(f,null,{wrap:false,options:i})}return f};const U=(e,t,r,i={})=>{if(t==null&&k(e)){return[e]}if(!k(e)||!k(t)){return j(e,t,i)}if(typeof r==="function"){return U(e,t,1,{transform:r})}if(x(r)){return U(e,t,0,r)}let n=Object.assign({},i);if(n.capture===true)n.wrap=true;r=r||n.step||1;if(!F(r)){if(r!=null&&!x(r))return q(r,n);return U(e,t,1,r)}if(F(e)&&F(t)){return $(e,t,r,n)}return M(e,t,Math.max(Math.abs(r),1),n)};var H=U;const W=(e,t={})=>{let r=(e,i={})=>{let n=c.isInvalidBrace(i);let s=e.invalid===true&&t.escapeInvalid===true;let a=n===true||s===true;let u=t.escapeInvalid===true?"\\":"";let o="";if(e.isOpen===true){return u+e.value}if(e.isClose===true){return u+e.value}if(e.type==="open"){return a?u+e.value:"("}if(e.type==="close"){return a?u+e.value:")"}if(e.type==="comma"){return e.prev.type==="comma"?"":a?e.value:"|"}if(e.value){return e.value}if(e.nodes&&e.ranges>0){let r=c.reduce(e.nodes);let i=H(...r,Object.assign({},t,{wrap:false,toRegex:true}));if(i.length!==0){return r.length>1&&i.length>1?`(${i})`:i}}if(e.nodes){for(let t of e.nodes){o+=r(t,e)}}return o};return r(e)};var G=W;const V=(e="",t="",r=false)=>{let i=[];e=[].concat(e);t=[].concat(t);if(!t.length)return e;if(!e.length){return r?c.flatten(t).map(e=>`{${e}}`):t}for(let n of e){if(Array.isArray(n)){for(let e of n){i.push(V(e,t,r))}}else{for(let e of t){if(r===true&&typeof e==="string")e=`{${e}}`;i.push(Array.isArray(e)?V(n,e,r):n+e)}}}return c.flatten(i)};const z=(e,t={})=>{let r=t.rangeLimit===void 0?1e3:t.rangeLimit;let i=(e,n={})=>{e.queue=[];let s=n;let a=n.queue;while(s.type!=="brace"&&s.type!=="root"&&s.parent){s=s.parent;a=s.queue}if(e.invalid||e.dollar){a.push(V(a.pop(),D(e,t)));return}if(e.type==="brace"&&e.invalid!==true&&e.nodes.length===2){a.push(V(a.pop(),["{}"]));return}if(e.nodes&&e.ranges>0){let i=c.reduce(e.nodes);if(c.exceedsLimit(...i,t.step,r)){throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.")}let n=H(...i,t);if(n.length===0){n=D(e,t)}a.push(V(a.pop(),n));e.nodes=[];return}let u=c.encloseBrace(e);let o=e.queue;let l=e;while(l.type!=="brace"&&l.type!=="root"&&l.parent){l=l.parent;o=l.queue}for(let t=0;t<e.nodes.length;t++){let r=e.nodes[t];if(r.type==="comma"&&e.type==="brace"){if(t===1)o.push("");o.push("");continue}if(r.type==="close"){a.push(V(a.pop(),o,u));continue}if(r.value&&r.type!=="open"){o.push(V(o.pop(),r.value));continue}if(r.nodes){i(r,e)}}return o};return c.flatten(i(e))};var K=z;var Q={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:"\n",CHAR_NO_BREAK_SPACE:" ",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"\t",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\ufeff"};const{MAX_LENGTH:X,CHAR_BACKSLASH:Z,CHAR_BACKTICK:J,CHAR_COMMA:Y,CHAR_DOT:ee,CHAR_LEFT_PARENTHESES:te,CHAR_RIGHT_PARENTHESES:re,CHAR_LEFT_CURLY_BRACE:ie,CHAR_RIGHT_CURLY_BRACE:ne,CHAR_LEFT_SQUARE_BRACKET:se,CHAR_RIGHT_SQUARE_BRACKET:ae,CHAR_DOUBLE_QUOTE:ue,CHAR_SINGLE_QUOTE:oe,CHAR_NO_BREAK_SPACE:le,CHAR_ZERO_WIDTH_NOBREAK_SPACE:fe}=Q;const he=(e,t={})=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}let r=t||{};let i=typeof r.maxLength==="number"?Math.min(X,r.maxLength):X;if(e.length>i){throw new SyntaxError(`Input length (${e.length}), exceeds max characters (${i})`)}let n={type:"root",input:e,nodes:[]};let s=[n];let a=n;let u=n;let o=0;let l=e.length;let f=0;let h=0;let c;const p=()=>e[f++];const d=e=>{if(e.type==="text"&&u.type==="dot"){u.type="text"}if(u&&u.type==="text"&&e.type==="text"){u.value+=e.value;return}a.nodes.push(e);e.parent=a;e.prev=u;u=e;return e};d({type:"bos"});while(f<l){a=s[s.length-1];c=p();if(c===fe||c===le){continue}if(c===Z){d({type:"text",value:(t.keepEscaping?c:"")+p()});continue}if(c===ae){d({type:"text",value:"\\"+c});continue}if(c===se){o++;let e;while(f<l&&(e=p())){c+=e;if(e===se){o++;continue}if(e===Z){c+=p();continue}if(e===ae){o--;if(o===0){break}}}d({type:"text",value:c});continue}if(c===te){a=d({type:"paren",nodes:[]});s.push(a);d({type:"text",value:c});continue}if(c===re){if(a.type!=="paren"){d({type:"text",value:c});continue}a=s.pop();d({type:"text",value:c});a=s[s.length-1];continue}if(c===ue||c===oe||c===J){let e=c;let r;if(t.keepQuotes!==true){c=""}while(f<l&&(r=p())){if(r===Z){c+=r+p();continue}if(r===e){if(t.keepQuotes===true)c+=r;break}c+=r}d({type:"text",value:c});continue}if(c===ie){h++;let e=u.value&&u.value.slice(-1)==="$"||a.dollar===true;let t={type:"brace",open:true,close:false,dollar:e,depth:h,commas:0,ranges:0,nodes:[]};a=d(t);s.push(a);d({type:"open",value:c});continue}if(c===ne){if(a.type!=="brace"){d({type:"text",value:c});continue}let e="close";a=s.pop();a.close=true;d({type:e,value:c});h--;a=s[s.length-1];continue}if(c===Y&&h>0){if(a.ranges>0){a.ranges=0;let e=a.nodes.shift();a.nodes=[e,{type:"text",value:D(a)}]}d({type:"comma",value:c});a.commas++;continue}if(c===ee&&h>0&&a.commas===0){let e=a.nodes;if(h===0||e.length===0){d({type:"text",value:c});continue}if(u.type==="dot"){a.range=[];u.value+=c;u.type="range";if(a.nodes.length!==3&&a.nodes.length!==5){a.invalid=true;a.ranges=0;u.type="text";continue}a.ranges++;a.args=[];continue}if(u.type==="range"){e.pop();let t=e[e.length-1];t.value+=u.value+c;u=t;a.ranges--;continue}d({type:"dot",value:c});continue}d({type:"text",value:c})}do{a=s.pop();if(a.type!=="root"){a.nodes.forEach(e=>{if(!e.nodes){if(e.type==="open")e.isOpen=true;if(e.type==="close")e.isClose=true;if(!e.nodes)e.type="text";e.invalid=true}});let e=s[s.length-1];let t=e.nodes.indexOf(a);e.nodes.splice(t,1,...a.nodes)}}while(s.length>0);d({type:"eos"});return n};var ce=he;const pe=(e,t={})=>{let r=[];if(Array.isArray(e)){for(let i of e){let e=pe.create(i,t);if(Array.isArray(e)){r.push(...e)}else{r.push(e)}}}else{r=[].concat(pe.create(e,t))}if(t&&t.expand===true&&t.nodupes===true){r=[...new Set(r)]}return r};pe.parse=((e,t={})=>ce(e,t));pe.stringify=((e,t={})=>{if(typeof e==="string"){return D(pe.parse(e,t),t)}return D(e,t)});pe.compile=((e,t={})=>{if(typeof e==="string"){e=pe.parse(e,t)}return G(e,t)});pe.expand=((e,t={})=>{if(typeof e==="string"){e=pe.parse(e,t)}let r=K(e,t);if(t.noempty===true){r=r.filter(Boolean)}if(t.nodupes===true){r=[...new Set(r)]}return r});pe.create=((e,t={})=>{if(e===""||e.length<3){return[e]}return t.expand!==true?pe.compile(e,t):pe.expand(e,t)});var de=pe;const ve="\\\\/";const ye=`[^${ve}]`;const ge="\\.";const be="\\+";const _e="\\?";const me="\\/";const Ee="(?=.)";const De="[^/]";const we=`(?:${me}|$)`;const Ae=`(?:^|${me})`;const Ce=`${ge}{1,2}${we}`;const xe=`(?!${ge})`;const Se=`(?!${Ae}${Ce})`;const ke=`(?!${ge}{0,1}${we})`;const Fe=`(?!${Ce})`;const Re=`[^.${me}]`;const Te=`${De}*?`;const Oe={DOT_LITERAL:ge,PLUS_LITERAL:be,QMARK_LITERAL:_e,SLASH_LITERAL:me,ONE_CHAR:Ee,QMARK:De,END_ANCHOR:we,DOTS_SLASH:Ce,NO_DOT:xe,NO_DOTS:Se,NO_DOT_SLASH:ke,NO_DOTS_SLASH:Fe,QMARK_NO_DOT:Re,STAR:Te,START_ANCHOR:Ae};const Ie=Object.assign({},Oe,{SLASH_LITERAL:`[${ve}]`,QMARK:ye,STAR:`${ye}*?`,DOTS_SLASH:`${ge}{1,2}(?:[${ve}]|$)`,NO_DOT:`(?!${ge})`,NO_DOTS:`(?!(?:^|[${ve}])${ge}{1,2}(?:[${ve}]|$))`,NO_DOT_SLASH:`(?!${ge}{0,1}(?:[${ve}]|$))`,NO_DOTS_SLASH:`(?!${ge}{1,2}(?:[${ve}]|$))`,QMARK_NO_DOT:`[^.${ve}]`,START_ANCHOR:`(?:^|[${ve}])`,END_ANCHOR:`(?:[${ve}]|$)`});const Be={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Ne={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:Be,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHAR:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:n.sep,extglobChars(e){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${e.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(e){return e===true?Ie:Oe}};var Le=createCommonjsModule(function(e,t){const r=process.platform==="win32";const{REGEX_SPECIAL_CHARS:i,REGEX_SPECIAL_CHARS_GLOBAL:s,REGEX_REMOVE_BACKSLASH:a}=Ne;t.isObject=(e=>e!==null&&typeof e==="object"&&!Array.isArray(e));t.hasRegexChars=(e=>i.test(e));t.isRegexChar=(e=>e.length===1&&t.hasRegexChars(e));t.escapeRegex=(e=>e.replace(s,"\\$1"));t.toPosixSlashes=(e=>e.replace(/\\/g,"/"));t.removeBackslashes=(e=>{return e.replace(a,e=>{return e==="\\"?"":e})});t.supportsLookbehinds=(()=>{let e=process.version.slice(1).split(".");if(e.length===3&&+e[0]>=9||+e[0]===8&&+e[1]>=10){return true}return false});t.isWindows=(e=>{if(e&&typeof e.windows==="boolean"){return e.windows}return r===true||n.sep==="\\"});t.escapeLast=((e,r,i)=>{let n=e.lastIndexOf(r,i);if(n===-1)return e;if(e[n-1]==="\\")return t.escapeLast(e,r,n-1);return e.slice(0,n)+"\\"+e.slice(n)})});var Pe=Le.isObject;var je=Le.hasRegexChars;var qe=Le.isRegexChar;var $e=Le.escapeRegex;var Me=Le.toPosixSlashes;var Ue=Le.removeBackslashes;var He=Le.supportsLookbehinds;var We=Le.isWindows;var Ge=Le.escapeLast;const{CHAR_ASTERISK:Ve,CHAR_AT:ze,CHAR_BACKWARD_SLASH:Ke,CHAR_COMMA:Qe,CHAR_DOT:Xe,CHAR_EXCLAMATION_MARK:Ze,CHAR_FORWARD_SLASH:Je,CHAR_LEFT_CURLY_BRACE:Ye,CHAR_LEFT_PARENTHESES:et,CHAR_LEFT_SQUARE_BRACKET:tt,CHAR_PLUS:rt,CHAR_QUESTION_MARK:it,CHAR_RIGHT_CURLY_BRACE:nt,CHAR_RIGHT_PARENTHESES:st,CHAR_RIGHT_SQUARE_BRACKET:at}=Ne;const ut=e=>{return e===Je||e===Ke};var ot=(e,t)=>{let r=t||{};let i=e.length-1;let n=-1;let s=0;let a=0;let u=false;let o=false;let l=false;let f=0;let h;let c;let p=false;let d=()=>n>=i;let v=()=>{h=c;return e.charCodeAt(++n)};while(n<i){c=v();let t;if(c===Ke){o=true;t=v();if(t===Ye){p=true}continue}if(p===true||c===Ye){f++;while(!d()&&(t=v())){if(t===Ke){o=true;t=v();continue}if(t===Ye){f++;continue}if(!p&&t===Xe&&(t=v())===Xe){u=true;break}if(!p&&t===Qe){u=true;break}if(t===nt){f--;if(f===0){p=false;break}}}}if(c===Je){if(h===Xe&&n===s+1){s+=2;continue}a=n+1;continue}if(c===Ve){u=true;break}if(c===Ve||c===it){u=true;break}if(c===tt){while(!d()&&(t=v())){if(t===Ke){o=true;t=v();continue}if(t===at){u=true;break}}}let r=c===rt||c===ze||c===Ze;if(r&&e.charCodeAt(n+1)===et){u=true;break}if(c===Ze&&n===s){l=true;s++;continue}if(c===et){while(!d()&&(t=v())){if(t===Ke){o=true;t=v();continue}if(t===st){u=true;break}}}if(u){break}}let y="";let g=e;let b=e;let _="";if(s>0){y=e.slice(0,s);e=e.slice(s);a-=s}if(b&&u===true&&a>0){b=e.slice(0,a);_=e.slice(a)}else if(u===true){b="";_=e}else{b=e}if(b&&b!==""&&b!=="/"&&b!==e){if(ut(b.charCodeAt(b.length-1))){b=b.slice(0,-1)}}if(r.unescape===true){if(_)_=Le.removeBackslashes(_);if(b&&o===true){b=Le.removeBackslashes(b)}}return{prefix:y,input:g,base:b,glob:_,negated:l,isGlob:u}};const{MAX_LENGTH:lt,POSIX_REGEX_SOURCE:ft,REGEX_NON_SPECIAL_CHAR:ht,REGEX_SPECIAL_CHARS_BACKREF:ct,REPLACEMENTS:pt}=Ne;const dt=(e,t)=>{if(typeof t.expandRange==="function"){return t.expandRange(...e,t)}e.sort();let r=`[${e.join("-")}]`;try{}catch(t){return e.map(e=>Le.escapeRegex(e)).join("..")}return r};const vt=e=>{let t=1;while(e.peek()==="!"&&(e.peek(2)!=="("||e.peek(3)==="?")){e.advance();e.start++;t++}if(t%2===0){return false}e.negated=true;e.start++;return true};const yt=(e,t)=>{return`Missing ${e}: "${t}" - use "\\\\${t}" to match literal characters`};const gt=(e,t)=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}e=pt[e]||e;let r=Object.assign({},t);let i=typeof r.maxLength==="number"?Math.min(lt,r.maxLength):lt;let n=e.length;if(n>i){throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${i}`)}let s={type:"bos",value:"",output:r.prepend||""};let a=[s];let u=r.capture?"":"?:";let o=Le.isWindows(t);const l=Ne.globChars(o);const f=Ne.extglobChars(l);const{DOT_LITERAL:h,PLUS_LITERAL:c,SLASH_LITERAL:p,ONE_CHAR:d,DOTS_SLASH:v,NO_DOT:y,NO_DOT_SLASH:g,NO_DOTS_SLASH:b,QMARK:_,QMARK_NO_DOT:m,STAR:E,START_ANCHOR:D}=l;const w=e=>{return`(${u}(?:(?!${D}${e.dot?v:h}).)*?)`};let A=r.dot?"":y;let C=r.bash===true?w(r):E;let x=r.dot?_:m;if(r.capture){C=`(${C})`}if(typeof r.noext==="boolean"){r.noextglob=r.noext}let S={index:-1,start:0,consumed:"",output:"",backtrack:false,brackets:0,braces:0,parens:0,quotes:0,tokens:a};let k=[];let F=[];let R=s;let T;const O=()=>S.index===n-1;const I=S.peek=((t=1)=>e[S.index+t]);const B=S.advance=(()=>e[++S.index]);const N=e=>{S.output+=e.output!=null?e.output:e.value;S.consumed+=e.value||""};const L=e=>{S[e]++;F.push(e)};const P=e=>{S[e]--;F.pop()};const j=e=>{if(R.type==="globstar"){let t=S.braces>0&&(e.type==="comma"||e.type==="brace");let r=k.length&&(e.type==="pipe"||e.type==="paren");if(e.type!=="slash"&&e.type!=="paren"&&!t&&!r){S.output=S.output.slice(0,-R.output.length);R.type="star";R.value="*";R.output=C;S.output+=R.output}}if(k.length&&e.type!=="paren"&&!f[e.value]){k[k.length-1].inner+=e.value}if(e.value||e.output)N(e);if(R&&R.type==="text"&&e.type==="text"){R.value+=e.value;return}e.prev=R;a.push(e);R=e};const q=(e,t)=>{let i=Object.assign({},f[t],{conditions:1,inner:""});i.prev=R;i.parens=S.parens;i.output=S.output;let n=(r.capture?"(":"")+i.open;j({type:e,value:t,output:S.output?"":d});j({type:"paren",extglob:true,value:B(),output:n});L("parens");k.push(i)};const $=t=>{let i=t.close+(r.capture?")":"");if(t.type==="negate"){let n=C;if(t.inner&&t.inner.length>1&&t.inner.includes("/")){n=w(r)}if(n!==C||O()||/^\)+$/.test(e.slice(S.index+1))){i=t.close=")$))"+n}if(t.prev.type==="bos"&&O()){S.negatedExtglob=true}}j({type:"paren",extglob:true,value:T,output:i});P("parens")};if(r.fastpaths!==false&&!/(^[*!]|[/{[()\]}"])/.test(e)){let t=false;let i=e.replace(ct,(e,r,i,n,s,a)=>{if(n==="\\"){t=true;return e}if(n==="?"){if(r){return r+n+(s?_.repeat(s.length):"")}if(a===0){return x+(s?_.repeat(s.length):"")}return _.repeat(i.length)}if(n==="."){return h.repeat(i.length)}if(n==="*"){if(r){return r+n+(s?C:"")}return C}return r?e:"\\"+e});if(t===true){if(r.unescape===true){i=i.replace(/\\/g,"")}else{i=i.replace(/\\+/g,e=>{return e.length%2===0?"\\\\":e?"\\":""})}}S.output=i;return S}while(!O()){T=B();if(T==="\0"){continue}if(T==="\\"){let t=I();if(t==="/"&&r.bash!==true){continue}if(t==="."||t===";"){continue}if(!t){T+="\\";j({type:"text",value:T});continue}let i=/^\\+/.exec(e.slice(S.index+1));let n=0;if(i&&i[0].length>2){n=i[0].length;S.index+=n;if(n%2!==0){T+="\\"}}if(r.unescape===true){T=B()||""}else{T+=B()||""}if(S.brackets===0){j({type:"text",value:T});continue}}if(S.brackets>0&&(T!=="]"||R.value==="["||R.value==="[^")){if(r.posix!==false&&T===":"){let e=R.value.slice(1);if(e.includes("[")){R.posix=true;if(e.includes(":")){let e=R.value.lastIndexOf("[");let t=R.value.slice(0,e);let r=R.value.slice(e+2);let i=ft[r];if(i){R.value=t+i;S.backtrack=true;B();if(!s.output&&a.indexOf(R)===1){s.output=d}continue}}}}if(T==="["&&I()!==":"||T==="-"&&I()==="]"){T="\\"+T}if(T==="]"&&(R.value==="["||R.value==="[^")){T="\\"+T}if(r.posix===true&&T==="!"&&R.value==="["){T="^"}R.value+=T;N({value:T});continue}if(S.quotes===1&&T!=='"'){T=Le.escapeRegex(T);R.value+=T;N({value:T});continue}if(T==='"'){S.quotes=S.quotes===1?0:1;if(r.keepQuotes===true){j({type:"text",value:T})}continue}if(T==="("){j({type:"paren",value:T});L("parens");continue}if(T===")"){if(S.parens===0&&r.strictBrackets===true){throw new SyntaxError(yt("opening","("))}let e=k[k.length-1];if(e&&S.parens===e.parens+1){$(k.pop());continue}j({type:"paren",value:T,output:S.parens?")":"\\)"});P("parens");continue}if(T==="["){if(r.nobracket===true||!e.slice(S.index+1).includes("]")){if(r.nobracket!==true&&r.strictBrackets===true){throw new SyntaxError(yt("closing","]"))}T="\\"+T}else{L("brackets")}j({type:"bracket",value:T});continue}if(T==="]"){if(r.nobracket===true||R&&R.type==="bracket"&&R.value.length===1){j({type:"text",value:T,output:"\\"+T});continue}if(S.brackets===0){if(r.strictBrackets===true){throw new SyntaxError(yt("opening","["))}j({type:"text",value:T,output:"\\"+T});continue}P("brackets");let e=R.value.slice(1);if(R.posix!==true&&e[0]==="^"&&!e.includes("/")){T="/"+T}R.value+=T;N({value:T});if(r.literalBrackets===false||Le.hasRegexChars(e)){continue}let t=Le.escapeRegex(R.value);S.output=S.output.slice(0,-R.value.length);if(r.literalBrackets===true){S.output+=t;R.value=t;continue}R.value=`(${u}${t}|${R.value})`;S.output+=R.value;continue}if(T==="{"&&r.nobrace!==true){j({type:"brace",value:T,output:"("});L("braces");continue}if(T==="}"){if(r.nobrace===true||S.braces===0){j({type:"text",value:T,output:"\\"+T});continue}let e=")";if(S.dots===true){let t=a.slice();let i=[];for(let e=t.length-1;e>=0;e--){a.pop();if(t[e].type==="brace"){break}if(t[e].type!=="dots"){i.unshift(t[e].value)}}e=dt(i,r);S.backtrack=true}j({type:"brace",value:T,output:e});P("braces");continue}if(T==="|"){if(k.length>0){k[k.length-1].conditions++}j({type:"text",value:T});continue}if(T===","){let e=T;if(S.braces>0&&F[F.length-1]==="braces"){e="|"}j({type:"comma",value:T,output:e});continue}if(T==="/"){if(R.type==="dot"&&S.index===1){S.start=S.index+1;S.consumed="";S.output="";a.pop();R=s;continue}j({type:"slash",value:T,output:p});continue}if(T==="."){if(S.braces>0&&R.type==="dot"){if(R.value===".")R.output=h;R.type="dots";R.output+=T;R.value+=T;S.dots=true;continue}j({type:"dot",value:T,output:h});continue}if(T==="?"){if(R&&R.type==="paren"){let e=I();let t=T;if(e==="<"&&!Le.supportsLookbehinds()){throw new Error("Node.js v10 or higher is required for regex lookbehinds")}if(R.value==="("&&!/[!=<:]/.test(e)||e==="<"&&!/[!=]/.test(I(2))){t="\\"+T}j({type:"text",value:T,output:t});continue}if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){q("qmark",T);continue}if(r.dot!==true&&(R.type==="slash"||R.type==="bos")){j({type:"qmark",value:T,output:m});continue}j({type:"qmark",value:T,output:_});continue}if(T==="!"){if(r.noextglob!==true&&I()==="("){if(I(2)!=="?"||!/[!=<:]/.test(I(3))){q("negate",T);continue}}if(r.nonegate!==true&&S.index===0){vt(S);continue}}if(T==="+"){if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){q("plus",T);continue}if(R&&(R.type==="bracket"||R.type==="paren"||R.type==="brace")){let e=R.extglob===true?"\\"+T:T;j({type:"plus",value:T,output:e});continue}if(S.parens>0&&r.regex!==false){j({type:"plus",value:T});continue}j({type:"plus",value:c});continue}if(T==="@"){if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){j({type:"at",value:T,output:""});continue}j({type:"text",value:T});continue}if(T!=="*"){if(T==="$"||T==="^"){T="\\"+T}let t=ht.exec(e.slice(S.index+1));if(t){T+=t[0];S.index+=t[0].length}j({type:"text",value:T});continue}if(R&&(R.type==="globstar"||R.star===true)){R.type="star";R.star=true;R.value+=T;R.output=C;S.backtrack=true;S.consumed+=T;continue}if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){q("star",T);continue}if(R.type==="star"){if(r.noglobstar===true){S.consumed+=T;continue}let t=R.prev;let i=t.prev;let n=t.type==="slash"||t.type==="bos";let s=i&&(i.type==="star"||i.type==="globstar");if(r.bash===true&&(!n||!O()&&I()!=="/")){j({type:"star",value:T,output:""});continue}let a=S.braces>0&&(t.type==="comma"||t.type==="brace");let u=k.length&&(t.type==="pipe"||t.type==="paren");if(!n&&t.type!=="paren"&&!a&&!u){j({type:"star",value:T,output:""});continue}while(e.slice(S.index+1,S.index+4)==="/**"){let t=e[S.index+4];if(t&&t!=="/"){break}S.consumed+="/**";S.index+=3}if(t.type==="bos"&&O()){R.type="globstar";R.value+=T;R.output=w(r);S.output=R.output;S.consumed+=T;continue}if(t.type==="slash"&&t.prev.type!=="bos"&&!s&&O()){S.output=S.output.slice(0,-(t.output+R.output).length);t.output="(?:"+t.output;R.type="globstar";R.output=w(r)+"|$)";R.value+=T;S.output+=t.output+R.output;S.consumed+=T;continue}let o=I();if(t.type==="slash"&&t.prev.type!=="bos"&&o==="/"){let e=I(2)!==void 0?"|$":"";S.output=S.output.slice(0,-(t.output+R.output).length);t.output="(?:"+t.output;R.type="globstar";R.output=`${w(r)}${p}|${p}${e})`;R.value+=T;S.output+=t.output+R.output;S.consumed+=T+B();j({type:"slash",value:T,output:""});continue}if(t.type==="bos"&&o==="/"){R.type="globstar";R.value+=T;R.output=`(?:^|${p}|${w(r)}${p})`;S.output=R.output;S.consumed+=T+B();j({type:"slash",value:T,output:""});continue}S.output=S.output.slice(0,-R.output.length);R.type="globstar";R.output=w(r);R.value+=T;S.output+=R.output;S.consumed+=T;continue}let t={type:"star",value:T,output:C};if(r.bash===true){t.output=".*?";if(R.type==="bos"||R.type==="slash"){t.output=A+t.output}j(t);continue}if(R&&(R.type==="bracket"||R.type==="paren")&&r.regex===true){t.output=T;j(t);continue}if(S.index===S.start||R.type==="slash"||R.type==="dot"){if(R.type==="dot"){S.output+=g;R.output+=g}else if(r.dot===true){S.output+=b;R.output+=b}else{S.output+=A;R.output+=A}if(I()!=="*"){S.output+=d;R.output+=d}}j(t)}while(S.brackets>0){if(r.strictBrackets===true)throw new SyntaxError(yt("closing","]"));S.output=Le.escapeLast(S.output,"[");P("brackets")}while(S.parens>0){if(r.strictBrackets===true)throw new SyntaxError(yt("closing",")"));S.output=Le.escapeLast(S.output,"(");P("parens")}while(S.braces>0){if(r.strictBrackets===true)throw new SyntaxError(yt("closing","}"));S.output=Le.escapeLast(S.output,"{");P("braces")}if(r.strictSlashes!==true&&(R.type==="star"||R.type==="bracket")){j({type:"maybe_slash",value:"",output:`${p}?`})}if(S.backtrack===true){S.output="";for(let e of S.tokens){S.output+=e.output!=null?e.output:e.value;if(e.suffix){S.output+=e.suffix}}}return S};gt.fastpaths=((e,t)=>{let r=Object.assign({},t);let i=typeof r.maxLength==="number"?Math.min(lt,r.maxLength):lt;let n=e.length;if(n>i){throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${i}`)}e=pt[e]||e;let s=Le.isWindows(t);const{DOT_LITERAL:a,SLASH_LITERAL:u,ONE_CHAR:o,DOTS_SLASH:l,NO_DOT:f,NO_DOTS:h,NO_DOTS_SLASH:c,STAR:p,START_ANCHOR:d}=Ne.globChars(s);let v=r.capture?"":"?:";let y=r.bash===true?".*?":p;let g=r.dot?h:f;let b=r.dot?c:f;if(r.capture){y=`(${y})`}const _=e=>{return`(${v}(?:(?!${d}${e.dot?l:a}).)*?)`};const m=e=>{switch(e){case"*":return`${g}${o}${y}`;case".*":return`${a}${o}${y}`;case"*.*":return`${g}${y}${a}${o}${y}`;case"*/*":return`${g}${y}${u}${o}${b}${y}`;case"**":return g+_(r);case"**/*":return`(?:${g}${_(r)}${u})?${b}${o}${y}`;case"**/*.*":return`(?:${g}${_(r)}${u})?${b}${y}${a}${o}${y}`;case"**/.*":return`(?:${g}${_(r)}${u})?${a}${o}${y}`;default:{let r=/^(.*?)\.(\w+)$/.exec(e);if(!r)return;let i=m(r[1],t);if(!i)return;return i+a+r[2]}}};let E=m(e);if(E&&r.strictSlashes!==true){E+=`${u}?`}return E});var bt=gt;const _t=(e,t,r=false)=>{if(Array.isArray(e)){let i=e.map(e=>_t(e,t,r));return e=>{for(let t of i){let r=t(e);if(r)return r}return false}}if(typeof e!=="string"||e===""){throw new TypeError("Expected pattern to be a non-empty string")}let i=t||{};let n=Le.isWindows(t);let s=_t.makeRe(e,t,false,true);let a=s.state;delete s.state;let u=()=>false;if(i.ignore){let e=Object.assign({},t,{ignore:null,onMatch:null,onResult:null});u=_t(i.ignore,e,r)}const o=(r,o=false)=>{let{isMatch:l,match:f,output:h}=_t.test(r,s,t,{glob:e,posix:n});let c={glob:e,state:a,regex:s,posix:n,input:r,output:h,match:f,isMatch:l};if(typeof i.onResult==="function"){i.onResult(c)}if(l===false){c.isMatch=false;return o?c:false}if(u(r)){if(typeof i.onIgnore==="function"){i.onIgnore(c)}c.isMatch=false;return o?c:false}if(typeof i.onMatch==="function"){i.onMatch(c)}return o?c:true};if(r){o.state=a}return o};_t.test=((e,t,r,{glob:i,posix:n}={})=>{if(typeof e!=="string"){throw new TypeError("Expected input to be a string")}if(e===""){return{isMatch:false,output:""}}let s=r||{};let a=s.format||(n?Le.toPosixSlashes:null);let u=e===i;let o=u&&a?a(e):e;if(u===false){o=a?a(e):e;u=o===i}if(u===false||s.capture===true){if(s.matchBase===true||s.basename===true){u=_t.matchBase(e,t,r,n)}else{u=t.exec(o)}}return{isMatch:!!u,match:u,output:o}});_t.matchBase=((e,t,r,i=Le.isWindows(r))=>{let s=t instanceof RegExp?t:_t.makeRe(t,r);return s.test(n.basename(e))});_t.isMatch=((e,t,r)=>_t(t,r)(e));_t.parse=((e,t)=>bt(e,t));_t.scan=((e,t)=>ot(e,t));_t.makeRe=((e,t,r=false,i=false)=>{if(!e||typeof e!=="string"){throw new TypeError("Expected a non-empty string")}let n=t||{};let s=n.contains?"":"^";let a=n.contains?"":"$";let u={negated:false,fastpaths:true};let o="";let l;if(e.startsWith("./")){e=e.slice(2);o=u.prefix="./"}if(n.fastpaths!==false&&(e[0]==="."||e[0]==="*")){l=bt.fastpaths(e,t)}if(l===void 0){u=_t.parse(e,t);u.prefix=o+(u.prefix||"");l=u.output}if(r===true){return l}let f=`${s}(?:${l})${a}`;if(u&&u.negated===true){f=`^(?!${f}).*$`}let h=_t.toRegex(f,t);if(i===true){h.state=u}return h});_t.toRegex=((e,t)=>{try{let r=t||{};return new RegExp(e,r.flags||(r.nocase?"i":""))}catch(e){if(t&&t.debug===true)throw e;return/$^/}});_t.constants=Ne;var mt=_t;var Et=mt;const Dt=e=>typeof e==="string"&&(e===""||e==="./");const wt=(e,t,r)=>{t=[].concat(t);e=[].concat(e);let i=new Set;let n=new Set;let s=new Set;let a=0;let u=e=>{s.add(e.output);if(r&&r.onResult){r.onResult(e)}};for(let s=0;s<t.length;s++){let o=Et(String(t[s]),Object.assign({},r,{onResult:u}),true);let l=o.state.negated||o.state.negatedExtglob;if(l)a++;for(let t of e){let e=o(t,true);let r=l?!e.isMatch:e.isMatch;if(!r)continue;if(l){i.add(e.output)}else{i.delete(e.output);n.add(e.output)}}}let o=a===t.length?[...s]:[...n];let l=o.filter(e=>!i.has(e));if(r&&l.length===0){if(r.failglob===true){throw new Error(`No matches found for "${t.join(", ")}"`)}if(r.nonull===true||r.nullglob===true){return r.unescape?t.map(e=>e.replace(/\\/g,"")):t}}return l};wt.match=wt;wt.matcher=((e,t)=>Et(e,t));wt.isMatch=((e,t,r)=>Et(t,r)(e));wt.any=wt.isMatch;wt.not=((e,t,r={})=>{t=[].concat(t).map(String);let i=new Set;let n=[];let s=e=>{if(r.onResult)r.onResult(e);n.push(e.output)};let a=wt(e,t,Object.assign({},r,{onResult:s}));for(let e of n){if(!a.includes(e)){i.add(e)}}return[...i]});wt.contains=((e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${a.inspect(e)}"`)}if(Array.isArray(t)){return t.some(t=>wt.contains(e,t,r))}if(typeof t==="string"){if(Dt(e)||Dt(t)){return false}if(e.includes(t)||e.startsWith("./")&&e.slice(2).includes(t)){return true}}return wt.isMatch(e,t,Object.assign({},r,{contains:true}))});wt.matchKeys=((e,t,r)=>{if(!Le.isObject(e)){throw new TypeError("Expected the first argument to be an object")}let i=wt(Object.keys(e),t,r);let n={};for(let t of i)n[t]=e[t];return n});wt.some=((e,t,r)=>{let i=[].concat(e);for(let e of[].concat(t)){let t=Et(String(e),r);if(i.some(e=>t(e))){return true}}return false});wt.every=((e,t,r)=>{let i=[].concat(e);for(let e of[].concat(t)){let t=Et(String(e),r);if(!i.every(e=>t(e))){return false}}return true});wt.all=((e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${a.inspect(e)}"`)}return[].concat(t).every(t=>Et(t,r)(e))});wt.capture=((e,t,r)=>{let i=Le.isWindows(r);let n=Et.makeRe(String(e),Object.assign({},r,{capture:true}));let s=n.exec(i?Le.toPosixSlashes(t):t);if(s){return s.slice(1).map(e=>e===void 0?"":e)}});wt.makeRe=((...e)=>Et.makeRe(...e));wt.scan=((...e)=>Et.scan(...e));wt.parse=((e,t)=>{let r=[];for(let i of[].concat(e||[])){for(let e of de(String(i),t)){r.push(Et.parse(e,t))}}return r});wt.braces=((e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");if(t&&t.nobrace===true||!/\{.*\}/.test(e)){return[e]}return de(e,t)});wt.braceExpand=((e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");return wt.braces(e,Object.assign({},t,{expand:true}))});var At=wt;function ensureArray(e){if(Array.isArray(e))return e;if(e==undefined)return[];return[e]}function getMatcherString(e,t){if(t===false){return e}return i.resolve(...typeof t==="string"?[t,e]:[e])}const Ct=function createFilter(e,t,r){const n=r&&r.resolve;const s=e=>{return e instanceof RegExp?e:{test:At.matcher(getMatcherString(e,n).split(i.sep).join("/"),{dot:true})}};const a=ensureArray(e).map(s);const u=ensureArray(t).map(s);return function(e){if(typeof e!=="string")return false;if(/\0/.test(e))return false;e=e.split(i.sep).join("/");for(let t=0;t<u.length;++t){const r=u[t];if(r.test(e))return false}for(let t=0;t<a.length;++t){const r=a[t];if(r.test(e))return true}return!a.length}};const xt="break case class catch const continue debugger default delete do else export extends finally for function if import in instanceof let new return super switch this throw try typeof var void while with yield enum await implements package protected static interface private public";const St="arguments Infinity NaN undefined null true false eval uneval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Symbol Error EvalError InternalError RangeError ReferenceError SyntaxError TypeError URIError Number Math Date String RegExp Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array Map Set WeakMap WeakSet SIMD ArrayBuffer DataView JSON Promise Generator GeneratorFunction Reflect Proxy Intl";const kt=new Set(`${xt} ${St}`.split(" "));kt.add("");const Ft=function makeLegalIdentifier(e){e=e.replace(/-(\w)/g,(e,t)=>t.toUpperCase()).replace(/[^$_a-zA-Z0-9]/g,"_");if(/\d/.test(e[0])||kt.has(e)){e=`_${e}`}return e||"_"};function stringify$2(e){return(JSON.stringify(e)||"undefined").replace(/[\u2028\u2029]/g,e=>`\\u${("000"+e.charCodeAt(0).toString(16)).slice(-4)}`)}function serializeArray(e,t,r){let i="[";const n=t?"\n"+r+t:"";for(let s=0;s<e.length;s++){const a=e[s];i+=`${s>0?",":""}${n}${serialize(a,t,r+t)}`}return i+`${t?"\n"+r:""}]`}function serializeObject(e,t,r){let i="{";const n=t?"\n"+r+t:"";const s=Object.keys(e);for(let a=0;a<s.length;a++){const u=s[a];const o=Ft(u)===u?u:stringify$2(u);i+=`${a>0?",":""}${n}${o}:${t?" ":""}${serialize(e[u],t,r+t)}`}return i+`${t?"\n"+r:""}}`}function serialize(e,t,r){if(e===Infinity)return"Infinity";if(e===-Infinity)return"-Infinity";if(e===0&&1/e===-Infinity)return"-0";if(e instanceof Date)return"new Date("+e.getTime()+")";if(e instanceof RegExp)return e.toString();if(e!==e)return"NaN";if(Array.isArray(e))return serializeArray(e,t,r);if(e===null)return"null";if(typeof e==="object")return serializeObject(e,t,r);return stringify$2(e)}const Rt=function dataToEsm(e,t={}){const r=t.compact?"":"indent"in t?t.indent:"\t";const i=t.compact?"":" ";const n=t.compact?"":"\n";const s=t.preferConst?"const":"var";if(t.namedExports===false||typeof e!=="object"||Array.isArray(e)||e instanceof Date||e instanceof RegExp||e===null){const n=serialize(e,t.compact?null:r,"");const s=i||(/^[{[\-\/]/.test(n)?"":" ");return`export default${s}${n};`}let a="";const u=[];const o=Object.keys(e);for(let l=0;l<o.length;l++){const f=o[l];if(f===Ft(f)){if(t.objectShorthand)u.push(f);else u.push(`${f}:${i}${f}`);a+=`export ${s} ${f}${i}=${i}${serialize(e[f],t.compact?null:r,"")};${n}`}else{u.push(`${stringify$2(f)}:${i}${serialize(e[f],t.compact?null:r,"")}`)}}return a+`export default${i}{${n}${r}${u.join(`,${n}${r}`)}${n}};${n}`};t.addExtension=u;t.attachScopes=h;t.createFilter=Ct;t.dataToEsm=Rt;t.extractAssignedNames=l;t.makeLegalIdentifier=Ft},314:function(e){"use strict";e.exports=setInterval},320:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.isSpaceSeparator=isSpaceSeparator;t.isIdStartChar=isIdStartChar;t.isIdContinueChar=isIdContinueChar;t.isDigit=isDigit;t.isHexDigit=isHexDigit;var i=r(858);var n=_interopRequireWildcard(i);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function isSpaceSeparator(e){return n.Space_Separator.test(e)}function isIdStartChar(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||n.ID_Start.test(e)}function isIdContinueChar(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="‌"||e==="‍"||n.ID_Continue.test(e)}function isDigit(e){return/[0-9]/.test(e)}function isHexDigit(e){return/[0-9A-Fa-f]/.test(e)}},327:function(e,t,r){e.exports=glob;var i=r(66);var n=r(129);var s=r(620);var a=s.Minimatch;var u=r(207);var o=r(485).EventEmitter;var l=r(589);var f=r(393);var h=r(969);var c=r(487);var p=r(922);var d=p.alphasort;var v=p.alphasorti;var y=p.setopts;var g=p.ownProp;var b=r(408);var _=r(64);var m=p.childrenIgnored;var E=p.isIgnored;var D=r(83);function glob(e,t,r){if(typeof t==="function")r=t,t={};if(!t)t={};if(t.sync){if(r)throw new TypeError("callback provided to sync glob");return c(e,t)}return new Glob(e,t,r)}glob.sync=c;var w=glob.GlobSync=c.GlobSync;glob.glob=glob;function extend(e,t){if(t===null||typeof t!=="object"){return e}var r=Object.keys(t);var i=r.length;while(i--){e[r[i]]=t[r[i]]}return e}glob.hasMagic=function(e,t){var r=extend({},t);r.noprocess=true;var i=new Glob(e,r);var n=i.minimatch.set;if(!e)return false;if(n.length>1)return true;for(var s=0;s<n[0].length;s++){if(typeof n[0][s]!=="string")return true}return false};glob.Glob=Glob;u(Glob,o);function Glob(e,t,r){if(typeof t==="function"){r=t;t=null}if(t&&t.sync){if(r)throw new TypeError("callback provided to sync glob");return new w(e,t)}if(!(this instanceof Glob))return new Glob(e,t,r);y(this,e,t);this._didRealPath=false;var i=this.minimatch.set.length;this.matches=new Array(i);if(typeof r==="function"){r=D(r);this.on("error",r);this.on("end",function(e){r(null,e)})}var n=this;this._processing=0;this._emitQueue=[];this._processQueue=[];this.paused=false;if(this.noprocess)return this;if(i===0)return done();var s=true;for(var a=0;a<i;a++){this._process(this.minimatch.set[a],a,false,done)}s=false;function done(){--n._processing;if(n._processing<=0){if(s){process.nextTick(function(){n._finish()})}else{n._finish()}}}}Glob.prototype._finish=function(){f(this instanceof Glob);if(this.aborted)return;if(this.realpath&&!this._didRealpath)return this._realpath();p.finish(this);this.emit("end",this.found)};Glob.prototype._realpath=function(){if(this._didRealpath)return;this._didRealpath=true;var e=this.matches.length;if(e===0)return this._finish();var t=this;for(var r=0;r<this.matches.length;r++)this._realpathSet(r,next);function next(){if(--e===0)t._finish()}};Glob.prototype._realpathSet=function(e,t){var r=this.matches[e];if(!r)return t();var i=Object.keys(r);var s=this;var a=i.length;if(a===0)return t();var u=this.matches[e]=Object.create(null);i.forEach(function(r,i){r=s._makeAbs(r);n.realpath(r,s.realpathCache,function(i,n){if(!i)u[n]=true;else if(i.syscall==="stat")u[r]=true;else s.emit("error",i);if(--a===0){s.matches[e]=u;t()}})})};Glob.prototype._mark=function(e){return p.mark(this,e)};Glob.prototype._makeAbs=function(e){return p.makeAbs(this,e)};Glob.prototype.abort=function(){this.aborted=true;this.emit("abort")};Glob.prototype.pause=function(){if(!this.paused){this.paused=true;this.emit("pause")}};Glob.prototype.resume=function(){if(this.paused){this.emit("resume");this.paused=false;if(this._emitQueue.length){var e=this._emitQueue.slice(0);this._emitQueue.length=0;for(var t=0;t<e.length;t++){var r=e[t];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var i=this._processQueue.slice(0);this._processQueue.length=0;for(var t=0;t<i.length;t++){var n=i[t];this._processing--;this._process(n[0],n[1],n[2],n[3])}}}};Glob.prototype._process=function(e,t,r,i){f(this instanceof Glob);f(typeof i==="function");if(this.aborted)return;this._processing++;if(this.paused){this._processQueue.push([e,t,r,i]);return}var n=0;while(typeof e[n]==="string"){n++}var a;switch(n){case e.length:this._processSimple(e.join("/"),t,i);return;case 0:a=null;break;default:a=e.slice(0,n).join("/");break}var u=e.slice(n);var o;if(a===null)o=".";else if(h(a)||h(e.join("/"))){if(!a||!h(a))a="/"+a;o=a}else o=a;var l=this._makeAbs(o);if(m(this,o))return i();var c=u[0]===s.GLOBSTAR;if(c)this._processGlobStar(a,o,l,u,t,r,i);else this._processReaddir(a,o,l,u,t,r,i)};Glob.prototype._processReaddir=function(e,t,r,i,n,s,a){var u=this;this._readdir(r,s,function(o,l){return u._processReaddir2(e,t,r,i,n,s,l,a)})};Glob.prototype._processReaddir2=function(e,t,r,i,n,s,a,u){if(!a)return u();var o=i[0];var f=!!this.minimatch.negate;var h=o._glob;var c=this.dot||h.charAt(0)===".";var p=[];for(var d=0;d<a.length;d++){var v=a[d];if(v.charAt(0)!=="."||c){var y;if(f&&!e){y=!v.match(o)}else{y=v.match(o)}if(y)p.push(v)}}var g=p.length;if(g===0)return u();if(i.length===1&&!this.mark&&!this.stat){if(!this.matches[n])this.matches[n]=Object.create(null);for(var d=0;d<g;d++){var v=p[d];if(e){if(e!=="/")v=e+"/"+v;else v=e+v}if(v.charAt(0)==="/"&&!this.nomount){v=l.join(this.root,v)}this._emitMatch(n,v)}return u()}i.shift();for(var d=0;d<g;d++){var v=p[d];var b;if(e){if(e!=="/")v=e+"/"+v;else v=e+v}this._process([v].concat(i),n,s,u)}u()};Glob.prototype._emitMatch=function(e,t){if(this.aborted)return;if(E(this,t))return;if(this.paused){this._emitQueue.push([e,t]);return}var r=h(t)?t:this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute)t=r;if(this.matches[e][t])return;if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[e][t]=true;var n=this.statCache[r];if(n)this.emit("stat",t,n);this.emit("match",t)};Glob.prototype._readdirInGlobStar=function(e,t){if(this.aborted)return;if(this.follow)return this._readdir(e,false,t);var r="lstat\0"+e;var n=this;var s=b(r,lstatcb_);if(s)i.lstat(e,s);function lstatcb_(r,i){if(r&&r.code==="ENOENT")return t();var s=i&&i.isSymbolicLink();n.symlinks[e]=s;if(!s&&i&&!i.isDirectory()){n.cache[e]="FILE";t()}else n._readdir(e,false,t)}};Glob.prototype._readdir=function(e,t,r){if(this.aborted)return;r=b("readdir\0"+e+"\0"+t,r);if(!r)return;if(t&&!g(this.symlinks,e))return this._readdirInGlobStar(e,r);if(g(this.cache,e)){var n=this.cache[e];if(!n||n==="FILE")return r();if(Array.isArray(n))return r(null,n)}var s=this;i.readdir(e,readdirCb(this,e,r))};function readdirCb(e,t,r){return function(i,n){if(i)e._readdirError(t,i,r);else e._readdirEntries(t,n,r)}}Glob.prototype._readdirEntries=function(e,t,r){if(this.aborted)return;if(!this.mark&&!this.stat){for(var i=0;i<t.length;i++){var n=t[i];if(e==="/")n=e+n;else n=e+"/"+n;this.cache[n]=true}}this.cache[e]=t;return r(null,t)};Glob.prototype._readdirError=function(e,t,r){if(this.aborted)return;switch(t.code){case"ENOTSUP":case"ENOTDIR":var i=this._makeAbs(e);this.cache[i]="FILE";if(i===this.cwdAbs){var n=new Error(t.code+" invalid cwd "+this.cwd);n.path=this.cwd;n.code=t.code;this.emit("error",n);this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict){this.emit("error",t);this.abort()}if(!this.silent)console.error("glob error",t);break}return r()};Glob.prototype._processGlobStar=function(e,t,r,i,n,s,a){var u=this;this._readdir(r,s,function(o,l){u._processGlobStar2(e,t,r,i,n,s,l,a)})};Glob.prototype._processGlobStar2=function(e,t,r,i,n,s,a,u){if(!a)return u();var o=i.slice(1);var l=e?[e]:[];var f=l.concat(o);this._process(f,n,false,u);var h=this.symlinks[r];var c=a.length;if(h&&s)return u();for(var p=0;p<c;p++){var d=a[p];if(d.charAt(0)==="."&&!this.dot)continue;var v=l.concat(a[p],o);this._process(v,n,true,u);var y=l.concat(a[p],i);this._process(y,n,true,u)}u()};Glob.prototype._processSimple=function(e,t,r){var i=this;this._stat(e,function(n,s){i._processSimple2(e,t,n,s,r)})};Glob.prototype._processSimple2=function(e,t,r,i,n){if(!this.matches[t])this.matches[t]=Object.create(null);if(!i)return n();if(e&&h(e)&&!this.nomount){var s=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=l.join(this.root,e)}else{e=l.resolve(this.root,e);if(s)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e);n()};Glob.prototype._stat=function(e,t){var r=this._makeAbs(e);var n=e.slice(-1)==="/";if(e.length>this.maxLength)return t();if(!this.stat&&g(this.cache,r)){var s=this.cache[r];if(Array.isArray(s))s="DIR";if(!n||s==="DIR")return t(null,s);if(n&&s==="FILE")return t()}var a;var u=this.statCache[r];if(u!==undefined){if(u===false)return t(null,u);else{var o=u.isDirectory()?"DIR":"FILE";if(n&&o==="FILE")return t();else return t(null,o,u)}}var l=this;var f=b("stat\0"+r,lstatcb_);if(f)i.lstat(r,f);function lstatcb_(n,s){if(s&&s.isSymbolicLink()){return i.stat(r,function(i,n){if(i)l._stat2(e,r,null,s,t);else l._stat2(e,r,i,n,t)})}else{l._stat2(e,r,n,s,t)}}};Glob.prototype._stat2=function(e,t,r,i,n){if(r&&(r.code==="ENOENT"||r.code==="ENOTDIR")){this.statCache[t]=false;return n()}var s=e.slice(-1)==="/";this.statCache[t]=i;if(t.slice(-1)==="/"&&i&&!i.isDirectory())return n(null,false,i);var a=true;if(i)a=i.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||a;if(s&&a==="FILE")return n();return n(null,a,i)}},337:function(e,t,r){"use strict";const i=r(589);const n=r(607);const s=r(685);const a=/[\uD800-\uDFFF]./;const u=n.filter(e=>a.test(e));const o={};function encodeStringToEmoji(e,t){if(o[e]){return o[e]}t=t||1;const r=[];do{if(!u.length){throw new Error("Ran out of emoji")}const e=Math.floor(Math.random()*u.length);r.push(u[e]);u.splice(e,1)}while(--t>0);const i=r.join("");o[e]=i;return i}function interpolateName(e,t,r){let n;if(typeof t==="function"){n=t(e.resourcePath)}else{n=t||"[hash].[ext]"}const a=r.context;const u=r.content;const o=r.regExp;let l="bin";let f="file";let h="";let c="";if(e.resourcePath){const t=i.parse(e.resourcePath);let r=e.resourcePath;if(t.ext){l=t.ext.substr(1)}if(t.dir){f=t.name;r=t.dir+i.sep}if(typeof a!=="undefined"){h=i.relative(a,r+"_").replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1");h=h.substr(0,h.length-1)}else{h=r.replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1")}if(h.length===1){h=""}else if(h.length>1){c=i.basename(h)}}let p=n;if(u){p=p.replace(/\[(?:([^:\]]+):)?(?:hash|contenthash)(?::([a-z]+\d*))?(?::(\d+))?\]/gi,(e,t,r,i)=>s(u,t,r,parseInt(i,10))).replace(/\[emoji(?::(\d+))?\]/gi,(e,t)=>encodeStringToEmoji(u,parseInt(t,10)))}p=p.replace(/\[ext\]/gi,()=>l).replace(/\[name\]/gi,()=>f).replace(/\[path\]/gi,()=>h).replace(/\[folder\]/gi,()=>c);if(o&&e.resourcePath){const t=e.resourcePath.match(new RegExp(o));t&&t.forEach((e,t)=>{p=p.replace(new RegExp("\\["+t+"\\]","ig"),e)})}if(typeof e.options==="object"&&typeof e.options.customInterpolateName==="function"){p=e.options.customInterpolateName.call(e,p,t,r)}return p}e.exports=interpolateName},341:function(e){var t={}.toString;e.exports=Array.isArray||function(e){return t.call(e)=="[object Array]"}},347:function(e){e.exports={assert:true,async_hooks:">= 8",buffer_ieee754:"< 0.9.7",buffer:true,child_process:true,cluster:true,console:true,constants:true,crypto:true,_debugger:"< 8",dgram:true,dns:true,domain:true,events:true,freelist:"< 6",fs:true,"fs/promises":">= 10 && < 10.1",_http_agent:">= 0.11.1",_http_client:">= 0.11.1",_http_common:">= 0.11.1",_http_incoming:">= 0.11.1",_http_outgoing:">= 0.11.1",_http_server:">= 0.11.1",http:true,http2:">= 8.8",https:true,inspector:">= 8.0.0",_linklist:"< 8",module:true,net:true,"node-inspect/lib/_inspect":">= 7.6.0","node-inspect/lib/internal/inspect_client":">= 7.6.0","node-inspect/lib/internal/inspect_repl":">= 7.6.0",os:true,path:true,perf_hooks:">= 8.5",process:">= 1",punycode:true,querystring:true,readline:true,repl:true,smalloc:">= 0.11.5 && < 3",_stream_duplex:">= 0.9.4",_stream_transform:">= 0.9.4",_stream_wrap:">= 1.4.1",_stream_passthrough:">= 0.9.4",_stream_readable:">= 0.9.4",_stream_writable:">= 0.9.4",stream:true,string_decoder:true,sys:true,timers:true,_tls_common:">= 0.11.13",_tls_legacy:">= 0.11.3 && < 10",_tls_wrap:">= 0.11.3",tls:true,trace_events:">= 10",tty:true,url:true,util:true,"v8/tools/arguments":">= 10","v8/tools/codemap":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/consarray":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/csvparser":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/logreader":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/profile_view":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/splaytree":[">= 4.4.0 && < 5",">= 5.2.0"],v8:">= 1",vm:true,worker_threads:">= 11.7",zlib:true}},348:function(e,t,r){var i=r(20);var n=r(491);e.exports=expandTop;var s="\0SLASH"+Math.random()+"\0";var a="\0OPEN"+Math.random()+"\0";var u="\0CLOSE"+Math.random()+"\0";var o="\0COMMA"+Math.random()+"\0";var l="\0PERIOD"+Math.random()+"\0";function numeric(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function escapeBraces(e){return e.split("\\\\").join(s).split("\\{").join(a).split("\\}").join(u).split("\\,").join(o).split("\\.").join(l)}function unescapeBraces(e){return e.split(s).join("\\").split(a).join("{").split(u).join("}").split(o).join(",").split(l).join(".")}function parseCommaParts(e){if(!e)return[""];var t=[];var r=n("{","}",e);if(!r)return e.split(",");var i=r.pre;var s=r.body;var a=r.post;var u=i.split(",");u[u.length-1]+="{"+s+"}";var o=parseCommaParts(a);if(a.length){u[u.length-1]+=o.shift();u.push.apply(u,o)}t.push.apply(t,u);return t}function expandTop(e){if(!e)return[];if(e.substr(0,2)==="{}"){e="\\{\\}"+e.substr(2)}return expand(escapeBraces(e),true).map(unescapeBraces)}function identity(e){return e}function embrace(e){return"{"+e+"}"}function isPadded(e){return/^-?0\d/.test(e)}function lte(e,t){return e<=t}function gte(e,t){return e>=t}function expand(e,t){var r=[];var s=n("{","}",e);if(!s||/\$$/.test(s.pre))return[e];var a=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(s.body);var o=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(s.body);var l=a||o;var f=s.body.indexOf(",")>=0;if(!l&&!f){if(s.post.match(/,.*\}/)){e=s.pre+"{"+s.body+u+s.post;return expand(e)}return[e]}var h;if(l){h=s.body.split(/\.\./)}else{h=parseCommaParts(s.body);if(h.length===1){h=expand(h[0],false).map(embrace);if(h.length===1){var c=s.post.length?expand(s.post,false):[""];return c.map(function(e){return s.pre+h[0]+e})}}}var p=s.pre;var c=s.post.length?expand(s.post,false):[""];var d;if(l){var v=numeric(h[0]);var y=numeric(h[1]);var g=Math.max(h[0].length,h[1].length);var b=h.length==3?Math.abs(numeric(h[2])):1;var _=lte;var m=y<v;if(m){b*=-1;_=gte}var E=h.some(isPadded);d=[];for(var D=v;_(D,y);D+=b){var w;if(o){w=String.fromCharCode(D);if(w==="\\")w=""}else{w=String(D);if(E){var A=g-w.length;if(A>0){var C=new Array(A+1).join("0");if(D<0)w="-"+C+w.slice(1);else w=C+w}}}d.push(w)}}else{d=i(h,function(e){return expand(e,false)})}for(var x=0;x<d.length;x++){for(var S=0;S<c.length;S++){var k=p+d[x]+c[S];if(!t||l||k)r.push(k)}}return r}},353:function(e,t,r){var i=r(393);var n=r(631);var s=r(485);if(typeof s!=="function"){s=s.EventEmitter}var a;if(process.__signal_exit_emitter__){a=process.__signal_exit_emitter__}else{a=process.__signal_exit_emitter__=new s;a.count=0;a.emitted={}}if(!a.infinite){a.setMaxListeners(Infinity);a.infinite=true}e.exports=function(e,t){i.equal(typeof e,"function","a callback must be provided for exit handler");if(o===false){load()}var r="exit";if(t&&t.alwaysLast){r="afterexit"}var n=function(){a.removeListener(r,e);if(a.listeners("exit").length===0&&a.listeners("afterexit").length===0){unload()}};a.on(r,e);return n};e.exports.unload=unload;function unload(){if(!o){return}o=false;n.forEach(function(e){try{process.removeListener(e,u[e])}catch(e){}});process.emit=f;process.reallyExit=l;a.count-=1}function emit(e,t,r){if(a.emitted[e]){return}a.emitted[e]=true;a.emit(e,t,r)}var u={};n.forEach(function(e){u[e]=function listener(){var t=process.listeners(e);if(t.length===a.count){unload();emit("exit",null,e);emit("afterexit",null,e);process.kill(process.pid,e)}}});e.exports.signals=function(){return n};e.exports.load=load;var o=false;function load(){if(o){return}o=true;a.count+=1;n=n.filter(function(e){try{process.on(e,u[e]);return true}catch(e){return false}});process.emit=processEmit;process.reallyExit=processReallyExit}var l=process.reallyExit;function processReallyExit(e){process.exitCode=e||0;emit("exit",process.exitCode,null);emit("afterexit",process.exitCode,null);l.call(process,process.exitCode)}var f=process.emit;function processEmit(e,t){if(e==="exit"){if(t!==undefined){process.exitCode=t}var r=f.apply(this,arguments);emit("exit",process.exitCode,null);emit("afterexit",process.exitCode,null);return r}else{return f.apply(this,arguments)}}},354:function(e,t){"use strict";var r="[";t.up=function up(e){return r+(e||"")+"A"};t.down=function down(e){return r+(e||"")+"B"};t.forward=function forward(e){return r+(e||"")+"C"};t.back=function back(e){return r+(e||"")+"D"};t.nextLine=function nextLine(e){return r+(e||"")+"E"};t.previousLine=function previousLine(e){return r+(e||"")+"F"};t.horizontalAbsolute=function horizontalAbsolute(e){if(e==null)throw new Error("horizontalAboslute requires a column to position to");return r+e+"G"};t.eraseData=function eraseData(){return r+"J"};t.eraseLine=function eraseLine(){return r+"K"};t.goto=function(e,t){return r+t+";"+e+"H"};t.gotoSOL=function(){return"\r"};t.beep=function(){return""};t.hideCursor=function hideCursor(){return r+"?25l"};t.showCursor=function showCursor(){return r+"?25h"};var i={reset:0,bold:1,italic:3,underline:4,inverse:7,stopBold:22,stopItalic:23,stopUnderline:24,stopInverse:27,white:37,black:30,blue:34,cyan:36,green:32,magenta:35,red:31,yellow:33,bgWhite:47,bgBlack:40,bgBlue:44,bgCyan:46,bgGreen:42,bgMagenta:45,bgRed:41,bgYellow:43,grey:90,brightBlack:90,brightRed:91,brightGreen:92,brightYellow:93,brightBlue:94,brightMagenta:95,brightCyan:96,brightWhite:97,bgGrey:100,bgBrightBlack:100,bgBrightRed:101,bgBrightGreen:102,bgBrightYellow:103,bgBrightBlue:104,bgBrightMagenta:105,bgBrightCyan:106,bgBrightWhite:107};t.color=function color(e){if(arguments.length!==1||!Array.isArray(e)){e=Array.prototype.slice.call(arguments)}return r+e.map(colorNameToCode).join(";")+"m"};function colorNameToCode(e){if(i[e]!=null)return i[e];throw new Error("Unknown color or style name: "+e)}},358:function(e,t,r){"use strict";var i=r(64);var n=r(663);var s=e.exports=function(e,t){n.call(this,e);this.workDone=0;this.workTodo=t||0};i.inherits(s,n);s.prototype.completed=function(){return this.workTodo===0?0:this.workDone/this.workTodo};s.prototype.addWork=function(e){this.workTodo+=e;this.emit("change",this.name,this.completed(),this)};s.prototype.completeWork=function(e){this.workDone+=e;if(this.workDone>this.workTodo)this.workDone=this.workTodo;this.emit("change",this.name,this.completed(),this)};s.prototype.finish=function(){this.workTodo=this.workDone=1;this.emit("change",this.name,1,this)}},377:function(e,t,r){var i=r(55);var n=r(66);var s=r(589);var a=r(68);var u=r(807);var o=r(764);var l=function isFile(e){try{var t=n.statSync(e)}catch(e){if(e&&(e.code==="ENOENT"||e.code==="ENOTDIR"))return false;throw e}return t.isFile()||t.isFIFO()};e.exports=function(e,t){if(typeof e!=="string"){throw new TypeError("Path must be a string.")}var r=o(e,t);var f=r.isFile||l;var h=r.readFileSync||n.readFileSync;var c=r.extensions||[".js"];var p=r.basedir||s.dirname(a());var d=r.filename||p;r.paths=r.paths||[];var v=s.resolve(p);if(r.preserveSymlinks===false){try{v=n.realpathSync(v)}catch(e){if(e.code!=="ENOENT"){throw e}}}if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var y=s.resolve(v,e);if(e===".."||e.slice(-1)==="/")y+="/";var g=loadAsFileSync(y)||loadAsDirectorySync(y);if(g)return g}else{var b=loadNodeModulesSync(e,v);if(b)return b}if(i[e])return e;var _=new Error("Cannot find module '"+e+"' from '"+d+"'");_.code="MODULE_NOT_FOUND";throw _;function loadAsFileSync(e){var t=loadpkg(s.dirname(e));if(t&&t.dir&&t.pkg&&r.pathFilter){var i=s.relative(t.dir,e);var n=r.pathFilter(t.pkg,e,i);if(n){e=s.resolve(t.dir,n)}}if(f(e)){return e}for(var a=0;a<c.length;a++){var u=e+c[a];if(f(u)){return u}}}function loadpkg(e){if(e===""||e==="/")return;if(process.platform==="win32"&&/^\w:[/\\]*$/.test(e)){return}if(/[/\\]node_modules[/\\]*$/.test(e))return;var t=s.join(e,"package.json");if(!f(t)){return loadpkg(s.dirname(e))}var i=h(t);try{var n=JSON.parse(i)}catch(e){}if(n&&r.packageFilter){n=r.packageFilter(n,e)}return{pkg:n,dir:e}}function loadAsDirectorySync(e){var t=s.join(e,"/package.json");if(f(t)){try{var i=h(t,"UTF8");var n=JSON.parse(i)}catch(e){}if(r.packageFilter){n=r.packageFilter(n,e)}if(n.main){if(typeof n.main!=="string"){var a=new TypeError("package “"+n.name+"” `main` must be a string");a.code="INVALID_PACKAGE_MAIN";throw a}if(n.main==="."||n.main==="./"){n.main="index"}try{var u=loadAsFileSync(s.resolve(e,n.main));if(u)return u;var o=loadAsDirectorySync(s.resolve(e,n.main));if(o)return o}catch(e){}}}return loadAsFileSync(s.join(e,"/index"))}function loadNodeModulesSync(e,t){var i=u(t,r,e);for(var n=0;n<i.length;n++){var a=i[n];var o=loadAsFileSync(s.join(a,"/",e));if(o)return o;var l=loadAsDirectorySync(s.join(a,"/",e));if(l)return l}}}},378:function(e,t,r){e.exports=rimraf;rimraf.sync=rimrafSync;var i=r(393);var n=r(589);var s=r(66);var a=r(327);var u=parseInt("666",8);var o={nosort:true,silent:true};var l=0;var f=process.platform==="win32";function defaults(e){var t=["unlink","chmod","stat","lstat","rmdir","readdir"];t.forEach(function(t){e[t]=e[t]||s[t];t=t+"Sync";e[t]=e[t]||s[t]});e.maxBusyTries=e.maxBusyTries||3;e.emfileWait=e.emfileWait||1e3;if(e.glob===false){e.disableGlob=true}e.disableGlob=e.disableGlob||false;e.glob=e.glob||o}function rimraf(e,t,r){if(typeof t==="function"){r=t;t={}}i(e,"rimraf: missing path");i.equal(typeof e,"string","rimraf: path should be a string");i.equal(typeof r,"function","rimraf: callback function required");i(t,"rimraf: invalid options argument provided");i.equal(typeof t,"object","rimraf: options should be object");defaults(t);var n=0;var s=null;var u=0;if(t.disableGlob||!a.hasMagic(e))return afterGlob(null,[e]);t.lstat(e,function(r,i){if(!r)return afterGlob(null,[e]);a(e,t.glob,afterGlob)});function next(e){s=s||e;if(--u===0)r(s)}function afterGlob(e,i){if(e)return r(e);u=i.length;if(u===0)return r();i.forEach(function(e){rimraf_(e,t,function CB(r){if(r){if((r.code==="EBUSY"||r.code==="ENOTEMPTY"||r.code==="EPERM")&&n<t.maxBusyTries){n++;var i=n*100;return setTimeout(function(){rimraf_(e,t,CB)},i)}if(r.code==="EMFILE"&&l<t.emfileWait){return setTimeout(function(){rimraf_(e,t,CB)},l++)}if(r.code==="ENOENT")r=null}l=0;next(r)})})}}function rimraf_(e,t,r){i(e);i(t);i(typeof r==="function");t.lstat(e,function(i,n){if(i&&i.code==="ENOENT")return r(null);if(i&&i.code==="EPERM"&&f)fixWinEPERM(e,t,i,r);if(n&&n.isDirectory())return rmdir(e,t,i,r);t.unlink(e,function(i){if(i){if(i.code==="ENOENT")return r(null);if(i.code==="EPERM")return f?fixWinEPERM(e,t,i,r):rmdir(e,t,i,r);if(i.code==="EISDIR")return rmdir(e,t,i,r)}return r(i)})})}function fixWinEPERM(e,t,r,n){i(e);i(t);i(typeof n==="function");if(r)i(r instanceof Error);t.chmod(e,u,function(i){if(i)n(i.code==="ENOENT"?null:r);else t.stat(e,function(i,s){if(i)n(i.code==="ENOENT"?null:r);else if(s.isDirectory())rmdir(e,t,r,n);else t.unlink(e,n)})})}function fixWinEPERMSync(e,t,r){i(e);i(t);if(r)i(r instanceof Error);try{t.chmodSync(e,u)}catch(e){if(e.code==="ENOENT")return;else throw r}try{var n=t.statSync(e)}catch(e){if(e.code==="ENOENT")return;else throw r}if(n.isDirectory())rmdirSync(e,t,r);else t.unlinkSync(e)}function rmdir(e,t,r,n){i(e);i(t);if(r)i(r instanceof Error);i(typeof n==="function");t.rmdir(e,function(i){if(i&&(i.code==="ENOTEMPTY"||i.code==="EEXIST"||i.code==="EPERM"))rmkids(e,t,n);else if(i&&i.code==="ENOTDIR")n(r);else n(i)})}function rmkids(e,t,r){i(e);i(t);i(typeof r==="function");t.readdir(e,function(i,s){if(i)return r(i);var a=s.length;if(a===0)return t.rmdir(e,r);var u;s.forEach(function(i){rimraf(n.join(e,i),t,function(i){if(u)return;if(i)return r(u=i);if(--a===0)t.rmdir(e,r)})})})}function rimrafSync(e,t){t=t||{};defaults(t);i(e,"rimraf: missing path");i.equal(typeof e,"string","rimraf: path should be a string");i(t,"rimraf: missing options");i.equal(typeof t,"object","rimraf: options should be object");var r;if(t.disableGlob||!a.hasMagic(e)){r=[e]}else{try{t.lstatSync(e);r=[e]}catch(i){r=a.sync(e,t.glob)}}if(!r.length)return;for(var n=0;n<r.length;n++){var e=r[n];try{var s=t.lstatSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM"&&f)fixWinEPERMSync(e,t,r)}try{if(s&&s.isDirectory())rmdirSync(e,t,null);else t.unlinkSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM")return f?fixWinEPERMSync(e,t,r):rmdirSync(e,t,r);if(r.code!=="EISDIR")throw r;rmdirSync(e,t,r)}}}function rmdirSync(e,t,r){i(e);i(t);if(r)i(r instanceof Error);try{t.rmdirSync(e)}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR")throw r;if(i.code==="ENOTEMPTY"||i.code==="EEXIST"||i.code==="EPERM")rmkidsSync(e,t)}}function rmkidsSync(e,t){i(e);i(t);t.readdirSync(e).forEach(function(r){rimrafSync(n.join(e,r),t)});var r=f?100:1;var s=0;do{var a=true;try{var u=t.rmdirSync(e,t);a=false;return u}finally{if(++s<r&&a)continue}}while(true)}},379:function(e,t,r){const i=r(589);const{readFileSync:n,readFile:s,stat:a,lstat:u,readlink:o,statSync:l}=r(813);const{walk:f}=r(825);const h=r(860);const{attachScopes:c}=r(308);const p=r(928);let d=r(996);const v=r(963);const y=r(288);const g=r(779);const b=r(327);const _=r(991);const m=r(755);const{pregyp:E,nbind:D}=r(781);const w=r(384);const{getOptions:A}=r(669);const C=r(564);const x=r(228);const S=r(431);const k=r(594);d=d.Parser.extend(r(668),r(392),r(906),r(872),r(389));const F=[".js",".json",".node"];const{UNKNOWN:R,FUNCTION:T,WILDCARD:O,wildcardRegEx:I}=p;function isIdentifierRead(e,t){switch(t.type){case"ObjectPattern":case"ArrayPattern":return false;case"AssignmentExpression":return t.right===e;case"MemberExpression":return t.computed||e===t.object;case"Property":return e===t.value;case"MethodDefinition":return false;case"VariableDeclarator":return t.id!==e;case"ExportSpecifier":return false;case"FunctionExpression":case"FunctionDeclaration":case"ArrowFunctionExpression":return false;default:return true}}function isVarLoop(e){return e.type==="ForStatement"||e.type==="ForInStatement"||e.type==="ForOfStatement"}function isLoop(e){return e.type==="ForStatement"||e.type==="ForInStatement"||e.type==="ForOfStatement"||e.type==="WhileStatement"||e.type==="DoWhileStatement"}const B=new Map;let N;function getAssetState(e,t){let r=B.get(t);if(!r){B.set(t,r={entryIds:getEntryIds(t),assets:Object.create(null),assetNames:Object.create(null),assetPermissions:Object.create(null),hadOptions:false})}if(!r.hadOptions){r.hadOptions=true;if(e&&e.existingAssetNames){e.existingAssetNames.forEach(e=>{r.assetNames[e]=true})}}return N=r}const L=e=>Array.prototype.concat.apply([],e);function getEntryIds(e){if(e.options.entry){if(typeof e.options.entry==="string"){try{return[C.sync(e.options.entry,{extensions:F})]}catch(e){return}}else if(typeof e.options.entry==="object"){try{return L(Object.values(e.options.entry).map(e=>{if(typeof e==="string"){return[e]}if(e&&Array.isArray(e.import)){return e.import}return[]})).map(e=>C.sync(e,{extensions:F}))}catch(e){return}}}}function assetBase(e){if(!e)return"";if(e.endsWith("/")||e.endsWith("\\"))return e;return e+"/"}const P={cwd:()=>{return J},env:{NODE_ENV:R,[R]:true},[R]:true};const j=Symbol();const q=Symbol();const $=Symbol();const M=Symbol();const U=Symbol();const H=Symbol();const W=Symbol();const G={access:M,accessSync:M,createReadStream:M,exists:M,existsSync:M,fstat:M,fstatSync:M,lstat:M,lstatSync:M,open:M,readFile:M,readFileSync:M,stat:M,statSync:M};const V=Object.assign(Object.create(null),{bindings:{default:H},express:{default:function(){return{[R]:true,set:j,engine:q}}},fs:{default:G,...G},process:{default:P,...P},path:{default:{}},os:{default:S,...S},"node-pre-gyp":E,"node-pre-gyp/lib/pre-binding":E,"node-pre-gyp/lib/pre-binding.js":E,"node-gyp-build":{default:W},nbind:{init:$,default:{init:$}},"resolve-from":{default:U}});const z={MONGOOSE_DRIVER_PATH:undefined};z.global=z.GLOBAL=z.globalThis=z;const K=Symbol();E.find[K]=true;const Q=V.path;Object.keys(i).forEach(e=>{const t=i[e];if(typeof t==="function"){const r=function(){return t.apply(this,arguments)};r[K]=true;Q[e]=Q.default[e]=r}else{Q[e]=Q.default[e]=t}});Q.resolve=Q.default.resolve=function(...e){return i.resolve.apply(this,[J,...e])};Q.resolve[K]=true;const X=new Set([".h",".cmake",".c",".cpp"]);const Z=new Set(["CHANGELOG.md","README.md","readme.md","changelog.md"]);let J;function backtrack(e,t){if(!t||t.type!=="ArrayExpression")return e.skip()}const Y=/^\/[^\/]+|^[a-z]:[\\/][^\\/]+/i;function isAbsolutePathStr(e){return typeof e==="string"&&e.match(Y)}const ee=Symbol();function generateWildcardRequire(e,t,r,n,s){const a=n.length;const u=t.endsWith(O);const o=t.indexOf(O);const l=t.substr(0,o);const f=t.substr(o+1);const h=f?"?(.@(js|json|node))":".@(js|json|node)";if(s)console.log("Generating wildcard requires for "+t.replace(O,"*"));let c=b.sync(l+"**"+f+h,{mark:true,ignore:"node_modules/**/*"});if(!c.length)return;const p=c.map((t,r)=>{const n=JSON.stringify(t.substring(l.length,t.lastIndexOf(f)));let s=i.relative(e,t).replace(/\\/g,"/");if(!s.startsWith("../"))s="./"+s;let a=r===0?"  ":"  else ";if(u&&n.endsWith('.js"'))a+=`if (arg === ${n} || arg === ${n.substr(0,n.length-4)}")`;else if(u&&n.endsWith('.json"'))a+=`if (arg === ${n} || arg === ${n.substr(0,n.length-6)}")`;else if(u&&n.endsWith('.node"'))a+=`if (arg === ${n} || arg === ${n.substr(0,n.length-6)}")`;else a+=`if (arg === ${n})`;a+=` return require(${JSON.stringify(s)});`;return a}).join("\n");n.push(`function __ncc_wildcard$${a} (arg) {\n${p}\n}`);return`__ncc_wildcard$${a}(${r})`}const te=new WeakSet;function injectPathHook(e,t){const{mainTemplate:r}=e;if(!te.has(r)){te.add(r);r.hooks.requireExtensions.tap("asset-relocator-loader",(e,r)=>{let n="";if(r.name){n=i.relative(i.dirname(r.name),".").replace(/\\/g,"/");if(n.length)n="/"+n}return`${e}\n__webpack_require__.ab = __dirname + ${JSON.stringify(n+"/"+assetBase(t))};`})}}e.exports=async function(e,t){if(this.cacheable)this.cacheable();this.async();const r=this.resourcePath;const E=i.dirname(r);const x=A(this);injectPathHook(this._compilation,x.outputAssetBase);if(r.endsWith(".node")){const t=getAssetState(x,this._compilation);const i=_(this.resourcePath)||E;await g(i,t,assetBase(x.outputAssetBase),this.emitFile);let n;if(!(n=t.assets[r]))n=t.assets[r]=y(r.substr(i.length+1).replace(/\\/g,"/"),r,t.assetNames);const s=await new Promise((e,t)=>a(r,(r,i)=>r?t(r):e(i.mode)));t.assetPermissions[n]=s;this.emitFile(assetBase(x.outputAssetBase)+n,e);this.callback(null,"module.exports = __non_webpack_require__(__webpack_require__.ab + "+JSON.stringify(n)+")");return}if(r.endsWith(".json"))return this.callback(null,S,t);let S=e.toString();if(typeof x.production==="boolean"&&P.env.NODE_ENV===R){P.env.NODE_ENV=x.production?"production":"dev"}if(!J){if(typeof x.cwd==="string")J=i.resolve(x.cwd);else J=process.cwd()}const B=getAssetState(x,this._compilation);const N=B.entryIds;const L=_(r);const G=e=>{let t=i.basename(e);if(e.endsWith(".node")){if(L)t=e.substr(L.length+1).replace(/\\/g,"/");const r=g(L,B,assetBase(x.outputAssetBase),this.emitFile);te=te.then(()=>{return r})}let n;if(!(n=B.assets[e])){n=B.assets[e]=y(t,e,B.assetNames);if(x.debugLog)console.log("Emitting "+e+" for static use in module "+r)}te=te.then(async()=>{const[t,r]=await Promise.all([new Promise((t,r)=>s(e,(e,i)=>e?r(e):t(i))),await new Promise((t,r)=>u(e,(e,i)=>e?r(e):t(i)))]);if(r.isSymbolicLink()){const t=await new Promise((t,r)=>{o(e,(e,i)=>e?r(e):t(i))});const r=i.dirname(e);B.assetSymlinks[assetBase(x.outputAssetBase)+n]=i.relative(r,i.resolve(r,t))}else{B.assetPermissions[assetBase(x.outputAssetBase)+n]=r.mode;this.addDependency(e);this.emitFile(assetBase(x.outputAssetBase)+n,t)}});return"__webpack_require__.ab + "+JSON.stringify(n)};const Q=(e,t)=>{const n=e.indexOf(O);const a=n===-1?e.length:e.lastIndexOf(i.sep,n);const l=e.substr(0,a);const f=e.substr(a);const h=f.replace(I,(e,t)=>{return f[t-1]===i.sep?"**/*":"*/**/*"})||"/**/*";if(x.debugLog)console.log("Emitting directory "+l+h+" for static use in module "+r);const c=i.basename(l);const p=B.assets[l]||(B.assets[l]=y(c,l,B.assetNames));B.assets[l]=p;const d=b.sync(l+h,{mark:true,ignore:"node_modules/**/*"}).filter(e=>!X.has(i.extname(e))&&!Z.has(i.basename(e))&&!e.endsWith("/"));if(!d.length)return;te=te.then(async()=>{await Promise.all(d.map(async e=>{const[t,r]=await Promise.all([new Promise((t,r)=>s(e,(e,i)=>e?r(e):t(i))),await new Promise((t,r)=>u(e,(e,i)=>e?r(e):t(i)))]);if(r.isSymbolicLink()){const t=await new Promise((t,r)=>{o(e,(e,i)=>e?r(e):t(i))});const r=i.dirname(e);B.assetSymlinks[assetBase(x.outputAssetBase)+p+e.substr(l.length)]=i.relative(r,i.resolve(r,t)).replace(/\\/g,"/")}else{B.assetPermissions[assetBase(x.outputAssetBase)+p+e.substr(l.length)]=r.mode;this.addDependency(e);this.emitFile(assetBase(x.outputAssetBase)+p+e.substr(l.length),t)}}))});let v="";let g="";if(t){let e=f;let r=true;for(const i of t){const t=e.indexOf(O);const n=e.substr(0,t);e=e.substr(t+1);if(r){g=n;r=false}else{v+=" + '"+JSON.stringify(n).slice(1,-1)+"'"}if(i.type==="SpreadElement")v+=" + "+S.substring(i.argument.start,i.argument.end)+".join('/')";else v+=" + "+S.substring(i.start,i.end)}if(e.length){v+=" + '"+JSON.stringify(e).slice(1,-1)+"'"}}return"__webpack_require__.ab + "+JSON.stringify(p+g)+v};let te=Promise.resolve();const re=new h(S);let ie,ne;try{ie=d.parse(S,{allowReturnOutsideFunction:true,ecmaVersion:2020});ne=false}catch(e){}if(!ie){try{ie=d.parse(S,{sourceType:"module",ecmaVersion:2020});ne=true}catch(e){return this.callback(null,S,t)}}let se=c(ie,"scope");let ae=false;const ue=Object.assign(Object.create(null),{__dirname:{shadowDepth:0,value:i.resolve(r,"..")},__filename:{shadowDepth:0,value:r},process:{shadowDepth:0,value:P}});if(!ne){ue.require={shadowDepth:0,value:{[T](e){const t=V[e];return t.default},resolve(e){return C.sync(e,{basedir:E,extensions:F})}}};ue.require.value.resolve[K]=true}let oe=[];function setKnownBinding(e,t){if(e==="require")return;ue[e]={shadowDepth:0,value:t}}function getKnownBinding(e){const t=ue[e];if(t){if(t.shadowDepth===0){return t.value}}}if(ne){for(const e of ie.body){if(e.type==="ImportDeclaration"){const t=e.source.value;const r=V[t];if(r){for(const t of e.specifiers){if(t.type==="ImportNamespaceSpecifier")setKnownBinding(t.local.name,r);else if(t.type==="ImportDefaultSpecifier"&&"default"in r)setKnownBinding(t.local.name,r.default);else if(t.type==="ImportSpecifier"&&t.imported.name in r)setKnownBinding(t.local.name,r[t.imported.name])}}}}}function computePureStaticValue(e,t=true){const r=Object.create(null);Object.keys(ue).forEach(e=>{r[e]=getKnownBinding(e)});Object.keys(z).forEach(e=>{r[e]=z[e]});const i=p(e,r,t);return i}let le,fe;let he=false;let ce;function isStaticRequire(e){return e&&e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="require"&&ue.require.shadowDepth===0&&e.arguments.length===1&&e.arguments[0].type==="Literal"}({ast:ie=ie,scope:se=se,transformed:ae=ae}=w({id:r,ast:ie,scope:se,pkgBase:L,magicString:re,options:x,emitAsset:G,emitAssetDirectory:Q})||{});f(ie,{enter(e,t){if(e.scope){se=e.scope;for(const t in e.scope.declarations){if(t in ue)ue[t].shadowDepth++}}if(le)return backtrack(this,t);if(e.type==="Identifier"){if(isIdentifierRead(e,t)){let r;if(typeof(r=getKnownBinding(e.name))==="string"&&r.match(Y)||r&&(typeof r==="function"||typeof r==="object")&&r[K]){fe={value:typeof r==="string"?r:undefined};le=e;return this.skip()}else if(!ne&&e.name==="require"&&ue.require.shadowDepth===0&&t.type!=="UnaryExpression"){re.overwrite(e.start,e.end,"__non_webpack_require__");ae=true;return this.skip()}else if(!ne&&e.name==="__non_webpack_require__"&&t.type!=="UnaryExpression"){re.overwrite(e.start,e.end,'eval("require")');ae=true;return this.skip()}}}else if(!ne&&e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="require"&&ue.require.shadowDepth===0&&e.arguments.length){const u=e.arguments[0];const{result:o,sawIdentifier:l}=computePureStaticValue(u,true);if(!o){if(u.type==="LogicalExpression"&&u.operator==="||"&&u.left.type==="Identifier"){ae=true;re.overwrite(u.start,u.end,S.substring(u.right.start,u.right.end));return this.skip()}ae=true;re.overwrite(e.callee.start,e.callee.end,"__non_webpack_require__");return this.skip()}else if(typeof o.value==="string"&&l){if(o.wildcards){const t=i.resolve(E,o.value);if(o.wildcards.length===1&&validAssetEmission(t)){const r=generateWildcardRequire(E,t,S.substring(o.wildcards[0].start,o.wildcards[0].end),oe,x.debugLog);if(r){re.overwrite(e.start,e.end,r);ae=true;return this.skip()}}}else if(o.value){re.overwrite(u.start,u.end,JSON.stringify(o.value));ae=true;return this.skip()}}else if(o&&typeof o.then==="string"&&typeof o.else==="string"&&l){const e=computePureStaticValue(o.test,true).result;if(e&&"value"in e){if(e){ae=true;re.overwrite(u.start,u.end,JSON.stringify(o.then));return this.skip()}else{ae=true;re.overwrite(u.start,u.end,JSON.stringify(o.else));return this.skip()}}else{const e=S.substring(o.test.start,o.test.end);ae=true;re.overwrite(u.start,u.end,`${e} ? ${JSON.stringify(o.then)} : ${JSON.stringify(o.else)}`);return this.skip()}}else if(t.type==="CallExpression"&&t.callee===e){if(o.value==="pkginfo"&&t.arguments.length&&t.arguments[0].type==="Identifier"&&t.arguments[0].name==="module"){let e=new Set;for(let r=1;r<t.arguments.length;r++){if(t.arguments[r].type==="Literal")e.add(t.arguments[r].value)}const i=m(r);if(i){try{var s=JSON.parse(n(i+"/package.json"));if(e.size){for(var a in s){if(!e.has(a))delete s[a]}}}catch(e){}if(s){ae=true;re.overwrite(t.start,t.end,`Object.assign(module.exports, ${JSON.stringify(s)})`);return this.skip()}}}return this.skip()}else{return this.skip()}}else if(!ne&&e.type==="MemberExpression"&&e.object.type==="Identifier"&&e.object.name==="require"&&ue.require.shadowDepth===0&&e.property.type==="Identifier"&&!e.computed){if(e.property.name==="main"&&t&&t.type==="BinaryExpression"&&(t.operator==="=="||t.operator==="===")){let i;i=t.right===e?t.left:t.right;if(i.type==="Identifier"&&i.name==="module"){if(N&&N.indexOf(r)!==-1){re.overwrite(i.start,i.end,"__non_webpack_require__.cache[eval('__filename')]")}else{re.overwrite(t.start,t.end,"false");ae=true;return this.skip()}}}if(e.property.name==="ensure"){return this.skip()}}else if(!ne&&e.type==="MemberExpression"&&e.object.type==="Identifier"&&e.object.name==="module"&&"module"in ue===false&&e.property.type==="Identifier"&&!e.computed&&e.property.name==="require"){re.overwrite(e.start,e.end,"require");e.type="Identifier";e.name="require";ae=true}else if(e.type==="CallExpression"){const r=computePureStaticValue(e.callee,false).result;if(r&&typeof r.value==="function"&&r.value[K]){fe=computePureStaticValue(e,true).result;if(fe){le=e;return backtrack(this,t)}}else if(r&&typeof r.value==="symbol"){switch(r.value){case ee:if(e.arguments.length===1&&e.arguments[0].type==="Literal"&&e.callee.type==="Identifier"&&ue.require.shadowDepth===0){ae=true;re.overwrite(e.callee.start,e.callee.end,"require");re.appendRight(e.start,ce+"(");re.appendLeft(e.end,", "+S.substring(e.arguments[0].start,e.arguments[0].end)+")");return this.skip()}break;case H:if(e.arguments.length){const r=computePureStaticValue(e.arguments[0],false).result;if(r&&r.value){let i=false;let n;if(typeof r.value==="object")n=r.value;else if(typeof r.value==="string")n={bindings:r.value};if(!n.path){i=true;n.path=true}n.module_root=L;let s;try{s=v(n)}catch(e){}if(s){fe={value:s};le=e;emitStaticChildAsset(i);return backtrack(this,t)}}}break;case W:if(e.arguments.length===1&&e.arguments[0].type==="Identifier"&&e.arguments[0].name==="__dirname"&&ue.__dirname.shadowDepth===0){ae=true;let r;try{r=k.path(E)}catch(e){}if(r){fe={value:r};le=e;emitStaticChildAsset(i);return backtrack(this,t)}}break;case U:if(e.arguments.length===2&&e.arguments[0].type==="Identifier"&&e.arguments[0].name==="__dirname"&&ue.__dirname.shadowDepth===0){ae=true;re.overwrite(e.start,e.arguments[0].end+1,"require.resolve(");return this.skip()}break;case $:if(e.arguments.length){const t=computePureStaticValue(e.arguments[0],false).result;if(t&&t.value){const r=D(t.value);if(r){r.path=i.relative(E,r.path);ae=true;const t=JSON.stringify(r.path.replace(/\\/g,"/"));re.overwrite(e.start,e.end,`({ bind: require(${t}).NBind.bind_value, lib: require(${t}) })`);return this.skip()}}}break;case j:if(e.arguments.length===2&&e.arguments[0].type==="Literal"&&e.arguments[0].value==="view engine"&&!he){ae=true;const t=S.substring(e.arguments[1].start,e.arguments[1].end);re.appendRight(e.callee.object.end,`.engine(${t}, require(${t}).__express)`);return this.skip()}break;case q:he=true;break;case M:if(e.arguments[0]){fe=computePureStaticValue(e.arguments[0],true).result;if(fe){le=e.arguments[0];return backtrack(this,t)}}break}}}else if(e.type==="VariableDeclaration"&&!isVarLoop(t)){for(const r of e.declarations){if(!r.init)continue;const e=computePureStaticValue(r.init,false).result;if(e&&"value"in e){if(r.id.type==="Identifier"){setKnownBinding(r.id.name,e.value)}else if(r.id.type==="ObjectPattern"){for(const t of r.id.properties){if(t.type!=="Property"||t.key.type!=="Identifier"||t.value.type!=="Identifier"||typeof e.value!=="object"||e.value===null||!(t.key.name in e.value))continue;setKnownBinding(t.value.name,e.value[t.key.name])}}if(isAbsolutePathStr(e.value)){fe=e;le=r.init;emitStaticChildAsset();return backtrack(this,t)}}}}else if(e.type==="AssignmentExpression"&&!isLoop(t)){const r=computePureStaticValue(e.right,false).result;if(r&&"value"in r){if(e.left.type==="Identifier"){setKnownBinding(e.left.name,r.value)}else if(e.left.type==="ObjectPattern"){for(const t of e.left.properties){if(t.type!=="Property"||t.key.type!=="Identifier"||t.value.type!=="Identifier"||typeof r.value!=="object"||r.value===null||!(t.key.name in r.value))continue;setKnownBinding(t.value.name,r.value[t.key.name])}}if(isAbsolutePathStr(r.value)){fe=r;le=e.right;emitStaticChildAsset();return backtrack(this,t)}}if(!ne&&e.right.type==="CallExpression"&&isStaticRequire(e.right.callee)&&e.right.callee.arguments[0].value==="esm"&&e.left.type==="Identifier"&&e.left.name==="require"){ae=true;re.overwrite(e.start,e.end,"");return this.skip()}}else if(!ne&&e.type==="ConditionalExpression"&&isStaticRequire(e.consequent)&&isStaticRequire(e.alternate)){const t=computePureStaticValue(e.test,false).result;if(t&&"value"in t){ae=true;if(t.value){re.overwrite(e.start,e.end,S.substring(e.consequent.start,e.consequent.end))}else{re.overwrite(e.start,e.end,S.substring(e.alternate.start,e.alternate.end))}return this.skip()}}else if(!ne&&(e.type==="FunctionDeclaration"||e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression")&&(e.arguments||e.params)[0]&&(e.arguments||e.params)[0].type==="Identifier"){let r,i;if((e.type==="ArrowFunctionExpression"||e.type==="FunctionExpression")&&t.type==="VariableDeclarator"&&t.id.type==="Identifier"){r=t.id;i=e.arguments||e.params}else if(e.id){r=e.id;i=e.arguments||e.params}if(r&&e.body.body){let n,s,a=false;for(let t=0;t<e.body.body.length;t++){if(e.body.body[t].type==="VariableDeclaration"&&!n){n=e.body.body[t].declarations.find(e=>e.id.type==="Identifier"&&e.init&&e.init.type==="CallExpression"&&e.init.callee.type==="Identifier"&&e.init.callee.name==="require"&&ue.require.shadowDepth===0&&e.init.arguments[0]&&e.init.arguments[0].type==="Identifier"&&e.init.arguments[0].name===i[0].name);if(n)s=e.body.body[t]}if(n&&e.body.body[t].type==="ReturnStatement"&&e.body.body[t].argument&&e.body.body[t].argument.type==="Identifier"&&e.body.body[t].argument.name===n.id.name){a=true;break}}if(a){let a=";";const u=e.type==="ArrowFunctionExpression"&&e.params[0].start===e.start;if(e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"){e=t;a=","}ce=r.name+"$$mod";setKnownBinding(r.name,ee);const o=a+S.substring(e.start,r.start)+ce+S.substring(r.end,i[0].start+!u)+(u?"(":"")+n.id.name+", "+S.substring(i[0].start,i[i.length-1].end+!u)+(u?")":"")+S.substring(i[0].end+!u,s.start)+S.substring(s.end,e.end);re.appendRight(e.end,o)}}}},leave(e,t){if(e.scope){se=se.parent;for(const t in e.scope.declarations){if(t in ue){if(ue[t].shadowDepth>0)ue[t].shadowDepth--;else delete ue[t]}}}if(le){const t=computePureStaticValue(e,true).result;if(t){if("value"in t&&typeof t.value!=="symbol"||typeof t.then!=="symbol"&&typeof t.else!=="symbol"){fe=t;le=e;return}}emitStaticChildAsset()}}});if(!ae)return this.callback(null,S,t);te.then(()=>{if(oe.length)re.appendLeft(ie.body[0].start,oe.join("\n")+"\n");S=re.toString();t=t||re.generateMap();if(t){t.sources=[r]}this.callback(null,S,t)});function validAssetEmission(e){if(!e)return;if(e===r)return;let t="";if(e.endsWith(i.sep))t=i.sep;else if(e.endsWith(i.sep+O))t=i.sep+O;else if(e.endsWith(O))t=O;if(!x.emitDirnameAll&&e===E+t)return;if(!x.emitFilterAssetBaseAll&&e===(x.filterAssetBase||J)+t)return;if(e.endsWith(i.sep+"node_modules"+t))return;if(E.startsWith(e.substr(0,e.length-t.length)+i.sep))return;if(L){const t=r.substr(0,r.indexOf(i.sep+"node_modules"))+i.sep+"node_modules"+i.sep;if(!e.startsWith(t)){if(x.debugLog){if(assetEmission(e))console.log("Skipping asset emission of "+e.replace(I,"*")+" for "+r+" as it is outside the package base "+L)}return}}else if(!e.startsWith(x.filterAssetBase||J)){if(x.debugLog){if(assetEmission(e))console.log("Skipping asset emission of "+e.replace(I,"*")+" for "+r+" as it is outside the filterAssetBase directory "+(x.filterAssetBase||J))}return}return assetEmission(e)}function assetEmission(e){const t=e.indexOf(O);const r=t===-1?e.length:e.lastIndexOf(i.sep,t);const n=e.substr(0,r);try{const e=l(n);if(t!==-1&&e.isFile())return;if(e.isFile())return G;if(e.isDirectory())return Q}catch(e){return}}function emitStaticChildAsset(e=false){if(isAbsolutePathStr(fe.value)){let t;try{t=i.resolve(fe.value)}catch(e){}let r;if(r=validAssetEmission(t)){let i=r(t,fe.wildcards);if(i){if(e)i="__non_webpack_require__("+i+")";re.overwrite(le.start,le.end,i);ae=true}}}else if(isAbsolutePathStr(fe.then)&&isAbsolutePathStr(fe.else)){let t;try{t=i.resolve(fe.then)}catch(e){}let r;try{r=i.resolve(fe.else)}catch(e){}let n;if(!e&&(n=validAssetEmission(t))&&n===validAssetEmission(r)){const e=n(t);const i=n(r);if(e&&i){re.overwrite(le.start,le.end,`${S.substring(fe.test.start,fe.test.end)} ? ${e} : ${i}`);ae=true}}}le=fe=undefined}};e.exports.raw=true;e.exports.getAssetPermissions=function(e){if(N)return N.assetPermissions[e]};e.exports.getSymlinks=function(){if(N)return N.assetSymlinks};e.exports.initAssetCache=e.exports.initAssetPermissionsCache=function(e,t){injectPathHook(e,t);const r=getEntryIds(e);if(!r)return;const i=N={entryIds:r,assets:Object.create(null),assetNames:Object.create(null),assetPermissions:Object.create(null),assetSymlinks:Object.create(null),hadOptions:false};B.set(e,i);const n=e.getCache?e.getCache():e.cache;n.get("/RelocateLoader/AssetState/"+JSON.stringify(r),null,(e,t)=>{if(e)console.error(e);if(t){const e=JSON.parse(t);if(e.assetPermissions)i.assetPermissions=e.assetPermissions;if(e.assetSymlinks)i.assetSymlinks=e.assetSymlinks}});e.compiler.hooks.afterCompile.tap("relocate-loader",e=>{const t=e.getCache?e.getCache():e.cache;t.store("/RelocateLoader/AssetState/"+JSON.stringify(r),null,JSON.stringify({assetPermissions:i.assetPermissions,assetSymlinks:i.assetSymlinks}),e=>{if(e)console.error(e)})})}},384:function(e,t,r){const i=r(589);const n=r(564);const s=r(66);const a=r(708);e.exports=function({id:e,code:t,pkgBase:r,ast:u,scope:o,magicString:l,emitAssetDirectory:f}){let h;({transformed:h,ast:u,scope:o}=a(u,o,l));if(h)return{transformed:h,ast:u,scope:o};if(e.endsWith("google-gax/build/src/grpc.js")||global._unit&&e.includes("google-gax")){for(const t of u.body){if(t.type==="VariableDeclaration"&&t.declarations[0].id.type==="Identifier"&&t.declarations[0].id.name==="googleProtoFilesDir"){const r=f(i.resolve(i.dirname(e),global._unit?"./":"../../../google-proto-files"));if(r){l.overwrite(t.declarations[0].init.start,t.declarations[0].init.end,r);t.declarations[0].init=null;return{transformed:true}}}}}else if(e.endsWith("socket.io/lib/index.js")||global._unit&&e.includes("socket.io")){function replaceResolvePathStatement(t){if(t.type==="ExpressionStatement"&&t.expression.type==="AssignmentExpression"&&t.expression.operator==="="&&t.expression.right.type==="CallExpression"&&t.expression.right.callee.type==="Identifier"&&t.expression.right.callee.name==="read"&&t.expression.right.arguments.length>=1&&t.expression.right.arguments[0].type==="CallExpression"&&t.expression.right.arguments[0].callee.type==="Identifier"&&t.expression.right.arguments[0].callee.name==="resolvePath"&&t.expression.right.arguments[0].arguments.length===1&&t.expression.right.arguments[0].arguments[0].type==="Literal"){const s=t.expression.right.arguments[0].arguments[0].value;try{var r=n.sync(s,{basedir:i.dirname(e)})}catch(e){return{transformed:false}}const a="/"+i.relative(i.dirname(e),r);t.expression.right.arguments[0]={type:"BinaryExpression",start:t.expression.right.arguments[0].start,end:t.expression.right.arguments[0].end,operator:"+",left:{type:"Identifier",name:"__dirname"},right:{type:"Literal",value:a,raw:JSON.stringify(a)}};return{transformed:true}}return{transformed:false}}for(const e of u.body){if(e.type==="ExpressionStatement"&&e.expression.type==="AssignmentExpression"&&e.expression.operator==="="&&e.expression.left.type==="MemberExpression"&&e.expression.left.object.type==="MemberExpression"&&e.expression.left.object.object.type==="Identifier"&&e.expression.left.object.object.name==="Server"&&e.expression.left.object.property.type==="Identifier"&&e.expression.left.object.property.name==="prototype"&&e.expression.left.property.type==="Identifier"&&e.expression.left.property.name==="serveClient"&&e.expression.right.type==="FunctionExpression"){let t;for(const r of e.expression.right.body.body)if(r.type==="IfStatement")t=r;const r=t&&t.consequent.body;let i=false;if(r&&r[0]&&r[0].type==="ExpressionStatement")i=replaceResolvePathStatement(r[0]);const n=r&&r[1]&&r[1].type==="TryStatement"&&r[1].block.body;if(n&&n[0])i=replaceResolvePathStatement(n[0])||i;return{transformed:i}}}}else if(e.endsWith("oracledb/lib/oracledb.js")||global._unit&&e.includes("oracledb")){for(const t of u.body){if(t.type==="ForStatement"&&t.body.body&&t.body.body[0]&&t.body.body[0].type==="TryStatement"&&t.body.body[0].block.body[0]&&t.body.body[0].block.body[0].type==="ExpressionStatement"&&t.body.body[0].block.body[0].expression.type==="AssignmentExpression"&&t.body.body[0].block.body[0].expression.operator==="="&&t.body.body[0].block.body[0].expression.left.type==="Identifier"&&t.body.body[0].block.body[0].expression.left.name==="oracledbCLib"&&t.body.body[0].block.body[0].expression.right.type==="CallExpression"&&t.body.body[0].block.body[0].expression.right.callee.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.callee.name==="require"&&t.body.body[0].block.body[0].expression.right.arguments.length===1&&t.body.body[0].block.body[0].expression.right.arguments[0].type==="MemberExpression"&&t.body.body[0].block.body[0].expression.right.arguments[0].computed===true&&t.body.body[0].block.body[0].expression.right.arguments[0].object.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.arguments[0].object.name==="binaryLocations"&&t.body.body[0].block.body[0].expression.right.arguments[0].property.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.arguments[0].property.name==="i"){const r=t.body.body[0].block.body[0].expression.right.arguments[0];t.body.body[0].block.body[0].expression.right.arguments=[{type:"Literal",value:"_"}];const i=global._unit?"3.0.0":JSON.parse(s.readFileSync(e.slice(0,-15)+"package.json")).version;const n=Number(i.slice(0,i.indexOf(".")))>=4;const a="oracledb-"+(n?i:"abi"+process.versions.modules)+"-"+process.platform+"-"+process.arch+".node";l.overwrite(r.start,r.end,global._unit?"'./oracledb.js'":"'../build/Release/"+a+"'");return{transformed:true}}}}return{transformed:false}}},387:function(e,t,r){"use strict";var i=r(238);var n=r(737);var s=r(485).EventEmitter;var a=t=e.exports=new s;var u=r(64);var o=r(745);var l=r(354);o(true);var f=process.stderr;Object.defineProperty(a,"stream",{set:function(e){f=e;if(this.gauge)this.gauge.setWriteTo(f,f)},get:function(){return f}});var h;a.useColor=function(){return h!=null?h:f.isTTY};a.enableColor=function(){h=true;this.gauge.setTheme({hasColor:h,hasUnicode:c})};a.disableColor=function(){h=false;this.gauge.setTheme({hasColor:h,hasUnicode:c})};a.level="info";a.gauge=new n(f,{enabled:false,theme:{hasColor:a.useColor()},template:[{type:"progressbar",length:20},{type:"activityIndicator",kerning:1,length:1},{type:"section",default:""},":",{type:"logline",kerning:1,default:""}]});a.tracker=new i.TrackerGroup;a.progressEnabled=a.gauge.isEnabled();var c;a.enableUnicode=function(){c=true;this.gauge.setTheme({hasColor:this.useColor(),hasUnicode:c})};a.disableUnicode=function(){c=false;this.gauge.setTheme({hasColor:this.useColor(),hasUnicode:c})};a.setGaugeThemeset=function(e){this.gauge.setThemeset(e)};a.setGaugeTemplate=function(e){this.gauge.setTemplate(e)};a.enableProgress=function(){if(this.progressEnabled)return;this.progressEnabled=true;this.tracker.on("change",this.showProgress);if(this._pause)return;this.gauge.enable()};a.disableProgress=function(){if(!this.progressEnabled)return;this.progressEnabled=false;this.tracker.removeListener("change",this.showProgress);this.gauge.disable()};var p=["newGroup","newItem","newStream"];var d=function(e){Object.keys(a).forEach(function(t){if(t[0]==="_")return;if(p.filter(function(e){return e===t}).length)return;if(e[t])return;if(typeof a[t]!=="function")return;var r=a[t];e[t]=function(){return r.apply(a,arguments)}});if(e instanceof i.TrackerGroup){p.forEach(function(t){var r=e[t];e[t]=function(){return d(r.apply(e,arguments))}})}return e};p.forEach(function(e){a[e]=function(){return d(this.tracker[e].apply(this.tracker,arguments))}});a.clearProgress=function(e){if(!this.progressEnabled)return e&&process.nextTick(e);this.gauge.hide(e)};a.showProgress=function(e,t){if(!this.progressEnabled)return;var r={};if(e)r.section=e;var i=a.record[a.record.length-1];if(i){r.subsection=i.prefix;var n=a.disp[i.level]||i.level;var s=this._format(n,a.style[i.level]);if(i.prefix)s+=" "+this._format(i.prefix,this.prefixStyle);s+=" "+i.message.split(/\r?\n/)[0];r.logline=s}r.completed=t||this.tracker.completed();this.gauge.show(r)}.bind(a);a.pause=function(){this._paused=true;if(this.progressEnabled)this.gauge.disable()};a.resume=function(){if(!this._paused)return;this._paused=false;var e=this._buffer;this._buffer=[];e.forEach(function(e){this.emitLog(e)},this);if(this.progressEnabled)this.gauge.enable()};a._buffer=[];var v=0;a.record=[];a.maxRecordSize=1e4;a.log=function(e,t,r){var i=this.levels[e];if(i===undefined){return this.emit("error",new Error(u.format("Undefined log level: %j",e)))}var n=new Array(arguments.length-2);var s=null;for(var a=2;a<arguments.length;a++){var o=n[a-2]=arguments[a];if(typeof o==="object"&&o&&o instanceof Error&&o.stack){Object.defineProperty(o,"stack",{value:s=o.stack+"",enumerable:true,writable:true})}}if(s)n.unshift(s+"\n");r=u.format.apply(u,n);var l={id:v++,level:e,prefix:String(t||""),message:r,messageRaw:n};this.emit("log",l);this.emit("log."+e,l);if(l.prefix)this.emit(l.prefix,l);this.record.push(l);var f=this.maxRecordSize;var h=this.record.length-f;if(h>f/10){var c=Math.floor(f*.9);this.record=this.record.slice(-1*c)}this.emitLog(l)}.bind(a);a.emitLog=function(e){if(this._paused){this._buffer.push(e);return}if(this.progressEnabled)this.gauge.pulse(e.prefix);var t=this.levels[e.level];if(t===undefined)return;if(t<this.levels[this.level])return;if(t>0&&!isFinite(t))return;var r=a.disp[e.level]!=null?a.disp[e.level]:e.level;this.clearProgress();e.message.split(/\r?\n/).forEach(function(t){if(this.heading){this.write(this.heading,this.headingStyle);this.write(" ")}this.write(r,a.style[e.level]);var i=e.prefix||"";if(i)this.write(" ");this.write(i,this.prefixStyle);this.write(" "+t+"\n")},this);this.showProgress()};a._format=function(e,t){if(!f)return;var r="";if(this.useColor()){t=t||{};var i=[];if(t.fg)i.push(t.fg);if(t.bg)i.push("bg"+t.bg[0].toUpperCase()+t.bg.slice(1));if(t.bold)i.push("bold");if(t.underline)i.push("underline");if(t.inverse)i.push("inverse");if(i.length)r+=l.color(i);if(t.beep)r+=l.beep()}r+=e;if(this.useColor()){r+=l.color("reset")}return r};a.write=function(e,t){if(!f)return;f.write(this._format(e,t))};a.addLevel=function(e,t,r,i){if(i==null)i=e;this.levels[e]=t;this.style[e]=r;if(!this[e]){this[e]=function(){var t=new Array(arguments.length+1);t[0]=e;for(var r=0;r<arguments.length;r++){t[r+1]=arguments[r]}return this.log.apply(this,t)}.bind(this)}this.disp[e]=i};a.prefixStyle={fg:"magenta"};a.headingStyle={fg:"white",bg:"black"};a.style={};a.levels={};a.disp={};a.addLevel("silly",-Infinity,{inverse:true},"sill");a.addLevel("verbose",1e3,{fg:"blue",bg:"black"},"verb");a.addLevel("info",2e3,{fg:"green"});a.addLevel("timing",2500,{fg:"green",bg:"black"});a.addLevel("http",3e3,{fg:"green",bg:"black"});a.addLevel("notice",3500,{fg:"blue",bg:"black"});a.addLevel("warn",4e3,{fg:"black",bg:"yellow"},"WARN");a.addLevel("error",5e3,{fg:"red",bg:"black"},"ERR!");a.addLevel("silent",Infinity);a.on("error",function(){})},389:function(e,t,r){"use strict";const i=r(140);e.exports=function(e){const t=i(e);const n=e.acorn||r(996);const s=n.tokTypes;return class extends t{_maybeParseFieldValue(e){if(this.eat(s.eq)){const t=this._inStaticFieldValue;this._inStaticFieldValue=true;e.value=this.parseExpression();this._inStaticFieldValue=t}else e.value=null}parseClassElement(e){if(this.options.ecmaVersion<8||!this.isContextual("static")){return super.parseClassElement.apply(this,arguments)}const t=this._branch();t.next();if([s.name,s.bracketL,s.string,this.privateNameToken].indexOf(t.type)==-1){return super.parseClassElement.apply(this,arguments)}if(t.type==s.bracketL){let e=0;do{if(t.eat(s.bracketL))++e;else if(t.eat(s.bracketR))--e;else t.next()}while(e>0)}else t.next();if(t.type!=s.eq&&!t.canInsertSemicolon()&&t.type!=s.semi){return super.parseClassElement.apply(this,arguments)}const r=this.startNode();r.static=this.eatContextual("static");if(this.type==this.privateNameToken){this.parsePrivateClassElementName(r)}else{this.parsePropertyName(r)}if(r.key.type==="Identifier"&&r.key.name==="constructor"||r.key.type==="Literal"&&!r.computed&&r.key.value==="constructor"){this.raise(r.key.start,"Classes may not have a field called constructor")}if((r.key.name||r.key.value)==="prototype"&&!r.computed){this.raise(r.key.start,"Classes may not have a static property named prototype")}this._maybeParseFieldValue(r);this.finishNode(r,"FieldDefinition");this.semicolon();return r}parsePropertyName(e){if(e.static&&this.type==this.privateNameToken){this.parsePrivateClassElementName(e)}else{super.parsePropertyName(e)}}parseIdent(e,t){const r=super.parseIdent(e,t);if(this._inStaticFieldValue&&r.name=="arguments")this.raise(r.start,"A static class field initializer may not contain arguments");return r}}}},392:function(e,t,r){"use strict";const i=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;const n=r(996).tokTypes;e.exports=function(e){return class extends e{parseExport(e,t){i.lastIndex=this.pos;const r=i.exec(this.input);const s=this.input.charAt(this.pos+r[0].length);if(s!=="*")return super.parseExport(e,t);this.next();const a=this.startNode();this.expect(n.star);if(this.eatContextual("as")){e.declaration=null;a.exported=this.parseIdent(true);this.checkExport(t,a.exported.name,this.lastTokStart);e.specifiers=[this.finishNode(a,"ExportNamespaceSpecifier")]}this.expectContextual("from");if(this.type!==n.string)this.unexpected();e.source=this.parseExprAtom();this.semicolon();return this.finishNode(e,e.specifiers?"ExportNamedDeclaration":"ExportAllDeclaration")}}}},393:function(e){e.exports=__webpack_require__(357)},402:function(e,t,r){"use strict";function _classCallCheck(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var i=r(945).Buffer;var n=r(64);function copyBuffer(e,t,r){e.copy(t,r)}e.exports=function(){function BufferList(){_classCallCheck(this,BufferList);this.head=null;this.tail=null;this.length=0}BufferList.prototype.push=function push(e){var t={data:e,next:null};if(this.length>0)this.tail.next=t;else this.head=t;this.tail=t;++this.length};BufferList.prototype.unshift=function unshift(e){var t={data:e,next:this.head};if(this.length===0)this.tail=t;this.head=t;++this.length};BufferList.prototype.shift=function shift(){if(this.length===0)return;var e=this.head.data;if(this.length===1)this.head=this.tail=null;else this.head=this.head.next;--this.length;return e};BufferList.prototype.clear=function clear(){this.head=this.tail=null;this.length=0};BufferList.prototype.join=function join(e){if(this.length===0)return"";var t=this.head;var r=""+t.data;while(t=t.next){r+=e+t.data}return r};BufferList.prototype.concat=function concat(e){if(this.length===0)return i.alloc(0);if(this.length===1)return this.head.data;var t=i.allocUnsafe(e>>>0);var r=this.head;var n=0;while(r){copyBuffer(r.data,t,n);n+=r.data.length;r=r.next}return t};return BufferList}();if(n&&n.inspect&&n.inspect.custom){e.exports.prototype[n.inspect.custom]=function(){var e=n.inspect({length:this.length});return this.constructor.name+" "+e}}},408:function(e,t,r){var i=r(16);var n=Object.create(null);var s=r(83);e.exports=i(inflight);function inflight(e,t){if(n[e]){n[e].push(t);return null}else{n[e]=[t];return makeres(e)}}function makeres(e){return s(function RES(){var t=n[e];var r=t.length;var i=slice(arguments);try{for(var s=0;s<r;s++){t[s].apply(null,i)}}finally{if(t.length>r){t.splice(0,r);process.nextTick(function(){RES.apply(null,i)})}else{delete n[e]}}})}function slice(e){var t=e.length;var r=[];for(var i=0;i<t;i++)r[i]=e[i];return r}},421:function(e,t,r){"use strict";var i=r(945).Buffer;var n=i.isEncoding||function(e){e=""+e;switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return true;default:return false}};function _normalizeEncoding(e){if(!e)return"utf8";var t;while(true){switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase();t=true}}}function normalizeEncoding(e){var t=_normalizeEncoding(e);if(typeof t!=="string"&&(i.isEncoding===n||!n(e)))throw new Error("Unknown encoding: "+e);return t||e}t.StringDecoder=StringDecoder;function StringDecoder(e){this.encoding=normalizeEncoding(e);var t;switch(this.encoding){case"utf16le":this.text=utf16Text;this.end=utf16End;t=4;break;case"utf8":this.fillLast=utf8FillLast;t=4;break;case"base64":this.text=base64Text;this.end=base64End;t=3;break;default:this.write=simpleWrite;this.end=simpleEnd;return}this.lastNeed=0;this.lastTotal=0;this.lastChar=i.allocUnsafe(t)}StringDecoder.prototype.write=function(e){if(e.length===0)return"";var t;var r;if(this.lastNeed){t=this.fillLast(e);if(t===undefined)return"";r=this.lastNeed;this.lastNeed=0}else{r=0}if(r<e.length)return t?t+this.text(e,r):this.text(e,r);return t||""};StringDecoder.prototype.end=utf8End;StringDecoder.prototype.text=utf8Text;StringDecoder.prototype.fillLast=function(e){if(this.lastNeed<=e.length){e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length);this.lastNeed-=e.length};function utf8CheckByte(e){if(e<=127)return 0;else if(e>>5===6)return 2;else if(e>>4===14)return 3;else if(e>>3===30)return 4;return e>>6===2?-1:-2}function utf8CheckIncomplete(e,t,r){var i=t.length-1;if(i<r)return 0;var n=utf8CheckByte(t[i]);if(n>=0){if(n>0)e.lastNeed=n-1;return n}if(--i<r||n===-2)return 0;n=utf8CheckByte(t[i]);if(n>=0){if(n>0)e.lastNeed=n-2;return n}if(--i<r||n===-2)return 0;n=utf8CheckByte(t[i]);if(n>=0){if(n>0){if(n===2)n=0;else e.lastNeed=n-3}return n}return 0}function utf8CheckExtraBytes(e,t,r){if((t[0]&192)!==128){e.lastNeed=0;return"�"}if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128){e.lastNeed=1;return"�"}if(e.lastNeed>2&&t.length>2){if((t[2]&192)!==128){e.lastNeed=2;return"�"}}}}function utf8FillLast(e){var t=this.lastTotal-this.lastNeed;var r=utf8CheckExtraBytes(this,e,t);if(r!==undefined)return r;if(this.lastNeed<=e.length){e.copy(this.lastChar,t,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,t,0,e.length);this.lastNeed-=e.length}function utf8Text(e,t){var r=utf8CheckIncomplete(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var i=e.length-(r-this.lastNeed);e.copy(this.lastChar,0,i);return e.toString("utf8",t,i)}function utf8End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+"�";return t}function utf16Text(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319){this.lastNeed=2;this.lastTotal=4;this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1];return r.slice(0,-1)}}return r}this.lastNeed=1;this.lastTotal=2;this.lastChar[0]=e[e.length-1];return e.toString("utf16le",t,e.length-1)}function utf16End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function base64Text(e,t){var r=(e.length-t)%3;if(r===0)return e.toString("base64",t);this.lastNeed=3-r;this.lastTotal=3;if(r===1){this.lastChar[0]=e[e.length-1]}else{this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1]}return e.toString("base64",t,e.length-r)}function base64End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+this.lastChar.toString("base64",0,3-this.lastNeed);return t}function simpleWrite(e){return e.toString(this.encoding)}function simpleEnd(e){return e&&e.length?this.write(e):""}},431:function(e){e.exports=__webpack_require__(87)},433:function(e,t,r){"use strict";var i=r(22);e.exports=TemplateItem;function isPercent(e){if(typeof e!=="string")return false;return e.slice(-1)==="%"}function percent(e){return Number(e.slice(0,-1))/100}function TemplateItem(e,t){this.overallOutputLength=t;this.finished=false;this.type=null;this.value=null;this.length=null;this.maxLength=null;this.minLength=null;this.kerning=null;this.align="left";this.padLeft=0;this.padRight=0;this.index=null;this.first=null;this.last=null;if(typeof e==="string"){this.value=e}else{for(var r in e)this[r]=e[r]}if(isPercent(this.length)){this.length=Math.round(this.overallOutputLength*percent(this.length))}if(isPercent(this.minLength)){this.minLength=Math.round(this.overallOutputLength*percent(this.minLength))}if(isPercent(this.maxLength)){this.maxLength=Math.round(this.overallOutputLength*percent(this.maxLength))}return this}TemplateItem.prototype={};TemplateItem.prototype.getBaseLength=function(){var e=this.length;if(e==null&&typeof this.value==="string"&&this.maxLength==null&&this.minLength==null){e=i(this.value)}return e};TemplateItem.prototype.getLength=function(){var e=this.getBaseLength();if(e==null)return null;return e+this.padLeft+this.padRight};TemplateItem.prototype.getMaxLength=function(){if(this.maxLength==null)return null;return this.maxLength+this.padLeft+this.padRight};TemplateItem.prototype.getMinLength=function(){if(this.minLength==null)return null;return this.minLength+this.padLeft+this.padRight}},445:function(e,t,r){"use strict";var i=r(66);var n=r(378);var s=r(387);e.exports=t;var a=process.version.substr(1).replace(/-.*$/,"").split(".").map(function(e){return+e});var u=["build","clean","configure","package","publish","reveal","testbinary","testpackage","unpublish"];var o="napi_build_version=";e.exports.get_napi_version=function(e){var t=process.versions.napi;if(!t){if(a[0]===9&&a[1]>=3)t=2;else if(a[0]===8)t=1}return t};e.exports.get_napi_version_as_string=function(t){var r=e.exports.get_napi_version(t);return r?""+r:""};e.exports.validate_package_json=function(t,r){var i=t.binary;var n=pathOK(i.module_path);var s=pathOK(i.remote_path);var a=pathOK(i.package_name);var u=e.exports.get_napi_build_versions(t,r,true);var o=e.exports.get_napi_build_versions_raw(t);if(u){u.forEach(function(e){if(!(parseInt(e,10)===e&&e>0)){throw new Error("All values specified in napi_versions must be positive integers.")}})}if(u&&(!n||!s&&!a)){throw new Error("When napi_versions is specified; module_path and either remote_path or "+"package_name must contain the substitution string '{napi_build_version}`.")}if((n||s||a)&&!o){throw new Error("When the substitution string '{napi_build_version}` is specified in "+"module_path, remote_path, or package_name; napi_versions must also be specified.")}if(u&&!e.exports.get_best_napi_build_version(t,r)&&e.exports.build_napi_only(t)){throw new Error("The N-API version of this Node instance is "+e.exports.get_napi_version(r?r.target:undefined)+". "+"This module supports N-API version(s) "+e.exports.get_napi_build_versions_raw(t)+". "+"This Node instance cannot run this module.")}if(o&&!u&&e.exports.build_napi_only(t)){throw new Error("The N-API version of this Node instance is "+e.exports.get_napi_version(r?r.target:undefined)+". "+"This module supports N-API version(s) "+e.exports.get_napi_build_versions_raw(t)+". "+"This Node instance cannot run this module.")}};function pathOK(e){return e&&(e.indexOf("{napi_build_version}")!==-1||e.indexOf("{node_napi_label}")!==-1)}e.exports.expand_commands=function(t,r,i){var n=[];var s=e.exports.get_napi_build_versions(t,r);i.forEach(function(i){if(s&&i.name==="install"){var a=e.exports.get_best_napi_build_version(t,r);var l=a?[o+a]:[];n.push({name:i.name,args:l})}else if(s&&u.indexOf(i.name)!==-1){s.forEach(function(e){var t=i.args.slice();t.push(o+e);n.push({name:i.name,args:t})})}else{n.push(i)}});return n};e.exports.get_napi_build_versions=function(t,r,i){var n=[];var a=e.exports.get_napi_version(r?r.target:undefined);if(t.binary&&t.binary.napi_versions){t.binary.napi_versions.forEach(function(e){var t=n.indexOf(e)!==-1;if(!t&&a&&e<=a){n.push(e)}else if(i&&!t&&a){s.info("This Node instance does not support builds for N-API version",e)}})}if(r&&r["build-latest-napi-version-only"]){var u=0;n.forEach(function(e){if(e>u)u=e});n=u?[u]:[]}return n.length?n:undefined};e.exports.get_napi_build_versions_raw=function(e){var t=[];if(e.binary&&e.binary.napi_versions){e.binary.napi_versions.forEach(function(e){if(t.indexOf(e)===-1){t.push(e)}})}return t.length?t:undefined};e.exports.get_command_arg=function(e){return o+e};e.exports.get_napi_build_version_from_command_args=function(e){for(var t=0;t<e.length;t++){var r=e[t];if(r.indexOf(o)===0){return parseInt(r.substr(o.length),10)}}return undefined};e.exports.swap_build_dir_out=function(t){if(t){n.sync(e.exports.get_build_dir(t));i.renameSync("build",e.exports.get_build_dir(t))}};e.exports.swap_build_dir_in=function(t){if(t){n.sync("build");i.renameSync(e.exports.get_build_dir(t),"build")}};e.exports.get_build_dir=function(e){return"build-tmp-napi-v"+e};e.exports.get_best_napi_build_version=function(t,r){var i=0;var n=e.exports.get_napi_build_versions(t,r);if(n){var s=e.exports.get_napi_version(r?r.target:undefined);n.forEach(function(e){if(e>i&&e<=s){i=e}})}return i===0?undefined:i};e.exports.build_napi_only=function(e){return e.binary&&e.binary.package_name&&e.binary.package_name.indexOf("{node_napi_label}")===-1}},446:function(e){"use strict";e.exports=function(){return/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-PRZcf-nqry=><]/g}},468:function(e){e.exports=Delegator;function Delegator(e,t){if(!(this instanceof Delegator))return new Delegator(e,t);this.proto=e;this.target=t;this.methods=[];this.getters=[];this.setters=[];this.fluents=[]}Delegator.prototype.method=function(e){var t=this.proto;var r=this.target;this.methods.push(e);t[e]=function(){return this[r][e].apply(this[r],arguments)};return this};Delegator.prototype.access=function(e){return this.getter(e).setter(e)};Delegator.prototype.getter=function(e){var t=this.proto;var r=this.target;this.getters.push(e);t.__defineGetter__(e,function(){return this[r][e]});return this};Delegator.prototype.setter=function(e){var t=this.proto;var r=this.target;this.setters.push(e);t.__defineSetter__(e,function(t){return this[r][e]=t});return this};Delegator.prototype.fluent=function(e){var t=this.proto;var r=this.target;this.fluents.push(e);t[e]=function(t){if("undefined"!=typeof t){this[r][e]=t;return this}else{return this[r][e]}};return this}},480:function(e){(function(t){"use strict";var r,i=20,n=1,s=1e6,a=1e6,u=-7,o=21,l="[big.js] ",f=l+"Invalid ",h=f+"decimal places",c=f+"rounding mode",p=l+"Division by zero",d={},v=void 0,y=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function _Big_(){function Big(e){var t=this;if(!(t instanceof Big))return e===v?_Big_():new Big(e);if(e instanceof Big){t.s=e.s;t.e=e.e;t.c=e.c.slice()}else{parse(t,e)}t.constructor=Big}Big.prototype=d;Big.DP=i;Big.RM=n;Big.NE=u;Big.PE=o;Big.version="5.2.2";return Big}function parse(e,t){var r,i,n;if(t===0&&1/t<0)t="-0";else if(!y.test(t+=""))throw Error(f+"number");e.s=t.charAt(0)=="-"?(t=t.slice(1),-1):1;if((r=t.indexOf("."))>-1)t=t.replace(".","");if((i=t.search(/e/i))>0){if(r<0)r=i;r+=+t.slice(i+1);t=t.substring(0,i)}else if(r<0){r=t.length}n=t.length;for(i=0;i<n&&t.charAt(i)=="0";)++i;if(i==n){e.c=[e.e=0]}else{for(;n>0&&t.charAt(--n)=="0";);e.e=r-i-1;e.c=[];for(r=0;i<=n;)e.c[r++]=+t.charAt(i++)}return e}function round(e,t,r,i){var n=e.c,s=e.e+t+1;if(s<n.length){if(r===1){i=n[s]>=5}else if(r===2){i=n[s]>5||n[s]==5&&(i||s<0||n[s+1]!==v||n[s-1]&1)}else if(r===3){i=i||!!n[0]}else{i=false;if(r!==0)throw Error(c)}if(s<1){n.length=1;if(i){e.e=-t;n[0]=1}else{n[0]=e.e=0}}else{n.length=s--;if(i){for(;++n[s]>9;){n[s]=0;if(!s--){++e.e;n.unshift(1)}}}for(s=n.length;!n[--s];)n.pop()}}else if(r<0||r>3||r!==~~r){throw Error(c)}return e}function stringify(e,t,r,i){var n,a,u=e.constructor,o=!e.c[0];if(r!==v){if(r!==~~r||r<(t==3)||r>s){throw Error(t==3?f+"precision":h)}e=new u(e);r=i-e.e;if(e.c.length>++i)round(e,r,u.RM);if(t==2)i=e.e+r+1;for(;e.c.length<i;)e.c.push(0)}n=e.e;a=e.c.join("");r=a.length;if(t!=2&&(t==1||t==3&&i<=n||n<=u.NE||n>=u.PE)){a=a.charAt(0)+(r>1?"."+a.slice(1):"")+(n<0?"e":"e+")+n}else if(n<0){for(;++n;)a="0"+a;a="0."+a}else if(n>0){if(++n>r)for(n-=r;n--;)a+="0";else if(n<r)a=a.slice(0,n)+"."+a.slice(n)}else if(r>1){a=a.charAt(0)+"."+a.slice(1)}return e.s<0&&(!o||t==4)?"-"+a:a}d.abs=function(){var e=new this.constructor(this);e.s=1;return e};d.cmp=function(e){var t,r=this,i=r.c,n=(e=new r.constructor(e)).c,s=r.s,a=e.s,u=r.e,o=e.e;if(!i[0]||!n[0])return!i[0]?!n[0]?0:-a:s;if(s!=a)return s;t=s<0;if(u!=o)return u>o^t?1:-1;a=(u=i.length)<(o=n.length)?u:o;for(s=-1;++s<a;){if(i[s]!=n[s])return i[s]>n[s]^t?1:-1}return u==o?0:u>o^t?1:-1};d.div=function(e){var t=this,r=t.constructor,i=t.c,n=(e=new r(e)).c,a=t.s==e.s?1:-1,u=r.DP;if(u!==~~u||u<0||u>s)throw Error(h);if(!n[0])throw Error(p);if(!i[0])return new r(a*0);var o,l,f,c,d,y=n.slice(),g=o=n.length,b=i.length,_=i.slice(0,o),m=_.length,E=e,D=E.c=[],w=0,A=u+(E.e=t.e-e.e)+1;E.s=a;a=A<0?0:A;y.unshift(0);for(;m++<o;)_.push(0);do{for(f=0;f<10;f++){if(o!=(m=_.length)){c=o>m?1:-1}else{for(d=-1,c=0;++d<o;){if(n[d]!=_[d]){c=n[d]>_[d]?1:-1;break}}}if(c<0){for(l=m==o?n:y;m;){if(_[--m]<l[m]){d=m;for(;d&&!_[--d];)_[d]=9;--_[d];_[m]+=10}_[m]-=l[m]}for(;!_[0];)_.shift()}else{break}}D[w++]=c?f:++f;if(_[0]&&c)_[m]=i[g]||0;else _=[i[g]]}while((g++<b||_[0]!==v)&&a--);if(!D[0]&&w!=1){D.shift();E.e--}if(w>A)round(E,u,r.RM,_[0]!==v);return E};d.eq=function(e){return!this.cmp(e)};d.gt=function(e){return this.cmp(e)>0};d.gte=function(e){return this.cmp(e)>-1};d.lt=function(e){return this.cmp(e)<0};d.lte=function(e){return this.cmp(e)<1};d.minus=d.sub=function(e){var t,r,i,n,s=this,a=s.constructor,u=s.s,o=(e=new a(e)).s;if(u!=o){e.s=-o;return s.plus(e)}var l=s.c.slice(),f=s.e,h=e.c,c=e.e;if(!l[0]||!h[0]){return h[0]?(e.s=-o,e):new a(l[0]?s:0)}if(u=f-c){if(n=u<0){u=-u;i=l}else{c=f;i=h}i.reverse();for(o=u;o--;)i.push(0);i.reverse()}else{r=((n=l.length<h.length)?l:h).length;for(u=o=0;o<r;o++){if(l[o]!=h[o]){n=l[o]<h[o];break}}}if(n){i=l;l=h;h=i;e.s=-e.s}if((o=(r=h.length)-(t=l.length))>0)for(;o--;)l[t++]=0;for(o=t;r>u;){if(l[--r]<h[r]){for(t=r;t&&!l[--t];)l[t]=9;--l[t];l[r]+=10}l[r]-=h[r]}for(;l[--o]===0;)l.pop();for(;l[0]===0;){l.shift();--c}if(!l[0]){e.s=1;l=[c=0]}e.c=l;e.e=c;return e};d.mod=function(e){var t,r=this,i=r.constructor,n=r.s,s=(e=new i(e)).s;if(!e.c[0])throw Error(p);r.s=e.s=1;t=e.cmp(r)==1;r.s=n;e.s=s;if(t)return new i(r);n=i.DP;s=i.RM;i.DP=i.RM=0;r=r.div(e);i.DP=n;i.RM=s;return this.minus(r.times(e))};d.plus=d.add=function(e){var t,r=this,i=r.constructor,n=r.s,s=(e=new i(e)).s;if(n!=s){e.s=-s;return r.minus(e)}var a=r.e,u=r.c,o=e.e,l=e.c;if(!u[0]||!l[0])return l[0]?e:new i(u[0]?r:n*0);u=u.slice();if(n=a-o){if(n>0){o=a;t=l}else{n=-n;t=u}t.reverse();for(;n--;)t.push(0);t.reverse()}if(u.length-l.length<0){t=l;l=u;u=t}n=l.length;for(s=0;n;u[n]%=10)s=(u[--n]=u[n]+l[n]+s)/10|0;if(s){u.unshift(s);++o}for(n=u.length;u[--n]===0;)u.pop();e.c=u;e.e=o;return e};d.pow=function(e){var t=this,r=new t.constructor(1),i=r,n=e<0;if(e!==~~e||e<-a||e>a)throw Error(f+"exponent");if(n)e=-e;for(;;){if(e&1)i=i.times(t);e>>=1;if(!e)break;t=t.times(t)}return n?r.div(i):i};d.round=function(e,t){var r=this.constructor;if(e===v)e=0;else if(e!==~~e||e<-s||e>s)throw Error(h);return round(new r(this),e,t===v?r.RM:t)};d.sqrt=function(){var e,t,r,i=this,n=i.constructor,s=i.s,a=i.e,u=new n(.5);if(!i.c[0])return new n(i);if(s<0)throw Error(l+"No square root");s=Math.sqrt(i+"");if(s===0||s===1/0){t=i.c.join("");if(!(t.length+a&1))t+="0";s=Math.sqrt(t);a=((a+1)/2|0)-(a<0||a&1);e=new n((s==1/0?"1e":(s=s.toExponential()).slice(0,s.indexOf("e")+1))+a)}else{e=new n(s)}a=e.e+(n.DP+=4);do{r=e;e=u.times(r.plus(i.div(r)))}while(r.c.slice(0,a).join("")!==e.c.slice(0,a).join(""));return round(e,n.DP-=4,n.RM)};d.times=d.mul=function(e){var t,r=this,i=r.constructor,n=r.c,s=(e=new i(e)).c,a=n.length,u=s.length,o=r.e,l=e.e;e.s=r.s==e.s?1:-1;if(!n[0]||!s[0])return new i(e.s*0);e.e=o+l;if(a<u){t=n;n=s;s=t;l=a;a=u;u=l}for(t=new Array(l=a+u);l--;)t[l]=0;for(o=u;o--;){u=0;for(l=a+o;l>o;){u=t[l]+s[o]*n[l-o-1]+u;t[l--]=u%10;u=u/10|0}t[l]=(t[l]+u)%10}if(u)++e.e;else t.shift();for(o=t.length;!t[--o];)t.pop();e.c=t;return e};d.toExponential=function(e){return stringify(this,1,e,e)};d.toFixed=function(e){return stringify(this,2,e,this.e+e)};d.toPrecision=function(e){return stringify(this,3,e,e-1)};d.toString=function(){return stringify(this)};d.valueOf=d.toJSON=function(){return stringify(this,4)};r=_Big_();r["default"]=r.Big=r;if(typeof define==="function"&&define.amd){define(function(){return r})}else if(true&&e.exports){e.exports=r}else{t.Big=r}})(this)},485:function(e){e.exports=__webpack_require__(614)},487:function(e,t,r){e.exports=globSync;globSync.GlobSync=GlobSync;var i=r(66);var n=r(129);var s=r(620);var a=s.Minimatch;var u=r(327).Glob;var o=r(64);var l=r(589);var f=r(393);var h=r(969);var c=r(922);var p=c.alphasort;var d=c.alphasorti;var v=c.setopts;var y=c.ownProp;var g=c.childrenIgnored;var b=c.isIgnored;function globSync(e,t){if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");return new GlobSync(e,t).found}function GlobSync(e,t){if(!e)throw new Error("must provide pattern");if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof GlobSync))return new GlobSync(e,t);v(this,e,t);if(this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var i=0;i<r;i++){this._process(this.minimatch.set[i],i,false)}this._finish()}GlobSync.prototype._finish=function(){f(this instanceof GlobSync);if(this.realpath){var e=this;this.matches.forEach(function(t,r){var i=e.matches[r]=Object.create(null);for(var s in t){try{s=e._makeAbs(s);var a=n.realpathSync(s,e.realpathCache);i[a]=true}catch(t){if(t.syscall==="stat")i[e._makeAbs(s)]=true;else throw t}}})}c.finish(this)};GlobSync.prototype._process=function(e,t,r){f(this instanceof GlobSync);var i=0;while(typeof e[i]==="string"){i++}var n;switch(i){case e.length:this._processSimple(e.join("/"),t);return;case 0:n=null;break;default:n=e.slice(0,i).join("/");break}var a=e.slice(i);var u;if(n===null)u=".";else if(h(n)||h(e.join("/"))){if(!n||!h(n))n="/"+n;u=n}else u=n;var o=this._makeAbs(u);if(g(this,u))return;var l=a[0]===s.GLOBSTAR;if(l)this._processGlobStar(n,u,o,a,t,r);else this._processReaddir(n,u,o,a,t,r)};GlobSync.prototype._processReaddir=function(e,t,r,i,n,s){var a=this._readdir(r,s);if(!a)return;var u=i[0];var o=!!this.minimatch.negate;var f=u._glob;var h=this.dot||f.charAt(0)===".";var c=[];for(var p=0;p<a.length;p++){var d=a[p];if(d.charAt(0)!=="."||h){var v;if(o&&!e){v=!d.match(u)}else{v=d.match(u)}if(v)c.push(d)}}var y=c.length;if(y===0)return;if(i.length===1&&!this.mark&&!this.stat){if(!this.matches[n])this.matches[n]=Object.create(null);for(var p=0;p<y;p++){var d=c[p];if(e){if(e.slice(-1)!=="/")d=e+"/"+d;else d=e+d}if(d.charAt(0)==="/"&&!this.nomount){d=l.join(this.root,d)}this._emitMatch(n,d)}return}i.shift();for(var p=0;p<y;p++){var d=c[p];var g;if(e)g=[e,d];else g=[d];this._process(g.concat(i),n,s)}};GlobSync.prototype._emitMatch=function(e,t){if(b(this,t))return;var r=this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute){t=r}if(this.matches[e][t])return;if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[e][t]=true;if(this.stat)this._stat(t)};GlobSync.prototype._readdirInGlobStar=function(e){if(this.follow)return this._readdir(e,false);var t;var r;var n;try{r=i.lstatSync(e)}catch(e){if(e.code==="ENOENT"){return null}}var s=r&&r.isSymbolicLink();this.symlinks[e]=s;if(!s&&r&&!r.isDirectory())this.cache[e]="FILE";else t=this._readdir(e,false);return t};GlobSync.prototype._readdir=function(e,t){var r;if(t&&!y(this.symlinks,e))return this._readdirInGlobStar(e);if(y(this.cache,e)){var n=this.cache[e];if(!n||n==="FILE")return null;if(Array.isArray(n))return n}try{return this._readdirEntries(e,i.readdirSync(e))}catch(t){this._readdirError(e,t);return null}};GlobSync.prototype._readdirEntries=function(e,t){if(!this.mark&&!this.stat){for(var r=0;r<t.length;r++){var i=t[r];if(e==="/")i=e+i;else i=e+"/"+i;this.cache[i]=true}}this.cache[e]=t;return t};GlobSync.prototype._readdirError=function(e,t){switch(t.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(e);this.cache[r]="FILE";if(r===this.cwdAbs){var i=new Error(t.code+" invalid cwd "+this.cwd);i.path=this.cwd;i.code=t.code;throw i}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict)throw t;if(!this.silent)console.error("glob error",t);break}};GlobSync.prototype._processGlobStar=function(e,t,r,i,n,s){var a=this._readdir(r,s);if(!a)return;var u=i.slice(1);var o=e?[e]:[];var l=o.concat(u);this._process(l,n,false);var f=a.length;var h=this.symlinks[r];if(h&&s)return;for(var c=0;c<f;c++){var p=a[c];if(p.charAt(0)==="."&&!this.dot)continue;var d=o.concat(a[c],u);this._process(d,n,true);var v=o.concat(a[c],i);this._process(v,n,true)}};GlobSync.prototype._processSimple=function(e,t){var r=this._stat(e);if(!this.matches[t])this.matches[t]=Object.create(null);if(!r)return;if(e&&h(e)&&!this.nomount){var i=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=l.join(this.root,e)}else{e=l.resolve(this.root,e);if(i)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e)};GlobSync.prototype._stat=function(e){var t=this._makeAbs(e);var r=e.slice(-1)==="/";if(e.length>this.maxLength)return false;if(!this.stat&&y(this.cache,t)){var n=this.cache[t];if(Array.isArray(n))n="DIR";if(!r||n==="DIR")return n;if(r&&n==="FILE")return false}var s;var a=this.statCache[t];if(!a){var u;try{u=i.lstatSync(t)}catch(e){if(e&&(e.code==="ENOENT"||e.code==="ENOTDIR")){this.statCache[t]=false;return false}}if(u&&u.isSymbolicLink()){try{a=i.statSync(t)}catch(e){a=u}}else{a=u}}this.statCache[t]=a;var n=true;if(a)n=a.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||n;if(r&&n==="FILE")return false;return n};GlobSync.prototype._mark=function(e){return c.mark(this,e)};GlobSync.prototype._makeAbs=function(e){return c.makeAbs(this,e)}},491:function(e){"use strict";e.exports=balanced;function balanced(e,t,r){if(e instanceof RegExp)e=maybeMatch(e,r);if(t instanceof RegExp)t=maybeMatch(t,r);var i=range(e,t,r);return i&&{start:i[0],end:i[1],pre:r.slice(0,i[0]),body:r.slice(i[0]+e.length,i[1]),post:r.slice(i[1]+t.length)}}function maybeMatch(e,t){var r=t.match(e);return r?r[0]:null}balanced.range=range;function range(e,t,r){var i,n,s,a,u;var o=r.indexOf(e);var l=r.indexOf(t,o+1);var f=o;if(o>=0&&l>0){i=[];s=r.length;while(f>=0&&!u){if(f==o){i.push(f);o=r.indexOf(e,f+1)}else if(i.length==1){u=[i.pop(),l]}else{n=i.pop();if(n<s){s=n;a=l}l=r.indexOf(t,f+1)}f=o<l&&o>=0?o:l}if(i.length){u=[s,a]}}return u}},500:function(e){"use strict";e.exports=(()=>{const e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[a-zA-Z\\d]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))"].join("|");return new RegExp(e,"g")})},502:function(e,t,r){"use strict";e.exports=PassThrough;var i=r(955);var n=r(683);n.inherits=r(207);n.inherits(PassThrough,i);function PassThrough(e){if(!(this instanceof PassThrough))return new PassThrough(e);i.call(this,e)}PassThrough.prototype._transform=function(e,t,r){r(null,e)}},503:function(e,t,r){"use strict";e.exports=t;var i=r(589);var n=r(579);var s=r(77);var a=r(630);var u=r(445);var o;if(process.env.NODE_PRE_GYP_ABI_CROSSWALK){o=require(process.env.NODE_PRE_GYP_ABI_CROSSWALK)}else{o=r(590)}var l={};Object.keys(o).forEach(function(e){var t=e.split(".")[0];if(!l[t]){l[t]=e}});function get_electron_abi(e,t){if(!e){throw new Error("get_electron_abi requires valid runtime arg")}if(typeof t==="undefined"){throw new Error("Empty target version is not supported if electron is the target.")}var r=n.parse(t);return e+"-v"+r.major+"."+r.minor}e.exports.get_electron_abi=get_electron_abi;function get_node_webkit_abi(e,t){if(!e){throw new Error("get_node_webkit_abi requires valid runtime arg")}if(typeof t==="undefined"){throw new Error("Empty target version is not supported if node-webkit is the target.")}return e+"-v"+t}e.exports.get_node_webkit_abi=get_node_webkit_abi;function get_node_abi(e,t){if(!e){throw new Error("get_node_abi requires valid runtime arg")}if(!t){throw new Error("get_node_abi requires valid process.versions object")}var r=n.parse(t.node);if(r.major===0&&r.minor%2){return e+"-v"+t.node}else{return t.modules?e+"-v"+ +t.modules:"v8-"+t.v8.split(".").slice(0,2).join(".")}}e.exports.get_node_abi=get_node_abi;function get_runtime_abi(e,t){if(!e){throw new Error("get_runtime_abi requires valid runtime arg")}if(e==="node-webkit"){return get_node_webkit_abi(e,t||process.versions["node-webkit"])}else if(e==="electron"){return get_electron_abi(e,t||process.versions.electron)}else{if(e!="node"){throw new Error("Unknown Runtime: '"+e+"'")}if(!t){return get_node_abi(e,process.versions)}else{var r;if(o[t]){r=o[t]}else{var i=t.split(".").map(function(e){return+e});if(i.length!=3){throw new Error("Unknown target version: "+t)}var n=i[0];var s=i[1];var a=i[2];if(n===1){while(true){if(s>0)--s;if(a>0)--a;var u=""+n+"."+s+"."+a;if(o[u]){r=o[u];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+u+" as ABI compatible target");break}if(s===0&&a===0){break}}}else if(n>=2){if(l[n]){r=o[l[n]];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+l[n]+" as ABI compatible target")}}else if(n===0){if(i[1]%2===0){while(--a>0){var f=""+n+"."+s+"."+a;if(o[f]){r=o[f];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+f+" as ABI compatible target");break}}}}}if(!r){throw new Error("Unsupported target version: "+t)}var h={node:t,v8:r.v8+".0",modules:r.node_abi>1?r.node_abi:undefined};return get_node_abi(e,h)}}}e.exports.get_runtime_abi=get_runtime_abi;var f=["module_name","module_path","host"];function validate_config(e,t){var r=e.name+" package.json is not node-pre-gyp ready:\n";var i=[];if(!e.main){i.push("main")}if(!e.version){i.push("version")}if(!e.name){i.push("name")}if(!e.binary){i.push("binary")}var n=e.binary;f.forEach(function(e){if(i.indexOf("binary")>-1){i.pop("binary")}if(!n||n[e]===undefined||n[e]===""){i.push("binary."+e)}});if(i.length>=1){throw new Error(r+"package.json must declare these properties: \n"+i.join("\n"))}if(n){var a=s.parse(n.host).protocol;if(a==="http:"){throw new Error("'host' protocol ("+a+") is invalid - only 'https:' is accepted")}}u.validate_package_json(e,t)}e.exports.validate_config=validate_config;function eval_template(e,t){Object.keys(t).forEach(function(r){var i="{"+r+"}";while(e.indexOf(i)>-1){e=e.replace(i,t[r])}});return e}function fix_slashes(e){if(e.slice(-1)!="/"){return e+"/"}return e}function drop_double_slashes(e){return e.replace(/\/\//g,"/")}function get_process_runtime(e){var t="node";if(e["node-webkit"]){t="node-webkit"}else if(e.electron){t="electron"}return t}e.exports.get_process_runtime=get_process_runtime;var h="{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz";var c="";e.exports.evaluate=function(e,t,r){t=t||{};validate_config(e,t);var o=e.version;var l=n.parse(o);var f=t.runtime||get_process_runtime(process.versions);var p={name:e.name,configuration:Boolean(t.debug)?"Debug":"Release",debug:t.debug,module_name:e.binary.module_name,version:l.version,prerelease:l.prerelease.length?l.prerelease.join("."):"",build:l.build.length?l.build.join("."):"",major:l.major,minor:l.minor,patch:l.patch,runtime:f,node_abi:get_runtime_abi(f,t.target),node_abi_napi:u.get_napi_version(t.target)?"napi":get_runtime_abi(f,t.target),napi_version:u.get_napi_version(t.target),napi_build_version:r||"",node_napi_label:r?"napi-v"+r:get_runtime_abi(f,t.target),target:t.target||"",platform:t.target_platform||process.platform,target_platform:t.target_platform||process.platform,arch:t.target_arch||process.arch,target_arch:t.target_arch||process.arch,libc:t.target_libc||a.family||"unknown",module_main:e.main,toolset:t.toolset||""};var d=process.env["npm_config_"+p.module_name+"_binary_host_mirror"]||e.binary.host;p.host=fix_slashes(eval_template(d,p));p.module_path=eval_template(e.binary.module_path,p);if(t.module_root){p.module_path=i.join(t.module_root,p.module_path)}else{p.module_path=i.resolve(p.module_path)}p.module=i.join(p.module_path,p.module_name+".node");p.remote_path=e.binary.remote_path?drop_double_slashes(fix_slashes(eval_template(e.binary.remote_path,p))):c;var v=e.binary.package_name?e.binary.package_name:h;p.package_name=eval_template(v,p);p.staged_tarball=i.join("build/stage",p.remote_path,p.package_name);p.hosted_path=s.resolve(p.host,p.remote_path);p.hosted_tarball=s.resolve(p.hosted_path,p.package_name);return p}},507:function(e,t,r){e.exports=r(64).deprecate},513:function(e){"use strict";e.exports=isWin32()||isColorTerm();function isWin32(){return process.platform==="win32"}function isColorTerm(){var e=/^screen|^xterm|^vt100|color|ansi|cygwin|linux/i;return!!process.env.COLORTERM||e.test(process.env.TERM)}},543:function(e,t,r){"use strict";const i=r(589);const n=/^\.\.?[/\\]/;function isAbsolutePath(e){return i.posix.isAbsolute(e)||i.win32.isAbsolute(e)}function isRelativePath(e){return n.test(e)}function stringifyRequest(e,t){const r=t.split("!");const n=e.context||e.options&&e.options.context;return JSON.stringify(r.map(e=>{const t=e.match(/^(.*?)(\?.*)/);const r=t?t[2]:"";let s=t?t[1]:e;if(isAbsolutePath(s)&&n){s=i.relative(n,s);if(isAbsolutePath(s)){return s+r}if(isRelativePath(s)===false){s="./"+s}}return s.replace(/\\/g,"/")+r}).join("!"))}e.exports=stringifyRequest},544:function(e,t,r){var i=r(589);var n=process.platform==="win32";var s=r(66);var a=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function rethrow(){var e;if(a){var t=new Error;e=debugCallback}else e=missingCallback;return e;function debugCallback(e){if(e){t.message=e.message;e=t;missingCallback(e)}}function missingCallback(e){if(e){if(process.throwDeprecation)throw e;else if(!process.noDeprecation){var t="fs: missing callback "+(e.stack||e.message);if(process.traceDeprecation)console.trace(t);else console.error(t)}}}}function maybeCallback(e){return typeof e==="function"?e:rethrow()}var u=i.normalize;if(n){var o=/(.*?)(?:[\/\\]+|$)/g}else{var o=/(.*?)(?:[\/]+|$)/g}if(n){var l=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/}else{var l=/^[\/]*/}t.realpathSync=function realpathSync(e,t){e=i.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return t[e]}var r=e,a={},u={};var f;var h;var c;var p;start();function start(){var t=l.exec(e);f=t[0].length;h=t[0];c=t[0];p="";if(n&&!u[c]){s.lstatSync(c);u[c]=true}}while(f<e.length){o.lastIndex=f;var d=o.exec(e);p=h;h+=d[0];c=p+d[1];f=o.lastIndex;if(u[c]||t&&t[c]===c){continue}var v;if(t&&Object.prototype.hasOwnProperty.call(t,c)){v=t[c]}else{var y=s.lstatSync(c);if(!y.isSymbolicLink()){u[c]=true;if(t)t[c]=c;continue}var g=null;if(!n){var b=y.dev.toString(32)+":"+y.ino.toString(32);if(a.hasOwnProperty(b)){g=a[b]}}if(g===null){s.statSync(c);g=s.readlinkSync(c)}v=i.resolve(p,g);if(t)t[c]=v;if(!n)a[b]=g}e=i.resolve(v,e.slice(f));start()}if(t)t[r]=e;return e};t.realpath=function realpath(e,t,r){if(typeof r!=="function"){r=maybeCallback(t);t=null}e=i.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return process.nextTick(r.bind(null,null,t[e]))}var a=e,u={},f={};var h;var c;var p;var d;start();function start(){var t=l.exec(e);h=t[0].length;c=t[0];p=t[0];d="";if(n&&!f[p]){s.lstat(p,function(e){if(e)return r(e);f[p]=true;LOOP()})}else{process.nextTick(LOOP)}}function LOOP(){if(h>=e.length){if(t)t[a]=e;return r(null,e)}o.lastIndex=h;var i=o.exec(e);d=c;c+=i[0];p=d+i[1];h=o.lastIndex;if(f[p]||t&&t[p]===p){return process.nextTick(LOOP)}if(t&&Object.prototype.hasOwnProperty.call(t,p)){return gotResolvedLink(t[p])}return s.lstat(p,gotStat)}function gotStat(e,i){if(e)return r(e);if(!i.isSymbolicLink()){f[p]=true;if(t)t[p]=p;return process.nextTick(LOOP)}if(!n){var a=i.dev.toString(32)+":"+i.ino.toString(32);if(u.hasOwnProperty(a)){return gotTarget(null,u[a],p)}}s.stat(p,function(e){if(e)return r(e);s.readlink(p,function(e,t){if(!n)u[a]=t;gotTarget(e,t)})})}function gotTarget(e,n,s){if(e)return r(e);var a=i.resolve(d,n);if(t)t[s]=a;gotResolvedLink(a)}function gotResolvedLink(t){e=i.resolve(t,e.slice(h));start()}}},564:function(e,t,r){var i=r(55);var n=r(572);n.core=i;n.isCore=function isCore(e){return i[e]};n.sync=r(377);t=n;e.exports=n},569:function(e,t,r){e.exports=r(688)},572:function(e,t,r){var i=r(55);var n=r(66);var s=r(589);var a=r(68);var u=r(807);var o=r(764);var l=function isFile(e,t){n.stat(e,function(e,r){if(!e){return t(null,r.isFile()||r.isFIFO())}if(e.code==="ENOENT"||e.code==="ENOTDIR")return t(null,false);return t(e)})};e.exports=function resolve(e,t,r){var f=r;var h=t;if(typeof t==="function"){f=h;h={}}if(typeof e!=="string"){var c=new TypeError("Path must be a string.");return process.nextTick(function(){f(c)})}h=o(e,h);var p=h.isFile||l;var d=h.readFile||n.readFile;var v=h.extensions||[".js"];var y=h.basedir||s.dirname(a());var g=h.filename||y;h.paths=h.paths||[];var b=s.resolve(y);if(h.preserveSymlinks===false){n.realpath(b,function(e,t){if(e&&e.code!=="ENOENT")f(c);else init(e?b:t)})}else{init(b)}var _;function init(t){if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){_=s.resolve(t,e);if(e===".."||e.slice(-1)==="/")_+="/";if(/\/$/.test(e)&&_===t){loadAsDirectory(_,h.package,onfile)}else loadAsFile(_,h.package,onfile)}else loadNodeModules(e,t,function(t,r,n){if(t)f(t);else if(r)f(null,r,n);else if(i[e])return f(null,e);else{var s=new Error("Cannot find module '"+e+"' from '"+g+"'");s.code="MODULE_NOT_FOUND";f(s)}})}function onfile(t,r,i){if(t)f(t);else if(r)f(null,r,i);else loadAsDirectory(_,function(t,r,i){if(t)f(t);else if(r)f(null,r,i);else{var n=new Error("Cannot find module '"+e+"' from '"+g+"'");n.code="MODULE_NOT_FOUND";f(n)}})}function loadAsFile(e,t,r){var i=t;var n=r;if(typeof i==="function"){n=i;i=undefined}var a=[""].concat(v);load(a,e,i);function load(e,t,r){if(e.length===0)return n(null,undefined,r);var i=t+e[0];var a=r;if(a)onpkg(null,a);else loadpkg(s.dirname(i),onpkg);function onpkg(r,u,o){a=u;if(r)return n(r);if(o&&a&&h.pathFilter){var l=s.relative(o,i);var f=l.slice(0,l.length-e[0].length);var c=h.pathFilter(a,t,f);if(c)return load([""].concat(v.slice()),s.resolve(o,c),a)}p(i,onex)}function onex(r,s){if(r)return n(r);if(s)return n(null,i,a);load(e.slice(1),t,a)}}}function loadpkg(e,t){if(e===""||e==="/")return t(null);if(process.platform==="win32"&&/^\w:[/\\]*$/.test(e)){return t(null)}if(/[/\\]node_modules[/\\]*$/.test(e))return t(null);var r=s.join(e,"package.json");p(r,function(i,n){if(!n)return loadpkg(s.dirname(e),t);d(r,function(i,n){if(i)t(i);try{var s=JSON.parse(n)}catch(e){}if(s&&h.packageFilter){s=h.packageFilter(s,r)}t(null,s,e)})})}function loadAsDirectory(e,t,r){var i=r;var n=t;if(typeof n==="function"){i=n;n=h.package}var a=s.join(e,"package.json");p(a,function(t,r){if(t)return i(t);if(!r)return loadAsFile(s.join(e,"index"),n,i);d(a,function(t,r){if(t)return i(t);try{var n=JSON.parse(r)}catch(e){}if(h.packageFilter){n=h.packageFilter(n,a)}if(n.main){if(typeof n.main!=="string"){var u=new TypeError("package “"+n.name+"” `main` must be a string");u.code="INVALID_PACKAGE_MAIN";return i(u)}if(n.main==="."||n.main==="./"){n.main="index"}loadAsFile(s.resolve(e,n.main),n,function(t,r,n){if(t)return i(t);if(r)return i(null,r,n);if(!n)return loadAsFile(s.join(e,"index"),n,i);var a=s.resolve(e,n.main);loadAsDirectory(a,n,function(t,r,n){if(t)return i(t);if(r)return i(null,r,n);loadAsFile(s.join(e,"index"),n,i)})});return}loadAsFile(s.join(e,"/index"),n,i)})})}function processDirs(t,r){if(r.length===0)return t(null,undefined);var i=r[0];var n=s.join(i,e);loadAsFile(n,h.package,onfile);function onfile(r,n,a){if(r)return t(r);if(n)return t(null,n,a);loadAsDirectory(s.join(i,e),h.package,ondir)}function ondir(e,i,n){if(e)return t(e);if(i)return t(null,i,n);processDirs(t,r.slice(1))}}function loadNodeModules(e,t,r){processDirs(r,u(t,h,e))}}},579:function(e,t){t=e.exports=SemVer;var r;if(typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)){r=function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER");console.log.apply(console,e)}}else{r=function(){}}t.SEMVER_SPEC_VERSION="2.0.0";var i=256;var n=Number.MAX_SAFE_INTEGER||9007199254740991;var s=16;var a=t.re=[];var u=t.src=[];var o=0;var l=o++;u[l]="0|[1-9]\\d*";var f=o++;u[f]="[0-9]+";var h=o++;u[h]="\\d*[a-zA-Z-][a-zA-Z0-9-]*";var c=o++;u[c]="("+u[l]+")\\."+"("+u[l]+")\\."+"("+u[l]+")";var p=o++;u[p]="("+u[f]+")\\."+"("+u[f]+")\\."+"("+u[f]+")";var d=o++;u[d]="(?:"+u[l]+"|"+u[h]+")";var v=o++;u[v]="(?:"+u[f]+"|"+u[h]+")";var y=o++;u[y]="(?:-("+u[d]+"(?:\\."+u[d]+")*))";var g=o++;u[g]="(?:-?("+u[v]+"(?:\\."+u[v]+")*))";var b=o++;u[b]="[0-9A-Za-z-]+";var _=o++;u[_]="(?:\\+("+u[b]+"(?:\\."+u[b]+")*))";var m=o++;var E="v?"+u[c]+u[y]+"?"+u[_]+"?";u[m]="^"+E+"$";var D="[v=\\s]*"+u[p]+u[g]+"?"+u[_]+"?";var w=o++;u[w]="^"+D+"$";var A=o++;u[A]="((?:<|>)?=?)";var C=o++;u[C]=u[f]+"|x|X|\\*";var x=o++;u[x]=u[l]+"|x|X|\\*";var S=o++;u[S]="[v=\\s]*("+u[x]+")"+"(?:\\.("+u[x]+")"+"(?:\\.("+u[x]+")"+"(?:"+u[y]+")?"+u[_]+"?"+")?)?";var k=o++;u[k]="[v=\\s]*("+u[C]+")"+"(?:\\.("+u[C]+")"+"(?:\\.("+u[C]+")"+"(?:"+u[g]+")?"+u[_]+"?"+")?)?";var F=o++;u[F]="^"+u[A]+"\\s*"+u[S]+"$";var R=o++;u[R]="^"+u[A]+"\\s*"+u[k]+"$";var T=o++;u[T]="(?:^|[^\\d])"+"(\\d{1,"+s+"})"+"(?:\\.(\\d{1,"+s+"}))?"+"(?:\\.(\\d{1,"+s+"}))?"+"(?:$|[^\\d])";var O=o++;u[O]="(?:~>?)";var I=o++;u[I]="(\\s*)"+u[O]+"\\s+";a[I]=new RegExp(u[I],"g");var B="$1~";var N=o++;u[N]="^"+u[O]+u[S]+"$";var L=o++;u[L]="^"+u[O]+u[k]+"$";var P=o++;u[P]="(?:\\^)";var j=o++;u[j]="(\\s*)"+u[P]+"\\s+";a[j]=new RegExp(u[j],"g");var q="$1^";var $=o++;u[$]="^"+u[P]+u[S]+"$";var M=o++;u[M]="^"+u[P]+u[k]+"$";var U=o++;u[U]="^"+u[A]+"\\s*("+D+")$|^$";var H=o++;u[H]="^"+u[A]+"\\s*("+E+")$|^$";var W=o++;u[W]="(\\s*)"+u[A]+"\\s*("+D+"|"+u[S]+")";a[W]=new RegExp(u[W],"g");var G="$1$2$3";var V=o++;u[V]="^\\s*("+u[S]+")"+"\\s+-\\s+"+"("+u[S]+")"+"\\s*$";var z=o++;u[z]="^\\s*("+u[k]+")"+"\\s+-\\s+"+"("+u[k]+")"+"\\s*$";var K=o++;u[K]="(<|>)?=?\\s*\\*";for(var Q=0;Q<o;Q++){r(Q,u[Q]);if(!a[Q]){a[Q]=new RegExp(u[Q])}}t.parse=parse;function parse(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof SemVer){return e}if(typeof e!=="string"){return null}if(e.length>i){return null}var r=t.loose?a[w]:a[m];if(!r.test(e)){return null}try{return new SemVer(e,t)}catch(e){return null}}t.valid=valid;function valid(e,t){var r=parse(e,t);return r?r.version:null}t.clean=clean;function clean(e,t){var r=parse(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}t.SemVer=SemVer;function SemVer(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof SemVer){if(e.loose===t.loose){return e}else{e=e.version}}else if(typeof e!=="string"){throw new TypeError("Invalid Version: "+e)}if(e.length>i){throw new TypeError("version is longer than "+i+" characters")}if(!(this instanceof SemVer)){return new SemVer(e,t)}r("SemVer",e,t);this.options=t;this.loose=!!t.loose;var s=e.trim().match(t.loose?a[w]:a[m]);if(!s){throw new TypeError("Invalid Version: "+e)}this.raw=e;this.major=+s[1];this.minor=+s[2];this.patch=+s[3];if(this.major>n||this.major<0){throw new TypeError("Invalid major version")}if(this.minor>n||this.minor<0){throw new TypeError("Invalid minor version")}if(this.patch>n||this.patch<0){throw new TypeError("Invalid patch version")}if(!s[4]){this.prerelease=[]}else{this.prerelease=s[4].split(".").map(function(e){if(/^[0-9]+$/.test(e)){var t=+e;if(t>=0&&t<n){return t}}return e})}this.build=s[5]?s[5].split("."):[];this.format()}SemVer.prototype.format=function(){this.version=this.major+"."+this.minor+"."+this.patch;if(this.prerelease.length){this.version+="-"+this.prerelease.join(".")}return this.version};SemVer.prototype.toString=function(){return this.version};SemVer.prototype.compare=function(e){r("SemVer.compare",this.version,this.options,e);if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}return this.compareMain(e)||this.comparePre(e)};SemVer.prototype.compareMain=function(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}return compareIdentifiers(this.major,e.major)||compareIdentifiers(this.minor,e.minor)||compareIdentifiers(this.patch,e.patch)};SemVer.prototype.comparePre=function(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}if(this.prerelease.length&&!e.prerelease.length){return-1}else if(!this.prerelease.length&&e.prerelease.length){return 1}else if(!this.prerelease.length&&!e.prerelease.length){return 0}var t=0;do{var i=this.prerelease[t];var n=e.prerelease[t];r("prerelease compare",t,i,n);if(i===undefined&&n===undefined){return 0}else if(n===undefined){return 1}else if(i===undefined){return-1}else if(i===n){continue}else{return compareIdentifiers(i,n)}}while(++t)};SemVer.prototype.inc=function(e,t){switch(e){case"premajor":this.prerelease.length=0;this.patch=0;this.minor=0;this.major++;this.inc("pre",t);break;case"preminor":this.prerelease.length=0;this.patch=0;this.minor++;this.inc("pre",t);break;case"prepatch":this.prerelease.length=0;this.inc("patch",t);this.inc("pre",t);break;case"prerelease":if(this.prerelease.length===0){this.inc("patch",t)}this.inc("pre",t);break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0){this.major++}this.minor=0;this.patch=0;this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0){this.minor++}this.patch=0;this.prerelease=[];break;case"patch":if(this.prerelease.length===0){this.patch++}this.prerelease=[];break;case"pre":if(this.prerelease.length===0){this.prerelease=[0]}else{var r=this.prerelease.length;while(--r>=0){if(typeof this.prerelease[r]==="number"){this.prerelease[r]++;r=-2}}if(r===-1){this.prerelease.push(0)}}if(t){if(this.prerelease[0]===t){if(isNaN(this.prerelease[1])){this.prerelease=[t,0]}}else{this.prerelease=[t,0]}}break;default:throw new Error("invalid increment argument: "+e)}this.format();this.raw=this.version;return this};t.inc=inc;function inc(e,t,r,i){if(typeof r==="string"){i=r;r=undefined}try{return new SemVer(e,r).inc(t,i).version}catch(e){return null}}t.diff=diff;function diff(e,t){if(eq(e,t)){return null}else{var r=parse(e);var i=parse(t);var n="";if(r.prerelease.length||i.prerelease.length){n="pre";var s="prerelease"}for(var a in r){if(a==="major"||a==="minor"||a==="patch"){if(r[a]!==i[a]){return n+a}}}return s}}t.compareIdentifiers=compareIdentifiers;var X=/^[0-9]+$/;function compareIdentifiers(e,t){var r=X.test(e);var i=X.test(t);if(r&&i){e=+e;t=+t}return e===t?0:r&&!i?-1:i&&!r?1:e<t?-1:1}t.rcompareIdentifiers=rcompareIdentifiers;function rcompareIdentifiers(e,t){return compareIdentifiers(t,e)}t.major=major;function major(e,t){return new SemVer(e,t).major}t.minor=minor;function minor(e,t){return new SemVer(e,t).minor}t.patch=patch;function patch(e,t){return new SemVer(e,t).patch}t.compare=compare;function compare(e,t,r){return new SemVer(e,r).compare(new SemVer(t,r))}t.compareLoose=compareLoose;function compareLoose(e,t){return compare(e,t,true)}t.rcompare=rcompare;function rcompare(e,t,r){return compare(t,e,r)}t.sort=sort;function sort(e,r){return e.sort(function(e,i){return t.compare(e,i,r)})}t.rsort=rsort;function rsort(e,r){return e.sort(function(e,i){return t.rcompare(e,i,r)})}t.gt=gt;function gt(e,t,r){return compare(e,t,r)>0}t.lt=lt;function lt(e,t,r){return compare(e,t,r)<0}t.eq=eq;function eq(e,t,r){return compare(e,t,r)===0}t.neq=neq;function neq(e,t,r){return compare(e,t,r)!==0}t.gte=gte;function gte(e,t,r){return compare(e,t,r)>=0}t.lte=lte;function lte(e,t,r){return compare(e,t,r)<=0}t.cmp=cmp;function cmp(e,t,r,i){switch(t){case"===":if(typeof e==="object")e=e.version;if(typeof r==="object")r=r.version;return e===r;case"!==":if(typeof e==="object")e=e.version;if(typeof r==="object")r=r.version;return e!==r;case"":case"=":case"==":return eq(e,r,i);case"!=":return neq(e,r,i);case">":return gt(e,r,i);case">=":return gte(e,r,i);case"<":return lt(e,r,i);case"<=":return lte(e,r,i);default:throw new TypeError("Invalid operator: "+t)}}t.Comparator=Comparator;function Comparator(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof Comparator){if(e.loose===!!t.loose){return e}else{e=e.value}}if(!(this instanceof Comparator)){return new Comparator(e,t)}r("comparator",e,t);this.options=t;this.loose=!!t.loose;this.parse(e);if(this.semver===Z){this.value=""}else{this.value=this.operator+this.semver.version}r("comp",this)}var Z={};Comparator.prototype.parse=function(e){var t=this.options.loose?a[U]:a[H];var r=e.match(t);if(!r){throw new TypeError("Invalid comparator: "+e)}this.operator=r[1];if(this.operator==="="){this.operator=""}if(!r[2]){this.semver=Z}else{this.semver=new SemVer(r[2],this.options.loose)}};Comparator.prototype.toString=function(){return this.value};Comparator.prototype.test=function(e){r("Comparator.test",e,this.options.loose);if(this.semver===Z){return true}if(typeof e==="string"){e=new SemVer(e,this.options)}return cmp(e,this.operator,this.semver,this.options)};Comparator.prototype.intersects=function(e,t){if(!(e instanceof Comparator)){throw new TypeError("a Comparator is required")}if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}var r;if(this.operator===""){r=new Range(e.value,t);return satisfies(this.value,r,t)}else if(e.operator===""){r=new Range(this.value,t);return satisfies(e.semver,r,t)}var i=(this.operator===">="||this.operator===">")&&(e.operator===">="||e.operator===">");var n=(this.operator==="<="||this.operator==="<")&&(e.operator==="<="||e.operator==="<");var s=this.semver.version===e.semver.version;var a=(this.operator===">="||this.operator==="<=")&&(e.operator===">="||e.operator==="<=");var u=cmp(this.semver,"<",e.semver,t)&&((this.operator===">="||this.operator===">")&&(e.operator==="<="||e.operator==="<"));var o=cmp(this.semver,">",e.semver,t)&&((this.operator==="<="||this.operator==="<")&&(e.operator===">="||e.operator===">"));return i||n||s&&a||u||o};t.Range=Range;function Range(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof Range){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease){return e}else{return new Range(e.raw,t)}}if(e instanceof Comparator){return new Range(e.value,t)}if(!(this instanceof Range)){return new Range(e,t)}this.options=t;this.loose=!!t.loose;this.includePrerelease=!!t.includePrerelease;this.raw=e;this.set=e.split(/\s*\|\|\s*/).map(function(e){return this.parseRange(e.trim())},this).filter(function(e){return e.length});if(!this.set.length){throw new TypeError("Invalid SemVer Range: "+e)}this.format()}Range.prototype.format=function(){this.range=this.set.map(function(e){return e.join(" ").trim()}).join("||").trim();return this.range};Range.prototype.toString=function(){return this.range};Range.prototype.parseRange=function(e){var t=this.options.loose;e=e.trim();var i=t?a[z]:a[V];e=e.replace(i,hyphenReplace);r("hyphen replace",e);e=e.replace(a[W],G);r("comparator trim",e,a[W]);e=e.replace(a[I],B);e=e.replace(a[j],q);e=e.split(/\s+/).join(" ");var n=t?a[U]:a[H];var s=e.split(" ").map(function(e){return parseComparator(e,this.options)},this).join(" ").split(/\s+/);if(this.options.loose){s=s.filter(function(e){return!!e.match(n)})}s=s.map(function(e){return new Comparator(e,this.options)},this);return s};Range.prototype.intersects=function(e,t){if(!(e instanceof Range)){throw new TypeError("a Range is required")}return this.set.some(function(r){return r.every(function(r){return e.set.some(function(e){return e.every(function(e){return r.intersects(e,t)})})})})};t.toComparators=toComparators;function toComparators(e,t){return new Range(e,t).set.map(function(e){return e.map(function(e){return e.value}).join(" ").trim().split(" ")})}function parseComparator(e,t){r("comp",e,t);e=replaceCarets(e,t);r("caret",e);e=replaceTildes(e,t);r("tildes",e);e=replaceXRanges(e,t);r("xrange",e);e=replaceStars(e,t);r("stars",e);return e}function isX(e){return!e||e.toLowerCase()==="x"||e==="*"}function replaceTildes(e,t){return e.trim().split(/\s+/).map(function(e){return replaceTilde(e,t)}).join(" ")}function replaceTilde(e,t){var i=t.loose?a[L]:a[N];return e.replace(i,function(t,i,n,s,a){r("tilde",e,t,i,n,s,a);var u;if(isX(i)){u=""}else if(isX(n)){u=">="+i+".0.0 <"+(+i+1)+".0.0"}else if(isX(s)){u=">="+i+"."+n+".0 <"+i+"."+(+n+1)+".0"}else if(a){r("replaceTilde pr",a);u=">="+i+"."+n+"."+s+"-"+a+" <"+i+"."+(+n+1)+".0"}else{u=">="+i+"."+n+"."+s+" <"+i+"."+(+n+1)+".0"}r("tilde return",u);return u})}function replaceCarets(e,t){return e.trim().split(/\s+/).map(function(e){return replaceCaret(e,t)}).join(" ")}function replaceCaret(e,t){r("caret",e,t);var i=t.loose?a[M]:a[$];return e.replace(i,function(t,i,n,s,a){r("caret",e,t,i,n,s,a);var u;if(isX(i)){u=""}else if(isX(n)){u=">="+i+".0.0 <"+(+i+1)+".0.0"}else if(isX(s)){if(i==="0"){u=">="+i+"."+n+".0 <"+i+"."+(+n+1)+".0"}else{u=">="+i+"."+n+".0 <"+(+i+1)+".0.0"}}else if(a){r("replaceCaret pr",a);if(i==="0"){if(n==="0"){u=">="+i+"."+n+"."+s+"-"+a+" <"+i+"."+n+"."+(+s+1)}else{u=">="+i+"."+n+"."+s+"-"+a+" <"+i+"."+(+n+1)+".0"}}else{u=">="+i+"."+n+"."+s+"-"+a+" <"+(+i+1)+".0.0"}}else{r("no pr");if(i==="0"){if(n==="0"){u=">="+i+"."+n+"."+s+" <"+i+"."+n+"."+(+s+1)}else{u=">="+i+"."+n+"."+s+" <"+i+"."+(+n+1)+".0"}}else{u=">="+i+"."+n+"."+s+" <"+(+i+1)+".0.0"}}r("caret return",u);return u})}function replaceXRanges(e,t){r("replaceXRanges",e,t);return e.split(/\s+/).map(function(e){return replaceXRange(e,t)}).join(" ")}function replaceXRange(e,t){e=e.trim();var i=t.loose?a[R]:a[F];return e.replace(i,function(t,i,n,s,a,u){r("xRange",e,t,i,n,s,a,u);var o=isX(n);var l=o||isX(s);var f=l||isX(a);var h=f;if(i==="="&&h){i=""}if(o){if(i===">"||i==="<"){t="<0.0.0"}else{t="*"}}else if(i&&h){if(l){s=0}a=0;if(i===">"){i=">=";if(l){n=+n+1;s=0;a=0}else{s=+s+1;a=0}}else if(i==="<="){i="<";if(l){n=+n+1}else{s=+s+1}}t=i+n+"."+s+"."+a}else if(l){t=">="+n+".0.0 <"+(+n+1)+".0.0"}else if(f){t=">="+n+"."+s+".0 <"+n+"."+(+s+1)+".0"}r("xRange return",t);return t})}function replaceStars(e,t){r("replaceStars",e,t);return e.trim().replace(a[K],"")}function hyphenReplace(e,t,r,i,n,s,a,u,o,l,f,h,c){if(isX(r)){t=""}else if(isX(i)){t=">="+r+".0.0"}else if(isX(n)){t=">="+r+"."+i+".0"}else{t=">="+t}if(isX(o)){u=""}else if(isX(l)){u="<"+(+o+1)+".0.0"}else if(isX(f)){u="<"+o+"."+(+l+1)+".0"}else if(h){u="<="+o+"."+l+"."+f+"-"+h}else{u="<="+u}return(t+" "+u).trim()}Range.prototype.test=function(e){if(!e){return false}if(typeof e==="string"){e=new SemVer(e,this.options)}for(var t=0;t<this.set.length;t++){if(testSet(this.set[t],e,this.options)){return true}}return false};function testSet(e,t,i){for(var n=0;n<e.length;n++){if(!e[n].test(t)){return false}}if(t.prerelease.length&&!i.includePrerelease){for(n=0;n<e.length;n++){r(e[n].semver);if(e[n].semver===Z){continue}if(e[n].semver.prerelease.length>0){var s=e[n].semver;if(s.major===t.major&&s.minor===t.minor&&s.patch===t.patch){return true}}}return false}return true}t.satisfies=satisfies;function satisfies(e,t,r){try{t=new Range(t,r)}catch(e){return false}return t.test(e)}t.maxSatisfying=maxSatisfying;function maxSatisfying(e,t,r){var i=null;var n=null;try{var s=new Range(t,r)}catch(e){return null}e.forEach(function(e){if(s.test(e)){if(!i||n.compare(e)===-1){i=e;n=new SemVer(i,r)}}});return i}t.minSatisfying=minSatisfying;function minSatisfying(e,t,r){var i=null;var n=null;try{var s=new Range(t,r)}catch(e){return null}e.forEach(function(e){if(s.test(e)){if(!i||n.compare(e)===1){i=e;n=new SemVer(i,r)}}});return i}t.minVersion=minVersion;function minVersion(e,t){e=new Range(e,t);var r=new SemVer("0.0.0");if(e.test(r)){return r}r=new SemVer("0.0.0-0");if(e.test(r)){return r}r=null;for(var i=0;i<e.set.length;++i){var n=e.set[i];n.forEach(function(e){var t=new SemVer(e.semver.version);switch(e.operator){case">":if(t.prerelease.length===0){t.patch++}else{t.prerelease.push(0)}t.raw=t.format();case"":case">=":if(!r||gt(r,t)){r=t}break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+e.operator)}})}if(r&&e.test(r)){return r}return null}t.validRange=validRange;function validRange(e,t){try{return new Range(e,t).range||"*"}catch(e){return null}}t.ltr=ltr;function ltr(e,t,r){return outside(e,t,"<",r)}t.gtr=gtr;function gtr(e,t,r){return outside(e,t,">",r)}t.outside=outside;function outside(e,t,r,i){e=new SemVer(e,i);t=new Range(t,i);var n,s,a,u,o;switch(r){case">":n=gt;s=lte;a=lt;u=">";o=">=";break;case"<":n=lt;s=gte;a=gt;u="<";o="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(satisfies(e,t,i)){return false}for(var l=0;l<t.set.length;++l){var f=t.set[l];var h=null;var c=null;f.forEach(function(e){if(e.semver===Z){e=new Comparator(">=0.0.0")}h=h||e;c=c||e;if(n(e.semver,h.semver,i)){h=e}else if(a(e.semver,c.semver,i)){c=e}});if(h.operator===u||h.operator===o){return false}if((!c.operator||c.operator===u)&&s(e,c.semver)){return false}else if(c.operator===o&&a(e,c.semver)){return false}}return true}t.prerelease=prerelease;function prerelease(e,t){var r=parse(e,t);return r&&r.prerelease.length?r.prerelease:null}t.intersects=intersects;function intersects(e,t,r){e=new Range(e,r);t=new Range(t,r);return e.intersects(t)}t.coerce=coerce;function coerce(e){if(e instanceof SemVer){return e}if(typeof e!=="string"){return null}var t=e.match(a[T]);if(t==null){return null}return parse(t[1]+"."+(t[2]||"0")+"."+(t[3]||"0"))}},589:function(e){e.exports=__webpack_require__(622)},590:function(e){e.exports={"0.1.14":{node_abi:null,v8:"1.3"},"0.1.15":{node_abi:null,v8:"1.3"},"0.1.16":{node_abi:null,v8:"1.3"},"0.1.17":{node_abi:null,v8:"1.3"},"0.1.18":{node_abi:null,v8:"1.3"},"0.1.19":{node_abi:null,v8:"2.0"},"0.1.20":{node_abi:null,v8:"2.0"},"0.1.21":{node_abi:null,v8:"2.0"},"0.1.22":{node_abi:null,v8:"2.0"},"0.1.23":{node_abi:null,v8:"2.0"},"0.1.24":{node_abi:null,v8:"2.0"},"0.1.25":{node_abi:null,v8:"2.0"},"0.1.26":{node_abi:null,v8:"2.0"},"0.1.27":{node_abi:null,v8:"2.1"},"0.1.28":{node_abi:null,v8:"2.1"},"0.1.29":{node_abi:null,v8:"2.1"},"0.1.30":{node_abi:null,v8:"2.1"},"0.1.31":{node_abi:null,v8:"2.1"},"0.1.32":{node_abi:null,v8:"2.1"},"0.1.33":{node_abi:null,v8:"2.1"},"0.1.90":{node_abi:null,v8:"2.2"},"0.1.91":{node_abi:null,v8:"2.2"},"0.1.92":{node_abi:null,v8:"2.2"},"0.1.93":{node_abi:null,v8:"2.2"},"0.1.94":{node_abi:null,v8:"2.2"},"0.1.95":{node_abi:null,v8:"2.2"},"0.1.96":{node_abi:null,v8:"2.2"},"0.1.97":{node_abi:null,v8:"2.2"},"0.1.98":{node_abi:null,v8:"2.2"},"0.1.99":{node_abi:null,v8:"2.2"},"0.1.100":{node_abi:null,v8:"2.2"},"0.1.101":{node_abi:null,v8:"2.3"},"0.1.102":{node_abi:null,v8:"2.3"},"0.1.103":{node_abi:null,v8:"2.3"},"0.1.104":{node_abi:null,v8:"2.3"},"0.2.0":{node_abi:1,v8:"2.3"},"0.2.1":{node_abi:1,v8:"2.3"},"0.2.2":{node_abi:1,v8:"2.3"},"0.2.3":{node_abi:1,v8:"2.3"},"0.2.4":{node_abi:1,v8:"2.3"},"0.2.5":{node_abi:1,v8:"2.3"},"0.2.6":{node_abi:1,v8:"2.3"},"0.3.0":{node_abi:1,v8:"2.5"},"0.3.1":{node_abi:1,v8:"2.5"},"0.3.2":{node_abi:1,v8:"3.0"},"0.3.3":{node_abi:1,v8:"3.0"},"0.3.4":{node_abi:1,v8:"3.0"},"0.3.5":{node_abi:1,v8:"3.0"},"0.3.6":{node_abi:1,v8:"3.0"},"0.3.7":{node_abi:1,v8:"3.0"},"0.3.8":{node_abi:1,v8:"3.1"},"0.4.0":{node_abi:1,v8:"3.1"},"0.4.1":{node_abi:1,v8:"3.1"},"0.4.2":{node_abi:1,v8:"3.1"},"0.4.3":{node_abi:1,v8:"3.1"},"0.4.4":{node_abi:1,v8:"3.1"},"0.4.5":{node_abi:1,v8:"3.1"},"0.4.6":{node_abi:1,v8:"3.1"},"0.4.7":{node_abi:1,v8:"3.1"},"0.4.8":{node_abi:1,v8:"3.1"},"0.4.9":{node_abi:1,v8:"3.1"},"0.4.10":{node_abi:1,v8:"3.1"},"0.4.11":{node_abi:1,v8:"3.1"},"0.4.12":{node_abi:1,v8:"3.1"},"0.5.0":{node_abi:1,v8:"3.1"},"0.5.1":{node_abi:1,v8:"3.4"},"0.5.2":{node_abi:1,v8:"3.4"},"0.5.3":{node_abi:1,v8:"3.4"},"0.5.4":{node_abi:1,v8:"3.5"},"0.5.5":{node_abi:1,v8:"3.5"},"0.5.6":{node_abi:1,v8:"3.6"},"0.5.7":{node_abi:1,v8:"3.6"},"0.5.8":{node_abi:1,v8:"3.6"},"0.5.9":{node_abi:1,v8:"3.6"},"0.5.10":{node_abi:1,v8:"3.7"},"0.6.0":{node_abi:1,v8:"3.6"},"0.6.1":{node_abi:1,v8:"3.6"},"0.6.2":{node_abi:1,v8:"3.6"},"0.6.3":{node_abi:1,v8:"3.6"},"0.6.4":{node_abi:1,v8:"3.6"},"0.6.5":{node_abi:1,v8:"3.6"},"0.6.6":{node_abi:1,v8:"3.6"},"0.6.7":{node_abi:1,v8:"3.6"},"0.6.8":{node_abi:1,v8:"3.6"},"0.6.9":{node_abi:1,v8:"3.6"},"0.6.10":{node_abi:1,v8:"3.6"},"0.6.11":{node_abi:1,v8:"3.6"},"0.6.12":{node_abi:1,v8:"3.6"},"0.6.13":{node_abi:1,v8:"3.6"},"0.6.14":{node_abi:1,v8:"3.6"},"0.6.15":{node_abi:1,v8:"3.6"},"0.6.16":{node_abi:1,v8:"3.6"},"0.6.17":{node_abi:1,v8:"3.6"},"0.6.18":{node_abi:1,v8:"3.6"},"0.6.19":{node_abi:1,v8:"3.6"},"0.6.20":{node_abi:1,v8:"3.6"},"0.6.21":{node_abi:1,v8:"3.6"},"0.7.0":{node_abi:1,v8:"3.8"},"0.7.1":{node_abi:1,v8:"3.8"},"0.7.2":{node_abi:1,v8:"3.8"},"0.7.3":{node_abi:1,v8:"3.9"},"0.7.4":{node_abi:1,v8:"3.9"},"0.7.5":{node_abi:1,v8:"3.9"},"0.7.6":{node_abi:1,v8:"3.9"},"0.7.7":{node_abi:1,v8:"3.9"},"0.7.8":{node_abi:1,v8:"3.9"},"0.7.9":{node_abi:1,v8:"3.11"},"0.7.10":{node_abi:1,v8:"3.9"},"0.7.11":{node_abi:1,v8:"3.11"},"0.7.12":{node_abi:1,v8:"3.11"},"0.8.0":{node_abi:1,v8:"3.11"},"0.8.1":{node_abi:1,v8:"3.11"},"0.8.2":{node_abi:1,v8:"3.11"},"0.8.3":{node_abi:1,v8:"3.11"},"0.8.4":{node_abi:1,v8:"3.11"},"0.8.5":{node_abi:1,v8:"3.11"},"0.8.6":{node_abi:1,v8:"3.11"},"0.8.7":{node_abi:1,v8:"3.11"},"0.8.8":{node_abi:1,v8:"3.11"},"0.8.9":{node_abi:1,v8:"3.11"},"0.8.10":{node_abi:1,v8:"3.11"},"0.8.11":{node_abi:1,v8:"3.11"},"0.8.12":{node_abi:1,v8:"3.11"},"0.8.13":{node_abi:1,v8:"3.11"},"0.8.14":{node_abi:1,v8:"3.11"},"0.8.15":{node_abi:1,v8:"3.11"},"0.8.16":{node_abi:1,v8:"3.11"},"0.8.17":{node_abi:1,v8:"3.11"},"0.8.18":{node_abi:1,v8:"3.11"},"0.8.19":{node_abi:1,v8:"3.11"},"0.8.20":{node_abi:1,v8:"3.11"},"0.8.21":{node_abi:1,v8:"3.11"},"0.8.22":{node_abi:1,v8:"3.11"},"0.8.23":{node_abi:1,v8:"3.11"},"0.8.24":{node_abi:1,v8:"3.11"},"0.8.25":{node_abi:1,v8:"3.11"},"0.8.26":{node_abi:1,v8:"3.11"},"0.8.27":{node_abi:1,v8:"3.11"},"0.8.28":{node_abi:1,v8:"3.11"},"0.9.0":{node_abi:1,v8:"3.11"},"0.9.1":{node_abi:10,v8:"3.11"},"0.9.2":{node_abi:10,v8:"3.11"},"0.9.3":{node_abi:10,v8:"3.13"},"0.9.4":{node_abi:10,v8:"3.13"},"0.9.5":{node_abi:10,v8:"3.13"},"0.9.6":{node_abi:10,v8:"3.15"},"0.9.7":{node_abi:10,v8:"3.15"},"0.9.8":{node_abi:10,v8:"3.15"},"0.9.9":{node_abi:11,v8:"3.15"},"0.9.10":{node_abi:11,v8:"3.15"},"0.9.11":{node_abi:11,v8:"3.14"},"0.9.12":{node_abi:11,v8:"3.14"},"0.10.0":{node_abi:11,v8:"3.14"},"0.10.1":{node_abi:11,v8:"3.14"},"0.10.2":{node_abi:11,v8:"3.14"},"0.10.3":{node_abi:11,v8:"3.14"},"0.10.4":{node_abi:11,v8:"3.14"},"0.10.5":{node_abi:11,v8:"3.14"},"0.10.6":{node_abi:11,v8:"3.14"},"0.10.7":{node_abi:11,v8:"3.14"},"0.10.8":{node_abi:11,v8:"3.14"},"0.10.9":{node_abi:11,v8:"3.14"},"0.10.10":{node_abi:11,v8:"3.14"},"0.10.11":{node_abi:11,v8:"3.14"},"0.10.12":{node_abi:11,v8:"3.14"},"0.10.13":{node_abi:11,v8:"3.14"},"0.10.14":{node_abi:11,v8:"3.14"},"0.10.15":{node_abi:11,v8:"3.14"},"0.10.16":{node_abi:11,v8:"3.14"},"0.10.17":{node_abi:11,v8:"3.14"},"0.10.18":{node_abi:11,v8:"3.14"},"0.10.19":{node_abi:11,v8:"3.14"},"0.10.20":{node_abi:11,v8:"3.14"},"0.10.21":{node_abi:11,v8:"3.14"},"0.10.22":{node_abi:11,v8:"3.14"},"0.10.23":{node_abi:11,v8:"3.14"},"0.10.24":{node_abi:11,v8:"3.14"},"0.10.25":{node_abi:11,v8:"3.14"},"0.10.26":{node_abi:11,v8:"3.14"},"0.10.27":{node_abi:11,v8:"3.14"},"0.10.28":{node_abi:11,v8:"3.14"},"0.10.29":{node_abi:11,v8:"3.14"},"0.10.30":{node_abi:11,v8:"3.14"},"0.10.31":{node_abi:11,v8:"3.14"},"0.10.32":{node_abi:11,v8:"3.14"},"0.10.33":{node_abi:11,v8:"3.14"},"0.10.34":{node_abi:11,v8:"3.14"},"0.10.35":{node_abi:11,v8:"3.14"},"0.10.36":{node_abi:11,v8:"3.14"},"0.10.37":{node_abi:11,v8:"3.14"},"0.10.38":{node_abi:11,v8:"3.14"},"0.10.39":{node_abi:11,v8:"3.14"},"0.10.40":{node_abi:11,v8:"3.14"},"0.10.41":{node_abi:11,v8:"3.14"},"0.10.42":{node_abi:11,v8:"3.14"},"0.10.43":{node_abi:11,v8:"3.14"},"0.10.44":{node_abi:11,v8:"3.14"},"0.10.45":{node_abi:11,v8:"3.14"},"0.10.46":{node_abi:11,v8:"3.14"},"0.10.47":{node_abi:11,v8:"3.14"},"0.10.48":{node_abi:11,v8:"3.14"},"0.11.0":{node_abi:12,v8:"3.17"},"0.11.1":{node_abi:12,v8:"3.18"},"0.11.2":{node_abi:12,v8:"3.19"},"0.11.3":{node_abi:12,v8:"3.19"},"0.11.4":{node_abi:12,v8:"3.20"},"0.11.5":{node_abi:12,v8:"3.20"},"0.11.6":{node_abi:12,v8:"3.20"},"0.11.7":{node_abi:12,v8:"3.20"},"0.11.8":{node_abi:13,v8:"3.21"},"0.11.9":{node_abi:13,v8:"3.22"},"0.11.10":{node_abi:13,v8:"3.22"},"0.11.11":{node_abi:14,v8:"3.22"},"0.11.12":{node_abi:14,v8:"3.22"},"0.11.13":{node_abi:14,v8:"3.25"},"0.11.14":{node_abi:14,v8:"3.26"},"0.11.15":{node_abi:14,v8:"3.28"},"0.11.16":{node_abi:14,v8:"3.28"},"0.12.0":{node_abi:14,v8:"3.28"},"0.12.1":{node_abi:14,v8:"3.28"},"0.12.2":{node_abi:14,v8:"3.28"},"0.12.3":{node_abi:14,v8:"3.28"},"0.12.4":{node_abi:14,v8:"3.28"},"0.12.5":{node_abi:14,v8:"3.28"},"0.12.6":{node_abi:14,v8:"3.28"},"0.12.7":{node_abi:14,v8:"3.28"},"0.12.8":{node_abi:14,v8:"3.28"},"0.12.9":{node_abi:14,v8:"3.28"},"0.12.10":{node_abi:14,v8:"3.28"},"0.12.11":{node_abi:14,v8:"3.28"},"0.12.12":{node_abi:14,v8:"3.28"},"0.12.13":{node_abi:14,v8:"3.28"},"0.12.14":{node_abi:14,v8:"3.28"},"0.12.15":{node_abi:14,v8:"3.28"},"0.12.16":{node_abi:14,v8:"3.28"},"0.12.17":{node_abi:14,v8:"3.28"},"0.12.18":{node_abi:14,v8:"3.28"},"1.0.0":{node_abi:42,v8:"3.31"},"1.0.1":{node_abi:42,v8:"3.31"},"1.0.2":{node_abi:42,v8:"3.31"},"1.0.3":{node_abi:42,v8:"4.1"},"1.0.4":{node_abi:42,v8:"4.1"},"1.1.0":{node_abi:43,v8:"4.1"},"1.2.0":{node_abi:43,v8:"4.1"},"1.3.0":{node_abi:43,v8:"4.1"},"1.4.1":{node_abi:43,v8:"4.1"},"1.4.2":{node_abi:43,v8:"4.1"},"1.4.3":{node_abi:43,v8:"4.1"},"1.5.0":{node_abi:43,v8:"4.1"},"1.5.1":{node_abi:43,v8:"4.1"},"1.6.0":{node_abi:43,v8:"4.1"},"1.6.1":{node_abi:43,v8:"4.1"},"1.6.2":{node_abi:43,v8:"4.1"},"1.6.3":{node_abi:43,v8:"4.1"},"1.6.4":{node_abi:43,v8:"4.1"},"1.7.1":{node_abi:43,v8:"4.1"},"1.8.1":{node_abi:43,v8:"4.1"},"1.8.2":{node_abi:43,v8:"4.1"},"1.8.3":{node_abi:43,v8:"4.1"},"1.8.4":{node_abi:43,v8:"4.1"},"2.0.0":{node_abi:44,v8:"4.2"},"2.0.1":{node_abi:44,v8:"4.2"},"2.0.2":{node_abi:44,v8:"4.2"},"2.1.0":{node_abi:44,v8:"4.2"},"2.2.0":{node_abi:44,v8:"4.2"},"2.2.1":{node_abi:44,v8:"4.2"},"2.3.0":{node_abi:44,v8:"4.2"},"2.3.1":{node_abi:44,v8:"4.2"},"2.3.2":{node_abi:44,v8:"4.2"},"2.3.3":{node_abi:44,v8:"4.2"},"2.3.4":{node_abi:44,v8:"4.2"},"2.4.0":{node_abi:44,v8:"4.2"},"2.5.0":{node_abi:44,v8:"4.2"},"3.0.0":{node_abi:45,v8:"4.4"},"3.1.0":{node_abi:45,v8:"4.4"},"3.2.0":{node_abi:45,v8:"4.4"},"3.3.0":{node_abi:45,v8:"4.4"},"3.3.1":{node_abi:45,v8:"4.4"},"4.0.0":{node_abi:46,v8:"4.5"},"4.1.0":{node_abi:46,v8:"4.5"},"4.1.1":{node_abi:46,v8:"4.5"},"4.1.2":{node_abi:46,v8:"4.5"},"4.2.0":{node_abi:46,v8:"4.5"},"4.2.1":{node_abi:46,v8:"4.5"},"4.2.2":{node_abi:46,v8:"4.5"},"4.2.3":{node_abi:46,v8:"4.5"},"4.2.4":{node_abi:46,v8:"4.5"},"4.2.5":{node_abi:46,v8:"4.5"},"4.2.6":{node_abi:46,v8:"4.5"},"4.3.0":{node_abi:46,v8:"4.5"},"4.3.1":{node_abi:46,v8:"4.5"},"4.3.2":{node_abi:46,v8:"4.5"},"4.4.0":{node_abi:46,v8:"4.5"},"4.4.1":{node_abi:46,v8:"4.5"},"4.4.2":{node_abi:46,v8:"4.5"},"4.4.3":{node_abi:46,v8:"4.5"},"4.4.4":{node_abi:46,v8:"4.5"},"4.4.5":{node_abi:46,v8:"4.5"},"4.4.6":{node_abi:46,v8:"4.5"},"4.4.7":{node_abi:46,v8:"4.5"},"4.5.0":{node_abi:46,v8:"4.5"},"4.6.0":{node_abi:46,v8:"4.5"},"4.6.1":{node_abi:46,v8:"4.5"},"4.6.2":{node_abi:46,v8:"4.5"},"4.7.0":{node_abi:46,v8:"4.5"},"4.7.1":{node_abi:46,v8:"4.5"},"4.7.2":{node_abi:46,v8:"4.5"},"4.7.3":{node_abi:46,v8:"4.5"},"4.8.0":{node_abi:46,v8:"4.5"},"4.8.1":{node_abi:46,v8:"4.5"},"4.8.2":{node_abi:46,v8:"4.5"},"4.8.3":{node_abi:46,v8:"4.5"},"4.8.4":{node_abi:46,v8:"4.5"},"4.8.5":{node_abi:46,v8:"4.5"},"4.8.6":{node_abi:46,v8:"4.5"},"4.8.7":{node_abi:46,v8:"4.5"},"4.9.0":{node_abi:46,v8:"4.5"},"4.9.1":{node_abi:46,v8:"4.5"},"5.0.0":{node_abi:47,v8:"4.6"},"5.1.0":{node_abi:47,v8:"4.6"},"5.1.1":{node_abi:47,v8:"4.6"},"5.2.0":{node_abi:47,v8:"4.6"},"5.3.0":{node_abi:47,v8:"4.6"},"5.4.0":{node_abi:47,v8:"4.6"},"5.4.1":{node_abi:47,v8:"4.6"},"5.5.0":{node_abi:47,v8:"4.6"},"5.6.0":{node_abi:47,v8:"4.6"},"5.7.0":{node_abi:47,v8:"4.6"},"5.7.1":{node_abi:47,v8:"4.6"},"5.8.0":{node_abi:47,v8:"4.6"},"5.9.0":{node_abi:47,v8:"4.6"},"5.9.1":{node_abi:47,v8:"4.6"},"5.10.0":{node_abi:47,v8:"4.6"},"5.10.1":{node_abi:47,v8:"4.6"},"5.11.0":{node_abi:47,v8:"4.6"},"5.11.1":{node_abi:47,v8:"4.6"},"5.12.0":{node_abi:47,v8:"4.6"},"6.0.0":{node_abi:48,v8:"5.0"},"6.1.0":{node_abi:48,v8:"5.0"},"6.2.0":{node_abi:48,v8:"5.0"},"6.2.1":{node_abi:48,v8:"5.0"},"6.2.2":{node_abi:48,v8:"5.0"},"6.3.0":{node_abi:48,v8:"5.0"},"6.3.1":{node_abi:48,v8:"5.0"},"6.4.0":{node_abi:48,v8:"5.0"},"6.5.0":{node_abi:48,v8:"5.1"},"6.6.0":{node_abi:48,v8:"5.1"},"6.7.0":{node_abi:48,v8:"5.1"},"6.8.0":{node_abi:48,v8:"5.1"},"6.8.1":{node_abi:48,v8:"5.1"},"6.9.0":{node_abi:48,v8:"5.1"},"6.9.1":{node_abi:48,v8:"5.1"},"6.9.2":{node_abi:48,v8:"5.1"},"6.9.3":{node_abi:48,v8:"5.1"},"6.9.4":{node_abi:48,v8:"5.1"},"6.9.5":{node_abi:48,v8:"5.1"},"6.10.0":{node_abi:48,v8:"5.1"},"6.10.1":{node_abi:48,v8:"5.1"},"6.10.2":{node_abi:48,v8:"5.1"},"6.10.3":{node_abi:48,v8:"5.1"},"6.11.0":{node_abi:48,v8:"5.1"},"6.11.1":{node_abi:48,v8:"5.1"},"6.11.2":{node_abi:48,v8:"5.1"},"6.11.3":{node_abi:48,v8:"5.1"},"6.11.4":{node_abi:48,v8:"5.1"},"6.11.5":{node_abi:48,v8:"5.1"},"6.12.0":{node_abi:48,v8:"5.1"},"6.12.1":{node_abi:48,v8:"5.1"},"6.12.2":{node_abi:48,v8:"5.1"},"6.12.3":{node_abi:48,v8:"5.1"},"6.13.0":{node_abi:48,v8:"5.1"},"6.13.1":{node_abi:48,v8:"5.1"},"6.14.0":{node_abi:48,v8:"5.1"},"6.14.1":{node_abi:48,v8:"5.1"},"6.14.2":{node_abi:48,v8:"5.1"},"6.14.3":{node_abi:48,v8:"5.1"},"6.14.4":{node_abi:48,v8:"5.1"},"7.0.0":{node_abi:51,v8:"5.4"},"7.1.0":{node_abi:51,v8:"5.4"},"7.2.0":{node_abi:51,v8:"5.4"},"7.2.1":{node_abi:51,v8:"5.4"},"7.3.0":{node_abi:51,v8:"5.4"},"7.4.0":{node_abi:51,v8:"5.4"},"7.5.0":{node_abi:51,v8:"5.4"},"7.6.0":{node_abi:51,v8:"5.5"},"7.7.0":{node_abi:51,v8:"5.5"},"7.7.1":{node_abi:51,v8:"5.5"},"7.7.2":{node_abi:51,v8:"5.5"},"7.7.3":{node_abi:51,v8:"5.5"},"7.7.4":{node_abi:51,v8:"5.5"},"7.8.0":{node_abi:51,v8:"5.5"},"7.9.0":{node_abi:51,v8:"5.5"},"7.10.0":{node_abi:51,v8:"5.5"},"7.10.1":{node_abi:51,v8:"5.5"},"8.0.0":{node_abi:57,v8:"5.8"},"8.1.0":{node_abi:57,v8:"5.8"},"8.1.1":{node_abi:57,v8:"5.8"},"8.1.2":{node_abi:57,v8:"5.8"},"8.1.3":{node_abi:57,v8:"5.8"},"8.1.4":{node_abi:57,v8:"5.8"},"8.2.0":{node_abi:57,v8:"5.8"},"8.2.1":{node_abi:57,v8:"5.8"},"8.3.0":{node_abi:57,v8:"6.0"},"8.4.0":{node_abi:57,v8:"6.0"},"8.5.0":{node_abi:57,v8:"6.0"},"8.6.0":{node_abi:57,v8:"6.0"},"8.7.0":{node_abi:57,v8:"6.1"},"8.8.0":{node_abi:57,v8:"6.1"},"8.8.1":{node_abi:57,v8:"6.1"},"8.9.0":{node_abi:57,v8:"6.1"},"8.9.1":{node_abi:57,v8:"6.1"},"8.9.2":{node_abi:57,v8:"6.1"},"8.9.3":{node_abi:57,v8:"6.1"},"8.9.4":{node_abi:57,v8:"6.1"},"8.10.0":{node_abi:57,v8:"6.2"},"8.11.0":{node_abi:57,v8:"6.2"},"8.11.1":{node_abi:57,v8:"6.2"},"8.11.2":{node_abi:57,v8:"6.2"},"8.11.3":{node_abi:57,v8:"6.2"},"8.11.4":{node_abi:57,v8:"6.2"},"8.12.0":{node_abi:57,v8:"6.2"},"9.0.0":{node_abi:59,v8:"6.2"},"9.1.0":{node_abi:59,v8:"6.2"},"9.2.0":{node_abi:59,v8:"6.2"},"9.2.1":{node_abi:59,v8:"6.2"},"9.3.0":{node_abi:59,v8:"6.2"},"9.4.0":{node_abi:59,v8:"6.2"},"9.5.0":{node_abi:59,v8:"6.2"},"9.6.0":{node_abi:59,v8:"6.2"},"9.6.1":{node_abi:59,v8:"6.2"},"9.7.0":{node_abi:59,v8:"6.2"},"9.7.1":{node_abi:59,v8:"6.2"},"9.8.0":{node_abi:59,v8:"6.2"},"9.9.0":{node_abi:59,v8:"6.2"},"9.10.0":{node_abi:59,v8:"6.2"},"9.10.1":{node_abi:59,v8:"6.2"},"9.11.0":{node_abi:59,v8:"6.2"},"9.11.1":{node_abi:59,v8:"6.2"},"9.11.2":{node_abi:59,v8:"6.2"},"10.0.0":{node_abi:64,v8:"6.6"},"10.1.0":{node_abi:64,v8:"6.6"},"10.2.0":{node_abi:64,v8:"6.6"},"10.2.1":{node_abi:64,v8:"6.6"},"10.3.0":{node_abi:64,v8:"6.6"},"10.4.0":{node_abi:64,v8:"6.7"},"10.4.1":{node_abi:64,v8:"6.7"},"10.5.0":{node_abi:64,v8:"6.7"},"10.6.0":{node_abi:64,v8:"6.7"},"10.7.0":{node_abi:64,v8:"6.7"},"10.8.0":{node_abi:64,v8:"6.7"},"10.9.0":{node_abi:64,v8:"6.8"},"10.10.0":{node_abi:64,v8:"6.8"},"10.11.0":{node_abi:64,v8:"6.8"},"10.12.0":{node_abi:64,v8:"6.8"},"10.13.0":{node_abi:64,v8:"6.8"},"11.0.0":{node_abi:67,v8:"7.0"},"11.1.0":{node_abi:67,v8:"7.0"}}},594:function(module,__unusedexports,__nested_webpack_require_466019__){var fs=__nested_webpack_require_466019__(66);var path=__nested_webpack_require_466019__(589);var os=__nested_webpack_require_466019__(431);var runtimeRequire=true?eval("require"):0;var vars=process.config&&process.config.variables||{};var prebuildsOnly=!!process.env.PREBUILDS_ONLY;var abi=process.versions.modules;var runtime=isElectron()?"electron":"node";var arch=os.arch();var platform=os.platform();var libc=process.env.LIBC||(isAlpine(platform)?"musl":"glibc");var armv=process.env.ARM_VERSION||(arch==="arm64"?"8":vars.arm_version)||"";var uv=(process.versions.uv||"").split(".")[0];module.exports=load;function load(e){return runtimeRequire(load.path(e))}load.path=function(e){e=path.resolve(e||".");try{var t=runtimeRequire(path.join(e,"package.json")).name.toUpperCase().replace(/-/g,"_");if(process.env[t+"_PREBUILD"])e=process.env[t+"_PREBUILD"]}catch(e){}if(!prebuildsOnly){var r=getFirst(path.join(e,"build/Release"),matchBuild);if(r)return r;var i=getFirst(path.join(e,"build/Debug"),matchBuild);if(i)return i}var n=resolve(e);if(n)return n;var s=resolve(path.dirname(process.execPath));if(s)return s;var a=["platform="+platform,"arch="+arch,"runtime="+runtime,"abi="+abi,"uv="+uv,armv?"armv="+armv:"","libc="+libc].filter(Boolean).join(" ");throw new Error("No native build was found for "+a);function resolve(e){var t=path.join(e,"prebuilds",platform+"-"+arch);var r=readdirSync(t).map(parseTags);var i=r.filter(matchTags(runtime,abi));var n=i.sort(compareTags(runtime))[0];if(n)return path.join(t,n.file)}};function readdirSync(e){try{return fs.readdirSync(e)}catch(e){return[]}}function getFirst(e,t){var r=readdirSync(e).filter(t);return r[0]&&path.join(e,r[0])}function matchBuild(e){return/\.node$/.test(e)}function parseTags(e){var t=e.split(".");var r=t.pop();var i={file:e,specificity:0};if(r!=="node")return;for(var n=0;n<t.length;n++){var s=t[n];if(s==="node"||s==="electron"||s==="node-webkit"){i.runtime=s}else if(s==="napi"){i.napi=true}else if(s.slice(0,3)==="abi"){i.abi=s.slice(3)}else if(s.slice(0,2)==="uv"){i.uv=s.slice(2)}else if(s.slice(0,4)==="armv"){i.armv=s.slice(4)}else if(s==="glibc"||s==="musl"){i.libc=s}else{continue}i.specificity++}return i}function matchTags(e,t){return function(r){if(r==null)return false;if(r.runtime!==e&&!runtimeAgnostic(r))return false;if(r.abi!==t&&!r.napi)return false;if(r.uv&&r.uv!==uv)return false;if(r.armv&&r.armv!==armv)return false;if(r.libc&&r.libc!==libc)return false;return true}}function runtimeAgnostic(e){return e.runtime==="node"&&e.napi}function compareTags(e){return function(t,r){if(t.runtime!==r.runtime){return t.runtime===e?-1:1}else if(t.abi!==r.abi){return t.abi?-1:1}else if(t.specificity!==r.specificity){return t.specificity>r.specificity?-1:1}else{return 0}}}function isElectron(){if(process.versions&&process.versions.electron)return true;if(process.env.ELECTRON_RUN_AS_NODE)return true;return typeof window!=="undefined"&&window.process&&window.process.type==="renderer"}function isAlpine(e){return e==="linux"&&fs.existsSync("/etc/alpine-release")}load.parseTags=parseTags;load.matchTags=matchTags;load.compareTags=compareTags},597:function(e,t,r){var i=r(688).Stream;e.exports=legacy;function legacy(e){return{ReadStream:ReadStream,WriteStream:WriteStream};function ReadStream(t,r){if(!(this instanceof ReadStream))return new ReadStream(t,r);i.call(this);var n=this;this.path=t;this.fd=null;this.readable=true;this.paused=false;this.flags="r";this.mode=438;this.bufferSize=64*1024;r=r||{};var s=Object.keys(r);for(var a=0,u=s.length;a<u;a++){var o=s[a];this[o]=r[o]}if(this.encoding)this.setEncoding(this.encoding);if(this.start!==undefined){if("number"!==typeof this.start){throw TypeError("start must be a Number")}if(this.end===undefined){this.end=Infinity}else if("number"!==typeof this.end){throw TypeError("end must be a Number")}if(this.start>this.end){throw new Error("start must be <= end")}this.pos=this.start}if(this.fd!==null){process.nextTick(function(){n._read()});return}e.open(this.path,this.flags,this.mode,function(e,t){if(e){n.emit("error",e);n.readable=false;return}n.fd=t;n.emit("open",t);n._read()})}function WriteStream(t,r){if(!(this instanceof WriteStream))return new WriteStream(t,r);i.call(this);this.path=t;this.fd=null;this.writable=true;this.flags="w";this.encoding="binary";this.mode=438;this.bytesWritten=0;r=r||{};var n=Object.keys(r);for(var s=0,a=n.length;s<a;s++){var u=n[s];this[u]=r[u]}if(this.start!==undefined){if("number"!==typeof this.start){throw TypeError("start must be a Number")}if(this.start<0){throw new Error("start must be >= zero")}this.pos=this.start}this.busy=false;this._queue=[];if(this.fd===null){this._open=e.open;this._queue.push([this._open,this.path,this.flags,this.mode,undefined]);this.flush()}}}},604:function(e,t,r){"use strict";var i=r(64);var n=r(663);var s=r(358);var a=r(30);var u=e.exports=function(e){n.call(this,e);this.parentGroup=null;this.trackers=[];this.completion={};this.weight={};this.totalWeight=0;this.finished=false;this.bubbleChange=bubbleChange(this)};i.inherits(u,n);function bubbleChange(e){return function(t,r,i){e.completion[i.id]=r;if(e.finished)return;e.emit("change",t||e.name,e.completed(),e)}}u.prototype.nameInTree=function(){var e=[];var t=this;while(t){e.unshift(t.name);t=t.parentGroup}return e.join("/")};u.prototype.addUnit=function(e,t){if(e.addUnit){var r=this;while(r){if(e===r){throw new Error("Attempted to add tracker group "+e.name+" to tree that already includes it "+this.nameInTree(this))}r=r.parentGroup}e.parentGroup=this}this.weight[e.id]=t||1;this.totalWeight+=this.weight[e.id];this.trackers.push(e);this.completion[e.id]=e.completed();e.on("change",this.bubbleChange);if(!this.finished)this.emit("change",e.name,this.completion[e.id],e);return e};u.prototype.completed=function(){if(this.trackers.length===0)return 0;var e=1/this.totalWeight;var t=0;for(var r=0;r<this.trackers.length;r++){var i=this.trackers[r].id;t+=e*this.weight[i]*this.completion[i]}return t};u.prototype.newGroup=function(e,t){return this.addUnit(new u(e),t)};u.prototype.newItem=function(e,t,r){return this.addUnit(new s(e,t),r)};u.prototype.newStream=function(e,t,r){return this.addUnit(new a(e,t),r)};u.prototype.finish=function(){this.finished=true;if(!this.trackers.length)this.addUnit(new s,1,true);for(var e=0;e<this.trackers.length;e++){var t=this.trackers[e];t.finish();t.removeListener("change",this.bubbleChange)}this.emit("change",this.name,1,this)};var o="                                  ";u.prototype.debug=function(e){e=e||0;var t=e?o.substr(0,e):"";var r=t+(this.name||"top")+": "+this.completed()+"\n";this.trackers.forEach(function(i){if(i instanceof u){r+=i.debug(e+1)}else{r+=t+" "+i.name+": "+i.completed()+"\n"}});return r}},606:function(e){"use strict";e.exports=(e=>{if(Number.isNaN(e)){return false}if(e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141)){return true}return false})},607:function(e){e.exports=["🀄","🃏","🅰","🅱","🅾","🅿","🆎","🆑","🆒","🆓","🆔","🆕","🆖","🆗","🆘","🆙","🆚","🇦🇨","🇦🇩","🇦🇪","🇦🇫","🇦🇬","🇦🇮","🇦🇱","🇦🇲","🇦🇴","🇦🇶","🇦🇷","🇦🇸","🇦🇹","🇦🇺","🇦🇼","🇦🇽","🇦🇿","🇦","🇧🇦","🇧🇧","🇧🇩","🇧🇪","🇧🇫","🇧🇬","🇧🇭","🇧🇮","🇧🇯","🇧🇱","🇧🇲","🇧🇳","🇧🇴","🇧🇶","🇧🇷","🇧🇸","🇧🇹","🇧🇻","🇧🇼","🇧🇾","🇧🇿","🇧","🇨🇦","🇨🇨","🇨🇩","🇨🇫","🇨🇬","🇨🇭","🇨🇮","🇨🇰","🇨🇱","🇨🇲","🇨🇳","🇨🇴","🇨🇵","🇨🇷","🇨🇺","🇨🇻","🇨🇼","🇨🇽","🇨🇾","🇨🇿","🇨","🇩🇪","🇩🇬","🇩🇯","🇩🇰","🇩🇲","🇩🇴","🇩🇿","🇩","🇪🇦","🇪🇨","🇪🇪","🇪🇬","🇪🇭","🇪🇷","🇪🇸","🇪🇹","🇪🇺","🇪","🇫🇮","🇫🇯","🇫🇰","🇫🇲","🇫🇴","🇫🇷","🇫","🇬🇦","🇬🇧","🇬🇩","🇬🇪","🇬🇫","🇬🇬","🇬🇭","🇬🇮","🇬🇱","🇬🇲","🇬🇳","🇬🇵","🇬🇶","🇬🇷","🇬🇸","🇬🇹","🇬🇺","🇬🇼","🇬🇾","🇬","🇭🇰","🇭🇲","🇭🇳","🇭🇷","🇭🇹","🇭🇺","🇭","🇮🇨","🇮🇩","🇮🇪","🇮🇱","🇮🇲","🇮🇳","🇮🇴","🇮🇶","🇮🇷","🇮🇸","🇮🇹","🇮","🇯🇪","🇯🇲","🇯🇴","🇯🇵","🇯","🇰🇪","🇰🇬","🇰🇭","🇰🇮","🇰🇲","🇰🇳","🇰🇵","🇰🇷","🇰🇼","🇰🇾","🇰🇿","🇰","🇱🇦","🇱🇧","🇱🇨","🇱🇮","🇱🇰","🇱🇷","🇱🇸","🇱🇹","🇱🇺","🇱🇻","🇱🇾","🇱","🇲🇦","🇲🇨","🇲🇩","🇲🇪","🇲🇫","🇲🇬","🇲🇭","🇲🇰","🇲🇱","🇲🇲","🇲🇳","🇲🇴","🇲🇵","🇲🇶","🇲🇷","🇲🇸","🇲🇹","🇲🇺","🇲🇻","🇲🇼","🇲🇽","🇲🇾","🇲🇿","🇲","🇳🇦","🇳🇨","🇳🇪","🇳🇫","🇳🇬","🇳🇮","🇳🇱","🇳🇴","🇳🇵","🇳🇷","🇳🇺","🇳🇿","🇳","🇴🇲","🇴","🇵🇦","🇵🇪","🇵🇫","🇵🇬","🇵🇭","🇵🇰","🇵🇱","🇵🇲","🇵🇳","🇵🇷","🇵🇸","🇵🇹","🇵🇼","🇵🇾","🇵","🇶🇦","🇶","🇷🇪","🇷🇴","🇷🇸","🇷🇺","🇷🇼","🇷","🇸🇦","🇸🇧","🇸🇨","🇸🇩","🇸🇪","🇸🇬","🇸🇭","🇸🇮","🇸🇯","🇸🇰","🇸🇱","🇸🇲","🇸🇳","🇸🇴","🇸🇷","🇸🇸","🇸🇹","🇸🇻","🇸🇽","🇸🇾","🇸🇿","🇸","🇹🇦","🇹🇨","🇹🇩","🇹🇫","🇹🇬","🇹🇭","🇹🇯","🇹🇰","🇹🇱","🇹🇲","🇹🇳","🇹🇴","🇹🇷","🇹🇹","🇹🇻","🇹🇼","🇹🇿","🇹","🇺🇦","🇺🇬","🇺🇲","🇺🇳","🇺🇸","🇺🇾","🇺🇿","🇺","🇻🇦","🇻🇨","🇻🇪","🇻🇬","🇻🇮","🇻🇳","🇻🇺","🇻","🇼🇫","🇼🇸","🇼","🇽🇰","🇽","🇾🇪","🇾🇹","🇾","🇿🇦","🇿🇲","🇿🇼","🇿","🈁","🈂","🈚","🈯","🈲","🈳","🈴","🈵","🈶","🈷","🈸","🈹","🈺","🉐","🉑","🌀","🌁","🌂","🌃","🌄","🌅","🌆","🌇","🌈","🌉","🌊","🌋","🌌","🌍","🌎","🌏","🌐","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","🌝","🌞","🌟","🌠","🌡","🌤","🌥","🌦","🌧","🌨","🌩","🌪","🌫","🌬","🌭","🌮","🌯","🌰","🌱","🌲","🌳","🌴","🌵","🌶","🌷","🌸","🌹","🌺","🌻","🌼","🌽","🌾","🌿","🍀","🍁","🍂","🍃","🍄","🍅","🍆","🍇","🍈","🍉","🍊","🍋","🍌","🍍","🍎","🍏","🍐","🍑","🍒","🍓","🍔","🍕","🍖","🍗","🍘","🍙","🍚","🍛","🍜","🍝","🍞","🍟","🍠","🍡","🍢","🍣","🍤","🍥","🍦","🍧","🍨","🍩","🍪","🍫","🍬","🍭","🍮","🍯","🍰","🍱","🍲","🍳","🍴","🍵","🍶","🍷","🍸","🍹","🍺","🍻","🍼","🍽","🍾","🍿","🎀","🎁","🎂","🎃","🎄","🎅🏻","🎅🏼","🎅🏽","🎅🏾","🎅🏿","🎅","🎆","🎇","🎈","🎉","🎊","🎋","🎌","🎍","🎎","🎏","🎐","🎑","🎒","🎓","🎖","🎗","🎙","🎚","🎛","🎞","🎟","🎠","🎡","🎢","🎣","🎤","🎥","🎦","🎧","🎨","🎩","🎪","🎫","🎬","🎭","🎮","🎯","🎰","🎱","🎲","🎳","🎴","🎵","🎶","🎷","🎸","🎹","🎺","🎻","🎼","🎽","🎾","🎿","🏀","🏁","🏂🏻","🏂🏼","🏂🏽","🏂🏾","🏂🏿","🏂","🏃🏻‍♀️","🏃🏻‍♂️","🏃🏻","🏃🏼‍♀️","🏃🏼‍♂️","🏃🏼","🏃🏽‍♀️","🏃🏽‍♂️","🏃🏽","🏃🏾‍♀️","🏃🏾‍♂️","🏃🏾","🏃🏿‍♀️","🏃🏿‍♂️","🏃🏿","🏃‍♀️","🏃‍♂️","🏃","🏄🏻‍♀️","🏄🏻‍♂️","🏄🏻","🏄🏼‍♀️","🏄🏼‍♂️","🏄🏼","🏄🏽‍♀️","🏄🏽‍♂️","🏄🏽","🏄🏾‍♀️","🏄🏾‍♂️","🏄🏾","🏄🏿‍♀️","🏄🏿‍♂️","🏄🏿","🏄‍♀️","🏄‍♂️","🏄","🏅","🏆","🏇🏻","🏇🏼","🏇🏽","🏇🏾","🏇🏿","🏇","🏈","🏉","🏊🏻‍♀️","🏊🏻‍♂️","🏊🏻","🏊🏼‍♀️","🏊🏼‍♂️","🏊🏼","🏊🏽‍♀️","🏊🏽‍♂️","🏊🏽","🏊🏾‍♀️","🏊🏾‍♂️","🏊🏾","🏊🏿‍♀️","🏊🏿‍♂️","🏊🏿","🏊‍♀️","🏊‍♂️","🏊","🏋🏻‍♀️","🏋🏻‍♂️","🏋🏻","🏋🏼‍♀️","🏋🏼‍♂️","🏋🏼","🏋🏽‍♀️","🏋🏽‍♂️","🏋🏽","🏋🏾‍♀️","🏋🏾‍♂️","🏋🏾","🏋🏿‍♀️","🏋🏿‍♂️","🏋🏿","🏋️‍♀️","🏋️‍♂️","🏋","🏌🏻‍♀️","🏌🏻‍♂️","🏌🏻","🏌🏼‍♀️","🏌🏼‍♂️","🏌🏼","🏌🏽‍♀️","🏌🏽‍♂️","🏌🏽","🏌🏾‍♀️","🏌🏾‍♂️","🏌🏾","🏌🏿‍♀️","🏌🏿‍♂️","🏌🏿","🏌️‍♀️","🏌️‍♂️","🏌","🏍","🏎","🏏","🏐","🏑","🏒","🏓","🏔","🏕","🏖","🏗","🏘","🏙","🏚","🏛","🏜","🏝","🏞","🏟","🏠","🏡","🏢","🏣","🏤","🏥","🏦","🏧","🏨","🏩","🏪","🏫","🏬","🏭","🏮","🏯","🏰","🏳️‍🌈","🏳","🏴‍☠️","🏴","🏵","🏷","🏸","🏹","🏺","🏻","🏼","🏽","🏾","🏿","🐀","🐁","🐂","🐃","🐄","🐅","🐆","🐇","🐈","🐉","🐊","🐋","🐌","🐍","🐎","🐏","🐐","🐑","🐒","🐓","🐔","🐕","🐖","🐗","🐘","🐙","🐚","🐛","🐜","🐝","🐞","🐟","🐠","🐡","🐢","🐣","🐤","🐥","🐦","🐧","🐨","🐩","🐪","🐫","🐬","🐭","🐮","🐯","🐰","🐱","🐲","🐳","🐴","🐵","🐶","🐷","🐸","🐹","🐺","🐻","🐼","🐽","🐾","🐿","👀","👁‍🗨","👁","👂🏻","👂🏼","👂🏽","👂🏾","👂🏿","👂","👃🏻","👃🏼","👃🏽","👃🏾","👃🏿","👃","👄","👅","👆🏻","👆🏼","👆🏽","👆🏾","👆🏿","👆","👇🏻","👇🏼","👇🏽","👇🏾","👇🏿","👇","👈🏻","👈🏼","👈🏽","👈🏾","👈🏿","👈","👉🏻","👉🏼","👉🏽","👉🏾","👉🏿","👉","👊🏻","👊🏼","👊🏽","👊🏾","👊🏿","👊","👋🏻","👋🏼","👋🏽","👋🏾","👋🏿","👋","👌🏻","👌🏼","👌🏽","👌🏾","👌🏿","👌","👍🏻","👍🏼","👍🏽","👍🏾","👍🏿","👍","👎🏻","👎🏼","👎🏽","👎🏾","👎🏿","👎","👏🏻","👏🏼","👏🏽","👏🏾","👏🏿","👏","👐🏻","👐🏼","👐🏽","👐🏾","👐🏿","👐","👑","👒","👓","👔","👕","👖","👗","👘","👙","👚","👛","👜","👝","👞","👟","👠","👡","👢","👣","👤","👥","👦🏻","👦🏼","👦🏽","👦🏾","👦🏿","👦","👧🏻","👧🏼","👧🏽","👧🏾","👧🏿","👧","👨🏻‍🌾","👨🏻‍🍳","👨🏻‍🎓","👨🏻‍🎤","👨🏻‍🎨","👨🏻‍🏫","👨🏻‍🏭","👨🏻‍💻","👨🏻‍💼","👨🏻‍🔧","👨🏻‍🔬","👨🏻‍🚀","👨🏻‍🚒","👨🏻‍⚕️","👨🏻‍⚖️","👨🏻‍✈️","👨🏻","👨🏼‍🌾","👨🏼‍🍳","👨🏼‍🎓","👨🏼‍🎤","👨🏼‍🎨","👨🏼‍🏫","👨🏼‍🏭","👨🏼‍💻","👨🏼‍💼","👨🏼‍🔧","👨🏼‍🔬","👨🏼‍🚀","👨🏼‍🚒","👨🏼‍⚕️","👨🏼‍⚖️","👨🏼‍✈️","👨🏼","👨🏽‍🌾","👨🏽‍🍳","👨🏽‍🎓","👨🏽‍🎤","👨🏽‍🎨","👨🏽‍🏫","👨🏽‍🏭","👨🏽‍💻","👨🏽‍💼","👨🏽‍🔧","👨🏽‍🔬","👨🏽‍🚀","👨🏽‍🚒","👨🏽‍⚕️","👨🏽‍⚖️","👨🏽‍✈️","👨🏽","👨🏾‍🌾","👨🏾‍🍳","👨🏾‍🎓","👨🏾‍🎤","👨🏾‍🎨","👨🏾‍🏫","👨🏾‍🏭","👨🏾‍💻","👨🏾‍💼","👨🏾‍🔧","👨🏾‍🔬","👨🏾‍🚀","👨🏾‍🚒","👨🏾‍⚕️","👨🏾‍⚖️","👨🏾‍✈️","👨🏾","👨🏿‍🌾","👨🏿‍🍳","👨🏿‍🎓","👨🏿‍🎤","👨🏿‍🎨","👨🏿‍🏫","👨🏿‍🏭","👨🏿‍💻","👨🏿‍💼","👨🏿‍🔧","👨🏿‍🔬","👨🏿‍🚀","👨🏿‍🚒","👨🏿‍⚕️","👨🏿‍⚖️","👨🏿‍✈️","👨🏿","👨‍🌾","👨‍🍳","👨‍🎓","👨‍🎤","👨‍🎨","👨‍🏫","👨‍🏭","👨‍👦‍👦","👨‍👦","👨‍👧‍👦","👨‍👧‍👧","👨‍👧","👨‍👨‍👦‍👦","👨‍👨‍👦","👨‍👨‍👧‍👦","👨‍👨‍👧‍👧","👨‍👨‍👧","👨‍👩‍👦‍👦","👨‍👩‍👦","👨‍👩‍👧‍👦","👨‍👩‍👧‍👧","👨‍👩‍👧","👨‍💻","👨‍💼","👨‍🔧","👨‍🔬","👨‍🚀","👨‍🚒","👨‍⚕️","👨‍⚖️","👨‍✈️","👨‍❤️‍👨","👨‍❤️‍💋‍👨","👨","👩🏻‍🌾","👩🏻‍🍳","👩🏻‍🎓","👩🏻‍🎤","👩🏻‍🎨","👩🏻‍🏫","👩🏻‍🏭","👩🏻‍💻","👩🏻‍💼","👩🏻‍🔧","👩🏻‍🔬","👩🏻‍🚀","👩🏻‍🚒","👩🏻‍⚕️","👩🏻‍⚖️","👩🏻‍✈️","👩🏻","👩🏼‍🌾","👩🏼‍🍳","👩🏼‍🎓","👩🏼‍🎤","👩🏼‍🎨","👩🏼‍🏫","👩🏼‍🏭","👩🏼‍💻","👩🏼‍💼","👩🏼‍🔧","👩🏼‍🔬","👩🏼‍🚀","👩🏼‍🚒","👩🏼‍⚕️","👩🏼‍⚖️","👩🏼‍✈️","👩🏼","👩🏽‍🌾","👩🏽‍🍳","👩🏽‍🎓","👩🏽‍🎤","👩🏽‍🎨","👩🏽‍🏫","👩🏽‍🏭","👩🏽‍💻","👩🏽‍💼","👩🏽‍🔧","👩🏽‍🔬","👩🏽‍🚀","👩🏽‍🚒","👩🏽‍⚕️","👩🏽‍⚖️","👩🏽‍✈️","👩🏽","👩🏾‍🌾","👩🏾‍🍳","👩🏾‍🎓","👩🏾‍🎤","👩🏾‍🎨","👩🏾‍🏫","👩🏾‍🏭","👩🏾‍💻","👩🏾‍💼","👩🏾‍🔧","👩🏾‍🔬","👩🏾‍🚀","👩🏾‍🚒","👩🏾‍⚕️","👩🏾‍⚖️","👩🏾‍✈️","👩🏾","👩🏿‍🌾","👩🏿‍🍳","👩🏿‍🎓","👩🏿‍🎤","👩🏿‍🎨","👩🏿‍🏫","👩🏿‍🏭","👩🏿‍💻","👩🏿‍💼","👩🏿‍🔧","👩🏿‍🔬","👩🏿‍🚀","👩🏿‍🚒","👩🏿‍⚕️","👩🏿‍⚖️","👩🏿‍✈️","👩🏿","👩‍🌾","👩‍🍳","👩‍🎓","👩‍🎤","👩‍🎨","👩‍🏫","👩‍🏭","👩‍👦‍👦","👩‍👦","👩‍👧‍👦","👩‍👧‍👧","👩‍👧","👩‍👩‍👦‍👦","👩‍👩‍👦","👩‍👩‍👧‍👦","👩‍👩‍👧‍👧","👩‍👩‍👧","👩‍💻","👩‍💼","👩‍🔧","👩‍🔬","👩‍🚀","👩‍🚒","👩‍⚕️","👩‍⚖️","👩‍✈️","👩‍❤️‍👨","👩‍❤️‍👩","👩‍❤️‍💋‍👨","👩‍❤️‍💋‍👩","👩","👪🏻","👪🏼","👪🏽","👪🏾","👪🏿","👪","👫🏻","👫🏼","👫🏽","👫🏾","👫🏿","👫","👬🏻","👬🏼","👬🏽","👬🏾","👬🏿","👬","👭🏻","👭🏼","👭🏽","👭🏾","👭🏿","👭","👮🏻‍♀️","👮🏻‍♂️","👮🏻","👮🏼‍♀️","👮🏼‍♂️","👮🏼","👮🏽‍♀️","👮🏽‍♂️","👮🏽","👮🏾‍♀️","👮🏾‍♂️","👮🏾","👮🏿‍♀️","👮🏿‍♂️","👮🏿","👮‍♀️","👮‍♂️","👮","👯🏻‍♀️","👯🏻‍♂️","👯🏻","👯🏼‍♀️","👯🏼‍♂️","👯🏼","👯🏽‍♀️","👯🏽‍♂️","👯🏽","👯🏾‍♀️","👯🏾‍♂️","👯🏾","👯🏿‍♀️","👯🏿‍♂️","👯🏿","👯‍♀️","👯‍♂️","👯","👰🏻","👰🏼","👰🏽","👰🏾","👰🏿","👰","👱🏻‍♀️","👱🏻‍♂️","👱🏻","👱🏼‍♀️","👱🏼‍♂️","👱🏼","👱🏽‍♀️","👱🏽‍♂️","👱🏽","👱🏾‍♀️","👱🏾‍♂️","👱🏾","👱🏿‍♀️","👱🏿‍♂️","👱🏿","👱‍♀️","👱‍♂️","👱","👲🏻","👲🏼","👲🏽","👲🏾","👲🏿","👲","👳🏻‍♀️","👳🏻‍♂️","👳🏻","👳🏼‍♀️","👳🏼‍♂️","👳🏼","👳🏽‍♀️","👳🏽‍♂️","👳🏽","👳🏾‍♀️","👳🏾‍♂️","👳🏾","👳🏿‍♀️","👳🏿‍♂️","👳🏿","👳‍♀️","👳‍♂️","👳","👴🏻","👴🏼","👴🏽","👴🏾","👴🏿","👴","👵🏻","👵🏼","👵🏽","👵🏾","👵🏿","👵","👶🏻","👶🏼","👶🏽","👶🏾","👶🏿","👶","👷🏻‍♀️","👷🏻‍♂️","👷🏻","👷🏼‍♀️","👷🏼‍♂️","👷🏼","👷🏽‍♀️","👷🏽‍♂️","👷🏽","👷🏾‍♀️","👷🏾‍♂️","👷🏾","👷🏿‍♀️","👷🏿‍♂️","👷🏿","👷‍♀️","👷‍♂️","👷","👸🏻","👸🏼","👸🏽","👸🏾","👸🏿","👸","👹","👺","👻","👼🏻","👼🏼","👼🏽","👼🏾","👼🏿","👼","👽","👾","👿","💀","💁🏻‍♀️","💁🏻‍♂️","💁🏻","💁🏼‍♀️","💁🏼‍♂️","💁🏼","💁🏽‍♀️","💁🏽‍♂️","💁🏽","💁🏾‍♀️","💁🏾‍♂️","💁🏾","💁🏿‍♀️","💁🏿‍♂️","💁🏿","💁‍♀️","💁‍♂️","💁","💂🏻‍♀️","💂🏻‍♂️","💂🏻","💂🏼‍♀️","💂🏼‍♂️","💂🏼","💂🏽‍♀️","💂🏽‍♂️","💂🏽","💂🏾‍♀️","💂🏾‍♂️","💂🏾","💂🏿‍♀️","💂🏿‍♂️","💂🏿","💂‍♀️","💂‍♂️","💂","💃🏻","💃🏼","💃🏽","💃🏾","💃🏿","💃","💄","💅🏻","💅🏼","💅🏽","💅🏾","💅🏿","💅","💆🏻‍♀️","💆🏻‍♂️","💆🏻","💆🏼‍♀️","💆🏼‍♂️","💆🏼","💆🏽‍♀️","💆🏽‍♂️","💆🏽","💆🏾‍♀️","💆🏾‍♂️","💆🏾","💆🏿‍♀️","💆🏿‍♂️","💆🏿","💆‍♀️","💆‍♂️","💆","💇🏻‍♀️","💇🏻‍♂️","💇🏻","💇🏼‍♀️","💇🏼‍♂️","💇🏼","💇🏽‍♀️","💇🏽‍♂️","💇🏽","💇🏾‍♀️","💇🏾‍♂️","💇🏾","💇🏿‍♀️","💇🏿‍♂️","💇🏿","💇‍♀️","💇‍♂️","💇","💈","💉","💊","💋","💌","💍","💎","💏","💐","💑","💒","💓","💔","💕","💖","💗","💘","💙","💚","💛","💜","💝","💞","💟","💠","💡","💢","💣","💤","💥","💦","💧","💨","💩","💪🏻","💪🏼","💪🏽","💪🏾","💪🏿","💪","💫","💬","💭","💮","💯","💰","💱","💲","💳","💴","💵","💶","💷","💸","💹","💺","💻","💼","💽","💾","💿","📀","📁","📂","📃","📄","📅","📆","📇","📈","📉","📊","📋","📌","📍","📎","📏","📐","📑","📒","📓","📔","📕","📖","📗","📘","📙","📚","📛","📜","📝","📞","📟","📠","📡","📢","📣","📤","📥","📦","📧","📨","📩","📪","📫","📬","📭","📮","📯","📰","📱","📲","📳","📴","📵","📶","📷","📸","📹","📺","📻","📼","📽","📿","🔀","🔁","🔂","🔃","🔄","🔅","🔆","🔇","🔈","🔉","🔊","🔋","🔌","🔍","🔎","🔏","🔐","🔑","🔒","🔓","🔔","🔕","🔖","🔗","🔘","🔙","🔚","🔛","🔜","🔝","🔞","🔟","🔠","🔡","🔢","🔣","🔤","🔥","🔦","🔧","🔨","🔩","🔪","🔫","🔬","🔭","🔮","🔯","🔰","🔱","🔲","🔳","🔴","🔵","🔶","🔷","🔸","🔹","🔺","🔻","🔼","🔽","🕉","🕊","🕋","🕌","🕍","🕎","🕐","🕑","🕒","🕓","🕔","🕕","🕖","🕗","🕘","🕙","🕚","🕛","🕜","🕝","🕞","🕟","🕠","🕡","🕢","🕣","🕤","🕥","🕦","🕧","🕯","🕰","🕳","🕴🏻","🕴🏼","🕴🏽","🕴🏾","🕴🏿","🕴","🕵🏻‍♀️","🕵🏻‍♂️","🕵🏻","🕵🏼‍♀️","🕵🏼‍♂️","🕵🏼","🕵🏽‍♀️","🕵🏽‍♂️","🕵🏽","🕵🏾‍♀️","🕵🏾‍♂️","🕵🏾","🕵🏿‍♀️","🕵🏿‍♂️","🕵🏿","🕵️‍♀️","🕵️‍♂️","🕵","🕶","🕷","🕸","🕹","🕺🏻","🕺🏼","🕺🏽","🕺🏾","🕺🏿","🕺","🖇","🖊","🖋","🖌","🖍","🖐🏻","🖐🏼","🖐🏽","🖐🏾","🖐🏿","🖐","🖕🏻","🖕🏼","🖕🏽","🖕🏾","🖕🏿","🖕","🖖🏻","🖖🏼","🖖🏽","🖖🏾","🖖🏿","🖖","🖤","🖥","🖨","🖱","🖲","🖼","🗂","🗃","🗄","🗑","🗒","🗓","🗜","🗝","🗞","🗡","🗣","🗨","🗯","🗳","🗺","🗻","🗼","🗽","🗾","🗿","😀","😁","😂","😃","😄","😅","😆","😇","😈","😉","😊","😋","😌","😍","😎","😏","😐","😑","😒","😓","😔","😕","😖","😗","😘","😙","😚","😛","😜","😝","😞","😟","😠","😡","😢","😣","😤","😥","😦","😧","😨","😩","😪","😫","😬","😭","😮","😯","😰","😱","😲","😳","😴","😵","😶","😷","😸","😹","😺","😻","😼","😽","😾","😿","🙀","🙁","🙂","🙃","🙄","🙅🏻‍♀️","🙅🏻‍♂️","🙅🏻","🙅🏼‍♀️","🙅🏼‍♂️","🙅🏼","🙅🏽‍♀️","🙅🏽‍♂️","🙅🏽","🙅🏾‍♀️","🙅🏾‍♂️","🙅🏾","🙅🏿‍♀️","🙅🏿‍♂️","🙅🏿","🙅‍♀️","🙅‍♂️","🙅","🙆🏻‍♀️","🙆🏻‍♂️","🙆🏻","🙆🏼‍♀️","🙆🏼‍♂️","🙆🏼","🙆🏽‍♀️","🙆🏽‍♂️","🙆🏽","🙆🏾‍♀️","🙆🏾‍♂️","🙆🏾","🙆🏿‍♀️","🙆🏿‍♂️","🙆🏿","🙆‍♀️","🙆‍♂️","🙆","🙇🏻‍♀️","🙇🏻‍♂️","🙇🏻","🙇🏼‍♀️","🙇🏼‍♂️","🙇🏼","🙇🏽‍♀️","🙇🏽‍♂️","🙇🏽","🙇🏾‍♀️","🙇🏾‍♂️","🙇🏾","🙇🏿‍♀️","🙇🏿‍♂️","🙇🏿","🙇‍♀️","🙇‍♂️","🙇","🙈","🙉","🙊","🙋🏻‍♀️","🙋🏻‍♂️","🙋🏻","🙋🏼‍♀️","🙋🏼‍♂️","🙋🏼","🙋🏽‍♀️","🙋🏽‍♂️","🙋🏽","🙋🏾‍♀️","🙋🏾‍♂️","🙋🏾","🙋🏿‍♀️","🙋🏿‍♂️","🙋🏿","🙋‍♀️","🙋‍♂️","🙋","🙌🏻","🙌🏼","🙌🏽","🙌🏾","🙌🏿","🙌","🙍🏻‍♀️","🙍🏻‍♂️","🙍🏻","🙍🏼‍♀️","🙍🏼‍♂️","🙍🏼","🙍🏽‍♀️","🙍🏽‍♂️","🙍🏽","🙍🏾‍♀️","🙍🏾‍♂️","🙍🏾","🙍🏿‍♀️","🙍🏿‍♂️","🙍🏿","🙍‍♀️","🙍‍♂️","🙍","🙎🏻‍♀️","🙎🏻‍♂️","🙎🏻","🙎🏼‍♀️","🙎🏼‍♂️","🙎🏼","🙎🏽‍♀️","🙎🏽‍♂️","🙎🏽","🙎🏾‍♀️","🙎🏾‍♂️","🙎🏾","🙎🏿‍♀️","🙎🏿‍♂️","🙎🏿","🙎‍♀️","🙎‍♂️","🙎","🙏🏻","🙏🏼","🙏🏽","🙏🏾","🙏🏿","🙏","🚀","🚁","🚂","🚃","🚄","🚅","🚆","🚇","🚈","🚉","🚊","🚋","🚌","🚍","🚎","🚏","🚐","🚑","🚒","🚓","🚔","🚕","🚖","🚗","🚘","🚙","🚚","🚛","🚜","🚝","🚞","🚟","🚠","🚡","🚢","🚣🏻‍♀️","🚣🏻‍♂️","🚣🏻","🚣🏼‍♀️","🚣🏼‍♂️","🚣🏼","🚣🏽‍♀️","🚣🏽‍♂️","🚣🏽","🚣🏾‍♀️","🚣🏾‍♂️","🚣🏾","🚣🏿‍♀️","🚣🏿‍♂️","🚣🏿","🚣‍♀️","🚣‍♂️","🚣","🚤","🚥","🚦","🚧","🚨","🚩","🚪","🚫","🚬","🚭","🚮","🚯","🚰","🚱","🚲","🚳","🚴🏻‍♀️","🚴🏻‍♂️","🚴🏻","🚴🏼‍♀️","🚴🏼‍♂️","🚴🏼","🚴🏽‍♀️","🚴🏽‍♂️","🚴🏽","🚴🏾‍♀️","🚴🏾‍♂️","🚴🏾","🚴🏿‍♀️","🚴🏿‍♂️","🚴🏿","🚴‍♀️","🚴‍♂️","🚴","🚵🏻‍♀️","🚵🏻‍♂️","🚵🏻","🚵🏼‍♀️","🚵🏼‍♂️","🚵🏼","🚵🏽‍♀️","🚵🏽‍♂️","🚵🏽","🚵🏾‍♀️","🚵🏾‍♂️","🚵🏾","🚵🏿‍♀️","🚵🏿‍♂️","🚵🏿","🚵‍♀️","🚵‍♂️","🚵","🚶🏻‍♀️","🚶🏻‍♂️","🚶🏻","🚶🏼‍♀️","🚶🏼‍♂️","🚶🏼","🚶🏽‍♀️","🚶🏽‍♂️","🚶🏽","🚶🏾‍♀️","🚶🏾‍♂️","🚶🏾","🚶🏿‍♀️","🚶🏿‍♂️","🚶🏿","🚶‍♀️","🚶‍♂️","🚶","🚷","🚸","🚹","🚺","🚻","🚼","🚽","🚾","🚿","🛀🏻","🛀🏼","🛀🏽","🛀🏾","🛀🏿","🛀","🛁","🛂","🛃","🛄","🛅","🛋","🛌🏻","🛌🏼","🛌🏽","🛌🏾","🛌🏿","🛌","🛍","🛎","🛏","🛐","🛑","🛒","🛠","🛡","🛢","🛣","🛤","🛥","🛩","🛫","🛬","🛰","🛳","🛴","🛵","🛶","🤐","🤑","🤒","🤓","🤔","🤕","🤖","🤗","🤘🏻","🤘🏼","🤘🏽","🤘🏾","🤘🏿","🤘","🤙🏻","🤙🏼","🤙🏽","🤙🏾","🤙🏿","🤙","🤚🏻","🤚🏼","🤚🏽","🤚🏾","🤚🏿","🤚","🤛🏻","🤛🏼","🤛🏽","🤛🏾","🤛🏿","🤛","🤜🏻","🤜🏼","🤜🏽","🤜🏾","🤜🏿","🤜","🤝🏻","🤝🏼","🤝🏽","🤝🏾","🤝🏿","🤝","🤞🏻","🤞🏼","🤞🏽","🤞🏾","🤞🏿","🤞","🤠","🤡","🤢","🤣","🤤","🤥","🤦🏻‍♀️","🤦🏻‍♂️","🤦🏻","🤦🏼‍♀️","🤦🏼‍♂️","🤦🏼","🤦🏽‍♀️","🤦🏽‍♂️","🤦🏽","🤦🏾‍♀️","🤦🏾‍♂️","🤦🏾","🤦🏿‍♀️","🤦🏿‍♂️","🤦🏿","🤦‍♀️","🤦‍♂️","🤦","🤧","🤰🏻","🤰🏼","🤰🏽","🤰🏾","🤰🏿","🤰","🤳🏻","🤳🏼","🤳🏽","🤳🏾","🤳🏿","🤳","🤴🏻","🤴🏼","🤴🏽","🤴🏾","🤴🏿","🤴","🤵🏻","🤵🏼","🤵🏽","🤵🏾","🤵🏿","🤵","🤶🏻","🤶🏼","🤶🏽","🤶🏾","🤶🏿","🤶","🤷🏻‍♀️","🤷🏻‍♂️","🤷🏻","🤷🏼‍♀️","🤷🏼‍♂️","🤷🏼","🤷🏽‍♀️","🤷🏽‍♂️","🤷🏽","🤷🏾‍♀️","🤷🏾‍♂️","🤷🏾","🤷🏿‍♀️","🤷🏿‍♂️","🤷🏿","🤷‍♀️","🤷‍♂️","🤷","🤸🏻‍♀️","🤸🏻‍♂️","🤸🏻","🤸🏼‍♀️","🤸🏼‍♂️","🤸🏼","🤸🏽‍♀️","🤸🏽‍♂️","🤸🏽","🤸🏾‍♀️","🤸🏾‍♂️","🤸🏾","🤸🏿‍♀️","🤸🏿‍♂️","🤸🏿","🤸‍♀️","🤸‍♂️","🤸","🤹🏻‍♀️","🤹🏻‍♂️","🤹🏻","🤹🏼‍♀️","🤹🏼‍♂️","🤹🏼","🤹🏽‍♀️","🤹🏽‍♂️","🤹🏽","🤹🏾‍♀️","🤹🏾‍♂️","🤹🏾","🤹🏿‍♀️","🤹🏿‍♂️","🤹🏿","🤹‍♀️","🤹‍♂️","🤹","🤺","🤼🏻‍♀️","🤼🏻‍♂️","🤼🏻","🤼🏼‍♀️","🤼🏼‍♂️","🤼🏼","🤼🏽‍♀️","🤼🏽‍♂️","🤼🏽","🤼🏾‍♀️","🤼🏾‍♂️","🤼🏾","🤼🏿‍♀️","🤼🏿‍♂️","🤼🏿","🤼‍♀️","🤼‍♂️","🤼","🤽🏻‍♀️","🤽🏻‍♂️","🤽🏻","🤽🏼‍♀️","🤽🏼‍♂️","🤽🏼","🤽🏽‍♀️","🤽🏽‍♂️","🤽🏽","🤽🏾‍♀️","🤽🏾‍♂️","🤽🏾","🤽🏿‍♀️","🤽🏿‍♂️","🤽🏿","🤽‍♀️","🤽‍♂️","🤽","🤾🏻‍♀️","🤾🏻‍♂️","🤾🏻","🤾🏼‍♀️","🤾🏼‍♂️","🤾🏼","🤾🏽‍♀️","🤾🏽‍♂️","🤾🏽","🤾🏾‍♀️","🤾🏾‍♂️","🤾🏾","🤾🏿‍♀️","🤾🏿‍♂️","🤾🏿","🤾‍♀️","🤾‍♂️","🤾","🥀","🥁","🥂","🥃","🥄","🥅","🥇","🥈","🥉","🥊","🥋","🥐","🥑","🥒","🥓","🥔","🥕","🥖","🥗","🥘","🥙","🥚","🥛","🥜","🥝","🥞","🦀","🦁","🦂","🦃","🦄","🦅","🦆","🦇","🦈","🦉","🦊","🦋","🦌","🦍","🦎","🦏","🦐","🦑","🧀","‼","⁉","™","ℹ","↔","↕","↖","↗","↘","↙","↩","↪","#⃣","⌚","⌛","⌨","⏏","⏩","⏪","⏫","⏬","⏭","⏮","⏯","⏰","⏱","⏲","⏳","⏸","⏹","⏺","Ⓜ","▪","▫","▶","◀","◻","◼","◽","◾","☀","☁","☂","☃","☄","☎","☑","☔","☕","☘","☝🏻","☝🏼","☝🏽","☝🏾","☝🏿","☝","☠","☢","☣","☦","☪","☮","☯","☸","☹","☺","♀","♂","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","♠","♣","♥","♦","♨","♻","♿","⚒","⚓","⚔","⚕","⚖","⚗","⚙","⚛","⚜","⚠","⚡","⚪","⚫","⚰","⚱","⚽","⚾","⛄","⛅","⛈","⛎","⛏","⛑","⛓","⛔","⛩","⛪","⛰","⛱","⛲","⛳","⛴","⛵","⛷🏻","⛷🏼","⛷🏽","⛷🏾","⛷🏿","⛷","⛸","⛹🏻‍♀️","⛹🏻‍♂️","⛹🏻","⛹🏼‍♀️","⛹🏼‍♂️","⛹🏼","⛹🏽‍♀️","⛹🏽‍♂️","⛹🏽","⛹🏾‍♀️","⛹🏾‍♂️","⛹🏾","⛹🏿‍♀️","⛹🏿‍♂️","⛹🏿","⛹️‍♀️","⛹️‍♂️","⛹","⛺","⛽","✂","✅","✈","✉","✊🏻","✊🏼","✊🏽","✊🏾","✊🏿","✊","✋🏻","✋🏼","✋🏽","✋🏾","✋🏿","✋","✌🏻","✌🏼","✌🏽","✌🏾","✌🏿","✌","✍🏻","✍🏼","✍🏽","✍🏾","✍🏿","✍","✏","✒","✔","✖","✝","✡","✨","✳","✴","❄","❇","❌","❎","❓","❔","❕","❗","❣","❤","➕","➖","➗","➡","➰","➿","⤴","⤵","*⃣","⬅","⬆","⬇","⬛","⬜","⭐","⭕","0⃣","〰","〽","1⃣","2⃣","㊗","㊙","3⃣","4⃣","5⃣","6⃣","7⃣","8⃣","9⃣","©","®",""]},609:function(e,t,r){"use strict";var i=r(150);var n=r(203);e.exports={activityIndicator:function(e,t,r){if(e.spun==null)return;return i(t,e.spun)},progressbar:function(e,t,r){if(e.completed==null)return;return n(t,r,e.completed)}}},620:function(e,t,r){e.exports=minimatch;minimatch.Minimatch=Minimatch;var i={sep:"/"};try{i=r(589)}catch(e){}var n=minimatch.GLOBSTAR=Minimatch.GLOBSTAR={};var s=r(348);var a={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}};var u="[^/]";var o=u+"*?";var l="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?";var f="(?:(?!(?:\\/|^)\\.).)*?";var h=charSet("().*{}+?[]^$\\!");function charSet(e){return e.split("").reduce(function(e,t){e[t]=true;return e},{})}var c=/\/+/;minimatch.filter=filter;function filter(e,t){t=t||{};return function(r,i,n){return minimatch(r,e,t)}}function ext(e,t){e=e||{};t=t||{};var r={};Object.keys(t).forEach(function(e){r[e]=t[e]});Object.keys(e).forEach(function(t){r[t]=e[t]});return r}minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return minimatch;var t=minimatch;var r=function minimatch(r,i,n){return t.minimatch(r,i,ext(e,n))};r.Minimatch=function Minimatch(r,i){return new t.Minimatch(r,ext(e,i))};return r};Minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return Minimatch;return minimatch.defaults(e).Minimatch};function minimatch(e,t,r){if(typeof t!=="string"){throw new TypeError("glob pattern string required")}if(!r)r={};if(!r.nocomment&&t.charAt(0)==="#"){return false}if(t.trim()==="")return e==="";return new Minimatch(t,r).match(e)}function Minimatch(e,t){if(!(this instanceof Minimatch)){return new Minimatch(e,t)}if(typeof e!=="string"){throw new TypeError("glob pattern string required")}if(!t)t={};e=e.trim();if(i.sep!=="/"){e=e.split(i.sep).join("/")}this.options=t;this.set=[];this.pattern=e;this.regexp=null;this.negate=false;this.comment=false;this.empty=false;this.make()}Minimatch.prototype.debug=function(){};Minimatch.prototype.make=make;function make(){if(this._made)return;var e=this.pattern;var t=this.options;if(!t.nocomment&&e.charAt(0)==="#"){this.comment=true;return}if(!e){this.empty=true;return}this.parseNegate();var r=this.globSet=this.braceExpand();if(t.debug)this.debug=console.error;this.debug(this.pattern,r);r=this.globParts=r.map(function(e){return e.split(c)});this.debug(this.pattern,r);r=r.map(function(e,t,r){return e.map(this.parse,this)},this);this.debug(this.pattern,r);r=r.filter(function(e){return e.indexOf(false)===-1});this.debug(this.pattern,r);this.set=r}Minimatch.prototype.parseNegate=parseNegate;function parseNegate(){var e=this.pattern;var t=false;var r=this.options;var i=0;if(r.nonegate)return;for(var n=0,s=e.length;n<s&&e.charAt(n)==="!";n++){t=!t;i++}if(i)this.pattern=e.substr(i);this.negate=t}minimatch.braceExpand=function(e,t){return braceExpand(e,t)};Minimatch.prototype.braceExpand=braceExpand;function braceExpand(e,t){if(!t){if(this instanceof Minimatch){t=this.options}else{t={}}}e=typeof e==="undefined"?this.pattern:e;if(typeof e==="undefined"){throw new TypeError("undefined pattern")}if(t.nobrace||!e.match(/\{.*\}/)){return[e]}return s(e)}Minimatch.prototype.parse=parse;var p={};function parse(e,t){if(e.length>1024*64){throw new TypeError("pattern is too long")}var r=this.options;if(!r.noglobstar&&e==="**")return n;if(e==="")return"";var i="";var s=!!r.nocase;var l=false;var f=[];var c=[];var d;var v=false;var y=-1;var g=-1;var b=e.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)";var _=this;function clearStateChar(){if(d){switch(d){case"*":i+=o;s=true;break;case"?":i+=u;s=true;break;default:i+="\\"+d;break}_.debug("clearStateChar %j %j",d,i);d=false}}for(var m=0,E=e.length,D;m<E&&(D=e.charAt(m));m++){this.debug("%s\t%s %s %j",e,m,i,D);if(l&&h[D]){i+="\\"+D;l=false;continue}switch(D){case"/":return false;case"\\":clearStateChar();l=true;continue;case"?":case"*":case"+":case"@":case"!":this.debug("%s\t%s %s %j <-- stateChar",e,m,i,D);if(v){this.debug("  in class");if(D==="!"&&m===g+1)D="^";i+=D;continue}_.debug("call clearStateChar %j",d);clearStateChar();d=D;if(r.noext)clearStateChar();continue;case"(":if(v){i+="(";continue}if(!d){i+="\\(";continue}f.push({type:d,start:m-1,reStart:i.length,open:a[d].open,close:a[d].close});i+=d==="!"?"(?:(?!(?:":"(?:";this.debug("plType %j %j",d,i);d=false;continue;case")":if(v||!f.length){i+="\\)";continue}clearStateChar();s=true;var w=f.pop();i+=w.close;if(w.type==="!"){c.push(w)}w.reEnd=i.length;continue;case"|":if(v||!f.length||l){i+="\\|";l=false;continue}clearStateChar();i+="|";continue;case"[":clearStateChar();if(v){i+="\\"+D;continue}v=true;g=m;y=i.length;i+=D;continue;case"]":if(m===g+1||!v){i+="\\"+D;l=false;continue}if(v){var A=e.substring(g+1,m);try{RegExp("["+A+"]")}catch(e){var C=this.parse(A,p);i=i.substr(0,y)+"\\["+C[0]+"\\]";s=s||C[1];v=false;continue}}s=true;v=false;i+=D;continue;default:clearStateChar();if(l){l=false}else if(h[D]&&!(D==="^"&&v)){i+="\\"}i+=D}}if(v){A=e.substr(g+1);C=this.parse(A,p);i=i.substr(0,y)+"\\["+C[0];s=s||C[1]}for(w=f.pop();w;w=f.pop()){var x=i.slice(w.reStart+w.open.length);this.debug("setting tail",i,w);x=x.replace(/((?:\\{2}){0,64})(\\?)\|/g,function(e,t,r){if(!r){r="\\"}return t+t+r+"|"});this.debug("tail=%j\n   %s",x,x,w,i);var S=w.type==="*"?o:w.type==="?"?u:"\\"+w.type;s=true;i=i.slice(0,w.reStart)+S+"\\("+x}clearStateChar();if(l){i+="\\\\"}var k=false;switch(i.charAt(0)){case".":case"[":case"(":k=true}for(var F=c.length-1;F>-1;F--){var R=c[F];var T=i.slice(0,R.reStart);var O=i.slice(R.reStart,R.reEnd-8);var I=i.slice(R.reEnd-8,R.reEnd);var B=i.slice(R.reEnd);I+=B;var N=T.split("(").length-1;var L=B;for(m=0;m<N;m++){L=L.replace(/\)[+*?]?/,"")}B=L;var P="";if(B===""&&t!==p){P="$"}var j=T+O+B+P+I;i=j}if(i!==""&&s){i="(?=.)"+i}if(k){i=b+i}if(t===p){return[i,s]}if(!s){return globUnescape(e)}var q=r.nocase?"i":"";try{var $=new RegExp("^"+i+"$",q)}catch(e){return new RegExp("$.")}$._glob=e;$._src=i;return $}minimatch.makeRe=function(e,t){return new Minimatch(e,t||{}).makeRe()};Minimatch.prototype.makeRe=makeRe;function makeRe(){if(this.regexp||this.regexp===false)return this.regexp;var e=this.set;if(!e.length){this.regexp=false;return this.regexp}var t=this.options;var r=t.noglobstar?o:t.dot?l:f;var i=t.nocase?"i":"";var s=e.map(function(e){return e.map(function(e){return e===n?r:typeof e==="string"?regExpEscape(e):e._src}).join("\\/")}).join("|");s="^(?:"+s+")$";if(this.negate)s="^(?!"+s+").*$";try{this.regexp=new RegExp(s,i)}catch(e){this.regexp=false}return this.regexp}minimatch.match=function(e,t,r){r=r||{};var i=new Minimatch(t,r);e=e.filter(function(e){return i.match(e)});if(i.options.nonull&&!e.length){e.push(t)}return e};Minimatch.prototype.match=match;function match(e,t){this.debug("match",e,this.pattern);if(this.comment)return false;if(this.empty)return e==="";if(e==="/"&&t)return true;var r=this.options;if(i.sep!=="/"){e=e.split(i.sep).join("/")}e=e.split(c);this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var s;var a;for(a=e.length-1;a>=0;a--){s=e[a];if(s)break}for(a=0;a<n.length;a++){var u=n[a];var o=e;if(r.matchBase&&u.length===1){o=[s]}var l=this.matchOne(o,u,t);if(l){if(r.flipNegate)return true;return!this.negate}}if(r.flipNegate)return false;return this.negate}Minimatch.prototype.matchOne=function(e,t,r){var i=this.options;this.debug("matchOne",{this:this,file:e,pattern:t});this.debug("matchOne",e.length,t.length);for(var s=0,a=0,u=e.length,o=t.length;s<u&&a<o;s++,a++){this.debug("matchOne loop");var l=t[a];var f=e[s];this.debug(t,l,f);if(l===false)return false;if(l===n){this.debug("GLOBSTAR",[t,l,f]);var h=s;var c=a+1;if(c===o){this.debug("** at the end");for(;s<u;s++){if(e[s]==="."||e[s]===".."||!i.dot&&e[s].charAt(0)===".")return false}return true}while(h<u){var p=e[h];this.debug("\nglobstar while",e,h,t,c,p);if(this.matchOne(e.slice(h),t.slice(c),r)){this.debug("globstar found match!",h,u,p);return true}else{if(p==="."||p===".."||!i.dot&&p.charAt(0)==="."){this.debug("dot detected!",e,h,t,c);break}this.debug("globstar swallow a segment, and continue");h++}}if(r){this.debug("\n>>> no match, partial?",e,h,t,c);if(h===u)return true}return false}var d;if(typeof l==="string"){if(i.nocase){d=f.toLowerCase()===l.toLowerCase()}else{d=f===l}this.debug("string match",l,f,d)}else{d=f.match(l);this.debug("pattern match",l,f,d)}if(!d)return false}if(s===u&&a===o){return true}else if(s===u){return r}else if(a===o){var v=s===u-1&&e[s]==="";return v}throw new Error("wtf?")};function globUnescape(e){return e.replace(/\\(.)/g,"$1")}function regExpEscape(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}},630:function(e,t,r){"use strict";var i=r(431).platform();var n=r(204).spawnSync;var s=r(66).readdirSync;var a="glibc";var u="musl";var o={encoding:"utf8",env:process.env};if(!n){n=function(){return{status:126,stdout:"",stderr:""}}}function contains(e){return function(t){return t.indexOf(e)!==-1}}function versionFromMuslLdd(e){return e.split(/[\r\n]+/)[1].trim().split(/\s/)[1]}function safeReaddirSync(e){try{return s(e)}catch(e){}return[]}var l="";var f="";var h="";if(i==="linux"){var c=n("getconf",["GNU_LIBC_VERSION"],o);if(c.status===0){l=a;f=c.stdout.trim().split(" ")[1];h="getconf"}else{var p=n("ldd",["--version"],o);if(p.status===0&&p.stdout.indexOf(u)!==-1){l=u;f=versionFromMuslLdd(p.stdout);h="ldd"}else if(p.status===1&&p.stderr.indexOf(u)!==-1){l=u;f=versionFromMuslLdd(p.stderr);h="ldd"}else{var d=safeReaddirSync("/lib");if(d.some(contains("-linux-gnu"))){l=a;h="filesystem"}else if(d.some(contains("libc.musl-"))){l=u;h="filesystem"}else if(d.some(contains("ld-musl-"))){l=u;h="filesystem"}else{var v=safeReaddirSync("/usr/sbin");if(v.some(contains("glibc"))){l=a;h="filesystem"}}}}}var y=l!==""&&l!==a;e.exports={GLIBC:a,MUSL:u,family:l,version:f,method:h,isNonGlibcLinux:y}},631:function(e){e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32"){e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT")}if(process.platform==="linux"){e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}},645:function(e,t,r){"use strict";var i=r(354);var n=r(765);var s=r(814);var a=e.exports=function(e,t,r){if(!r)r=80;s("OAN",[e,t,r]);this.showing=false;this.theme=e;this.width=r;this.template=t};a.prototype={};a.prototype.setTheme=function(e){s("O",[e]);this.theme=e};a.prototype.setTemplate=function(e){s("A",[e]);this.template=e};a.prototype.setWidth=function(e){s("N",[e]);this.width=e};a.prototype.hide=function(){return i.gotoSOL()+i.eraseLine()};a.prototype.hideCursor=i.hideCursor;a.prototype.showCursor=i.showCursor;a.prototype.show=function(e){var t=Object.create(this.theme);for(var r in e){t[r]=e[r]}return n(this.width,this.template,t).trim()+i.color("reset")+i.eraseLine()+i.gotoSOL()}},649:function(e,t,r){"use strict";var i=r(22);var n=r(656);e.exports=wideTruncate;function wideTruncate(e,t){if(i(e)===0)return e;if(t<=0)return"";if(i(e)<=t)return e;var r=n(e);var s=e.length+r.length;var a=e.slice(0,t+s);while(i(a)>t){a=a.slice(0,-1)}return a}},656:function(e,t,r){"use strict";var i=r(446)();e.exports=function(e){return typeof e==="string"?e.replace(i,""):e}},663:function(e,t,r){"use strict";var i=r(485).EventEmitter;var n=r(64);var s=0;var a=e.exports=function(e){i.call(this);this.id=++s;this.name=e};n.inherits(a,i)},668:function(e,t,r){"use strict";const i=r(140);e.exports=function(e){const t=e.acorn||r(996);const n=t.tokTypes;e=i(e);return class extends e{_maybeParseFieldValue(e){if(this.eat(n.eq)){const t=this._inFieldValue;this._inFieldValue=true;e.value=this.parseExpression();this._inFieldValue=t}else e.value=null}parseClassElement(e){if(this.options.ecmaVersion>=8&&(this.type==n.name||this.type==this.privateNameToken||this.type==n.bracketL||this.type==n.string)){const e=this._branch();if(e.type==n.bracketL){let t=0;do{if(e.eat(n.bracketL))++t;else if(e.eat(n.bracketR))--t;else e.next()}while(t>0)}else e.next();if(e.type==n.eq||e.canInsertSemicolon()||e.type==n.semi){const e=this.startNode();if(this.type==this.privateNameToken){this.parsePrivateClassElementName(e)}else{this.parsePropertyName(e)}if(e.key.type==="Identifier"&&e.key.name==="constructor"||e.key.type==="Literal"&&e.key.value==="constructor"){this.raise(e.key.start,"Classes may not have a field called constructor")}this.enterScope(64|2|1);this._maybeParseFieldValue(e);this.exitScope();this.finishNode(e,"FieldDefinition");this.semicolon();return e}}return super.parseClassElement.apply(this,arguments)}parseIdent(e,t){const r=super.parseIdent(e,t);if(this._inFieldValue&&r.name=="arguments")this.raise(r.start,"A class field initializer may not contain arguments");return r}}}},669:function(e,t,r){"use strict";const i=r(106);const n=r(146);const s=r(543);const a=r(300);const u=r(692);const o=r(72);const l=r(113);const f=r(768);const h=r(685);const c=r(337);t.getOptions=i;t.parseQuery=n;t.stringifyRequest=s;t.getRemainingRequest=a;t.getCurrentRequest=u;t.isUrlRequest=o;t.urlToRequest=l;t.parseString=f;t.getHashDigest=h;t.interpolateName=c},683:function(e,t){function isArray(e){if(Array.isArray){return Array.isArray(e)}return objectToString(e)==="[object Array]"}t.isArray=isArray;function isBoolean(e){return typeof e==="boolean"}t.isBoolean=isBoolean;function isNull(e){return e===null}t.isNull=isNull;function isNullOrUndefined(e){return e==null}t.isNullOrUndefined=isNullOrUndefined;function isNumber(e){return typeof e==="number"}t.isNumber=isNumber;function isString(e){return typeof e==="string"}t.isString=isString;function isSymbol(e){return typeof e==="symbol"}t.isSymbol=isSymbol;function isUndefined(e){return e===void 0}t.isUndefined=isUndefined;function isRegExp(e){return objectToString(e)==="[object RegExp]"}t.isRegExp=isRegExp;function isObject(e){return typeof e==="object"&&e!==null}t.isObject=isObject;function isDate(e){return objectToString(e)==="[object Date]"}t.isDate=isDate;function isError(e){return objectToString(e)==="[object Error]"||e instanceof Error}t.isError=isError;function isFunction(e){return typeof e==="function"}t.isFunction=isFunction;function isPrimitive(e){return e===null||typeof e==="boolean"||typeof e==="number"||typeof e==="string"||typeof e==="symbol"||typeof e==="undefined"}t.isPrimitive=isPrimitive;t.isBuffer=Buffer.isBuffer;function objectToString(e){return Object.prototype.toString.call(e)}},684:function(e,t,r){"use strict";const i=r(743);const n=r(606);e.exports=(e=>{if(typeof e!=="string"||e.length===0){return 0}e=i(e);let t=0;for(let r=0;r<e.length;r++){const i=e.codePointAt(r);if(i<=31||i>=127&&i<=159){continue}if(i>=768&&i<=879){continue}if(i>65535){r++}t+=n(i)?2:1}return t})},685:function(e,t,r){"use strict";const i={26:"abcdefghijklmnopqrstuvwxyz",32:"123456789abcdefghjkmnpqrstuvwxyz",36:"0123456789abcdefghijklmnopqrstuvwxyz",49:"abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",52:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",58:"**********************************************************",62:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",64:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"};function encodeBufferToBase(e,t){const n=i[t];if(!n){throw new Error("Unknown encoding base"+t)}const s=e.length;const a=r(480);a.RM=a.DP=0;let u=new a(0);for(let t=s-1;t>=0;t--){u=u.times(256).plus(e[t])}let o="";while(u.gt(0)){o=n[u.mod(t)]+o;u=u.div(t)}a.DP=20;a.RM=1;return o}function getHashDigest(e,t,i,n){t=t||"md5";n=n||9999;const s=r(298).createHash(t);s.update(e);if(i==="base26"||i==="base32"||i==="base36"||i==="base49"||i==="base52"||i==="base58"||i==="base62"||i==="base64"){return encodeBufferToBase(s.digest(),i.substr(4)).substr(0,n)}else{return s.digest(i||"hex").substr(0,n)}}e.exports=getHashDigest},688:function(e){e.exports=__webpack_require__(413)},692:function(e){"use strict";function getCurrentRequest(e){if(e.currentRequest){return e.currentRequest}const t=e.loaders.slice(e.loaderIndex).map(e=>e.request).concat([e.resource]);return t.join("!")}e.exports=getCurrentRequest},708:function(e,t,r){const{walk:i}=r(825);function handleWrappers(e,t,r){let n=false;let s;if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="UnaryExpression"&&e.body[0].expression.operator==="!"&&e.body[0].expression.argument.type==="CallExpression"&&e.body[0].expression.argument.callee.type==="FunctionExpression"&&e.body[0].expression.argument.arguments.length===1)s=e.body[0].expression.argument;else if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="CallExpression"&&e.body[0].expression.callee.type==="FunctionExpression"&&e.body[0].expression.arguments.length===1)s=e.body[0].expression;else if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="AssgnmentExpression"&&e.body[0].expression.left.type==="MemberExpression"&&e.body[0].expression.left.object.type==="Identifier"&&e.body[0].expression.left.object.name==="module"&&e.body[0].expression.left.property.type==="Identifier"&&e.body[0].expression.left.property.name==="exports"&&e.body[0].expression.right.type==="CallExpression"&&e.body[0].expression.right.callee.type==="FunctionExpression"&&e.body[0].expression.right.arguments.length===1)s=e.body[0].expression.right;if(s){if(s.arguments[0].type==="ConditionalExpression"&&s.arguments[0].test.type==="LogicalExpression"&&s.arguments[0].test.operator==="&&"&&s.arguments[0].test.left.type==="BinaryExpression"&&s.arguments[0].test.left.operator==="==="&&s.arguments[0].test.left.left.type==="UnaryExpression"&&s.arguments[0].test.left.left.operator==="typeof"&&s.arguments[0].test.left.left.argument.name==="define"&&s.arguments[0].test.left.right.type==="Literal"&&s.arguments[0].test.left.right.value==="function"&&s.arguments[0].test.right.type==="MemberExpression"&&s.arguments[0].test.right.object.type==="Identifier"&&s.arguments[0].test.right.property.type==="Identifier"&&s.arguments[0].test.right.property.name==="amd"&&s.arguments[0].test.right.computed===false&&s.arguments[0].alternate.type==="FunctionExpression"&&s.arguments[0].alternate.params.length===1&&s.arguments[0].alternate.params[0].type==="Identifier"&&s.arguments[0].alternate.body.body.length===1&&s.arguments[0].alternate.body.body[0].type==="ExpressionStatement"&&s.arguments[0].alternate.body.body[0].expression.type==="AssignmentExpression"&&s.arguments[0].alternate.body.body[0].expression.left.type==="MemberExpression"&&s.arguments[0].alternate.body.body[0].expression.left.object.type==="Identifier"&&s.arguments[0].alternate.body.body[0].expression.left.object.name==="module"&&s.arguments[0].alternate.body.body[0].expression.left.property.type==="Identifier"&&s.arguments[0].alternate.body.body[0].expression.left.property.name==="exports"&&s.arguments[0].alternate.body.body[0].expression.left.computed===false&&s.arguments[0].alternate.body.body[0].expression.right.type==="CallExpression"&&s.arguments[0].alternate.body.body[0].expression.right.callee.type==="Identifier"&&s.arguments[0].alternate.body.body[0].expression.right.callee.name===s.arguments[0].alternate.params[0].name&&s.arguments[0].alternate.body.body[0].expression.right.arguments.length===1&&s.arguments[0].alternate.body.body[0].expression.right.arguments[0].type==="Identifier"&&s.arguments[0].alternate.body.body[0].expression.right.arguments[0].name==="require"){let e=s.callee.body.body;if(e[0].type==="ExpressionStatement"&&e[0].expression.type==="Literal"&&e[0].expression.value==="use strict"){e=e.slice(1)}if(e.length===1&&e[0].type==="ExpressionStatement"&&e[0].expression.type==="CallExpression"&&e[0].expression.callee.type==="Identifier"&&e[0].expression.callee.name===s.arguments[0].test.right.object.name&&e[0].expression.arguments.length===1&&e[0].expression.arguments[0].type==="FunctionExpression"&&e[0].expression.arguments[0].params.length===1&&e[0].expression.arguments[0].params[0].type==="Identifier"&&e[0].expression.arguments[0].params[0].name==="require"){r.remove(e[0].expression.arguments[0].params[0].start,e[0].expression.arguments[0].params[0].end);n=true}}else if(s.arguments[0].type==="FunctionExpression"&&s.arguments[0].params.length===0&&(s.arguments[0].body.body.length===1||s.arguments[0].body.body.length===2&&s.arguments[0].body.body[0].type==="VariableDeclaration"&&s.arguments[0].body.body[0].declarations.length===3&&s.arguments[0].body.body[0].declarations.every(e=>e.init===null&&e.id.type==="Identifier"))&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].type==="ReturnStatement"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.type==="CallExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.type==="CallExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.arguments.length&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.arguments.every(e=>e.type==="Literal"&&typeof e.value==="number")&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.callee.type==="CallExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.callee.callee.type==="FunctionExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.callee.arguments.length===0&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.arguments.length===3&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.arguments[0].type==="ObjectExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.arguments[1].type==="ObjectExpression"&&s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.arguments[2].type==="ArrayExpression"){const e=s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.arguments[0].properties;const t=s.arguments[0].body.body[s.arguments[0].body.body.length-1].argument.callee.callee.callee.body.body[0];let i;if(t.type==="FunctionDeclaration")i=t.body;else if(t.type==="ReturnStatement")i=t.argument.body;if(i){const e=i.body[0].body.body[0].consequent.body[0].consequent.body[0].declarations[0].init;const t=i.body[1].init.declarations[0].init;e.right.name="_";t.right.name="_";r.overwrite(e.start,e.end,"__non_webpack_require__");r.overwrite(t.start,t.end,"__non_webpack_require__");n=true}const a={};if(e.every(e=>{if(e.type!=="Property"||e.computed!==false||e.key.type!=="Literal"||typeof e.key.value!=="number"||e.value.type!=="ArrayExpression"||e.value.elements.length!==2||e.value.elements[0].type!=="FunctionExpression"||e.value.elements[1].type!=="ObjectExpression")return false;const t=e.value.elements[1].properties;for(const e of t){if(e.type!=="Property"||e.value.type!=="Identifier"&&e.value.type!=="Literal"||e.key.type!=="Literal"||typeof e.key.value!=="string"||e.computed)return false;if(e.value.type==="Identifier"&&e.value.name==="undefined")a[e.key.value]=true}return true})){const e=Object.keys(a);if(e.length){const t=s.arguments[0].body.body[1].argument.callee.arguments[1];const i=e.map(e=>`"${e}": { exports: require("${e}") }`).join(",\n  ");r.appendRight(t.end-1,i);n=true}}}else if(s.arguments[0].type==="FunctionExpression"&&s.arguments[0].params.length===2&&s.arguments[0].params[0].type==="Identifier"&&s.arguments[0].params[1].type==="Identifier"&&s.callee.body.body.length===1){const e=s.callee.body.body[0];if(e.type==="IfStatement"&&e.test.type==="LogicalExpression"&&e.test.operator==="&&"&&e.test.left.type==="BinaryExpression"&&e.test.left.left.type==="UnaryExpression"&&e.test.left.left.operator==="typeof"&&e.test.left.left.argument.type==="Identifier"&&e.test.left.left.argument.name==="module"&&e.test.left.right.type==="Literal"&&e.test.left.right.value==="object"&&e.test.right.type==="BinaryExpression"&&e.test.right.left.type==="UnaryExpression"&&e.test.right.left.operator==="typeof"&&e.test.right.left.argument.type==="MemberExpression"&&e.test.right.left.argument.object.type==="Identifier"&&e.test.right.left.argument.object.name==="module"&&e.test.right.left.argument.property.type==="Identifier"&&e.test.right.left.argument.property.name==="exports"&&e.test.right.right.type==="Literal"&&e.test.right.right.value==="object"&&e.consequent.type==="BlockStatement"&&e.consequent.body.length>0){let t;if(e.consequent.body[0].type==="VariableDeclaration"&&e.consequent.body[0].declarations[0].init&&e.consequent.body[0].declarations[0].init.type==="CallExpression")t=e.consequent.body[0].declarations[0].init;else if(e.consequent.body[0].type==="ExpressionStatement"&&e.consequent.body[0].expression.type==="CallExpression")t=e.consequent.body[0].expression;else if(e.consequent.body[0].type==="ExpressionStatement"&&e.consequent.body[0].expression.type==="AssignmentExpression"&&e.consequent.body[0].expression.right.type==="CallExpression")t=e.consequent.body[0].expression.right;if(t&&t.callee.type==="Identifier"&&t.callee.name===s.callee.params[0].name&&t.arguments.length===2&&t.arguments[0].type==="Identifier"&&t.arguments[0].name==="require"&&t.arguments[1].type==="Identifier"&&t.arguments[1].name==="exports"){r.remove(s.arguments[0].params[0].start,s.arguments[0].params[s.arguments[0].params.length-1].end);n=true}}}else if(s.callee.type==="FunctionExpression"&&s.callee.params.length===1&&s.callee.body.body.length>2&&s.callee.body.body[0].type==="VariableDeclaration"&&s.callee.body.body[0].declarations.length===1&&s.callee.body.body[0].declarations[0].type==="VariableDeclarator"&&s.callee.body.body[0].declarations[0].id.type==="Identifier"&&s.callee.body.body[0].declarations[0].init.type==="ObjectExpression"&&s.callee.body.body[0].declarations[0].init.properties.length===0&&s.callee.body.body[1].type==="FunctionDeclaration"&&s.callee.body.body[1].params.length===1&&s.callee.body.body[1].body.body.length===3&&s.arguments[0].type==="ArrayExpression"&&s.arguments[0].elements.length>0&&s.arguments[0].elements.every(e=>e.type==="FunctionExpression")){const e=new Map;for(let t=0;t<s.arguments[0].elements.length;t++){const r=s.arguments[0].elements[t];if(r.body.body.length===1&&r.body.body[0].type==="ExpressionStatement"&&r.body.body[0].expression.type==="AssignmentExpression"&&r.body.body[0].expression.operator==="="&&r.body.body[0].expression.left.type==="MemberExpression"&&r.body.body[0].expression.left.object.type==="Identifier"&&r.body.body[0].expression.left.object.name===r.params[0].name&&r.body.body[0].expression.left.property.type==="Identifier"&&r.body.body[0].expression.left.property.name==="exports"&&r.body.body[0].expression.right.type==="CallExpression"&&r.body.body[0].expression.right.callee.type==="Identifier"&&r.body.body[0].expression.right.callee.name==="require"&&r.body.body[0].expression.right.arguments.length===1&&r.body.body[0].expression.right.arguments[0].type==="Literal"){e.set(t,r.body.body[0].expression.right.arguments[0].value)}}for(let t=0;t<s.arguments[0].elements.length;t++){const a=s.arguments[0].elements[t];if(a.params.length===3&&a.params[2].type==="Identifier"){i(a.body.body,{enter(t,i){if(t.type==="FunctionExpression"||t.type==="FunctionDeclaration"||t.type==="ArrowFunctionExpression"||t.type==="BlockStatement"||t.type==="TryStatement"){if(i)return this.skip()}if(t.type==="CallExpression"&&t.callee.type==="Identifier"&&t.callee.name===a.params[2].name&&t.arguments.length===1&&t.arguments[0].type==="Literal"){const s=e.get(t.arguments[0].value);if(s){const e={type:"CallExpression",callee:{type:"Identifier",name:"require"},arguments:[{type:"Literal",value:s}]};r.overwrite(t.start,t.end,`require(${JSON.stringify(s)})`);n=true;if(i.right===t)i.right=e;else if(i.left===t)i.left=e;else if(i.object===t)i.object=e;else if(i.callee===t)i.callee=e;else if(i.arguments&&i.arguments.some(e=>e===t))i.arguments=i.arguments.map(r=>r===t?e:r);else if(i.init===t)i.init=e}}}})}}}}return{ast:e,scope:t,transformed:n}}e.exports=handleWrappers},714:function(e){if(typeof Object.create==="function"){e.exports=function inherits(e,t){e.super_=t;e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}})}}else{e.exports=function inherits(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype;e.prototype=new r;e.prototype.constructor=e}}},722:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=r(287);var n=_interopRequireDefault(i);var s=r(809);var a=_interopRequireDefault(s);function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}t.default={parse:n.default,stringify:a.default};e.exports=t["default"]},735:function(e){"use strict";e.exports=process},737:function(e,t,r){"use strict";var i=r(645);var n=r(873);var s=r(513);var a=r(353);var u=r(254);var o=r(314);var l=r(735);var f=r(998);e.exports=Gauge;function callWith(e,t){return function(){return t.call(e)}}function Gauge(e,t){var r,n;if(e&&e.write){n=e;r=t||{}}else if(t&&t.write){n=t;r=e||{}}else{n=l.stderr;r=e||t||{}}this._status={spun:0,section:"",subsection:""};this._paused=false;this._disabled=true;this._showing=false;this._onScreen=false;this._needsRedraw=false;this._hideCursor=r.hideCursor==null?true:r.hideCursor;this._fixedFramerate=r.fixedFramerate==null?!/^v0\.8\./.test(l.version):r.fixedFramerate;this._lastUpdateAt=null;this._updateInterval=r.updateInterval==null?50:r.updateInterval;this._themes=r.themes||u;this._theme=r.theme;var s=this._computeTheme(r.theme);var a=r.template||[{type:"progressbar",length:20},{type:"activityIndicator",kerning:1,length:1},{type:"section",kerning:1,default:""},{type:"subsection",kerning:1,default:""}];this.setWriteTo(n,r.tty);var o=r.Plumbing||i;this._gauge=new o(s,a,this.getWidth());this._$$doRedraw=callWith(this,this._doRedraw);this._$$handleSizeChange=callWith(this,this._handleSizeChange);this._cleanupOnExit=r.cleanupOnExit==null||r.cleanupOnExit;this._removeOnExit=null;if(r.enabled||r.enabled==null&&this._tty&&this._tty.isTTY){this.enable()}else{this.disable()}}Gauge.prototype={};Gauge.prototype.isEnabled=function(){return!this._disabled};Gauge.prototype.setTemplate=function(e){this._gauge.setTemplate(e);if(this._showing)this._requestRedraw()};Gauge.prototype._computeTheme=function(e){if(!e)e={};if(typeof e==="string"){e=this._themes.getTheme(e)}else if(e&&(Object.keys(e).length===0||e.hasUnicode!=null||e.hasColor!=null)){var t=e.hasUnicode==null?n():e.hasUnicode;var r=e.hasColor==null?s:e.hasColor;e=this._themes.getDefault({hasUnicode:t,hasColor:r,platform:e.platform})}return e};Gauge.prototype.setThemeset=function(e){this._themes=e;this.setTheme(this._theme)};Gauge.prototype.setTheme=function(e){this._gauge.setTheme(this._computeTheme(e));if(this._showing)this._requestRedraw();this._theme=e};Gauge.prototype._requestRedraw=function(){this._needsRedraw=true;if(!this._fixedFramerate)this._doRedraw()};Gauge.prototype.getWidth=function(){return(this._tty&&this._tty.columns||80)-1};Gauge.prototype.setWriteTo=function(e,t){var r=!this._disabled;if(r)this.disable();this._writeTo=e;this._tty=t||e===l.stderr&&l.stdout.isTTY&&l.stdout||e.isTTY&&e||this._tty;if(this._gauge)this._gauge.setWidth(this.getWidth());if(r)this.enable()};Gauge.prototype.enable=function(){if(!this._disabled)return;this._disabled=false;if(this._tty)this._enableEvents();if(this._showing)this.show()};Gauge.prototype.disable=function(){if(this._disabled)return;if(this._showing){this._lastUpdateAt=null;this._showing=false;this._doRedraw();this._showing=true}this._disabled=true;if(this._tty)this._disableEvents()};Gauge.prototype._enableEvents=function(){if(this._cleanupOnExit){this._removeOnExit=a(callWith(this,this.disable))}this._tty.on("resize",this._$$handleSizeChange);if(this._fixedFramerate){this.redrawTracker=o(this._$$doRedraw,this._updateInterval);if(this.redrawTracker.unref)this.redrawTracker.unref()}};Gauge.prototype._disableEvents=function(){this._tty.removeListener("resize",this._$$handleSizeChange);if(this._fixedFramerate)clearInterval(this.redrawTracker);if(this._removeOnExit)this._removeOnExit()};Gauge.prototype.hide=function(e){if(this._disabled)return e&&l.nextTick(e);if(!this._showing)return e&&l.nextTick(e);this._showing=false;this._doRedraw();e&&f(e)};Gauge.prototype.show=function(e,t){this._showing=true;if(typeof e==="string"){this._status.section=e}else if(typeof e==="object"){var r=Object.keys(e);for(var i=0;i<r.length;++i){var n=r[i];this._status[n]=e[n]}}if(t!=null)this._status.completed=t;if(this._disabled)return;this._requestRedraw()};Gauge.prototype.pulse=function(e){this._status.subsection=e||"";this._status.spun++;if(this._disabled)return;if(!this._showing)return;this._requestRedraw()};Gauge.prototype._handleSizeChange=function(){this._gauge.setWidth(this._tty.columns-1);this._requestRedraw()};Gauge.prototype._doRedraw=function(){if(this._disabled||this._paused)return;if(!this._fixedFramerate){var e=Date.now();if(this._lastUpdateAt&&e-this._lastUpdateAt<this._updateInterval)return;this._lastUpdateAt=e}if(!this._showing&&this._onScreen){this._onScreen=false;var t=this._gauge.hide();if(this._hideCursor){t+=this._gauge.showCursor()}return this._writeTo.write(t)}if(!this._showing&&!this._onScreen)return;if(this._showing&&!this._onScreen){this._onScreen=true;this._needsRedraw=true;if(this._hideCursor){this._writeTo.write(this._gauge.hideCursor())}}if(!this._needsRedraw)return;if(!this._writeTo.write(this._gauge.show(this._status))){this._paused=true;this._writeTo.on("drain",callWith(this,function(){this._paused=false;this._doRedraw()}))}}},743:function(e,t,r){"use strict";const i=r(500);e.exports=(e=>typeof e==="string"?e.replace(i(),""):e)},745:function(e){e.exports=function(e){[process.stdout,process.stderr].forEach(function(t){if(t._handle&&t.isTTY&&typeof t._handle.setBlocking==="function"){t._handle.setBlocking(e)}})}},755:function(e,t,r){const{existsSync:i}=r(66);const{dirname:n}=r(589);e.exports=function getPackageScope(e){let t=n(e);do{e=t;t=n(e);if(i(e+"/package.json"))return e}while(e!==t)}},764:function(e){e.exports=function(e,t){return t||{}}},765:function(e,t,r){"use strict";var i=r(982);var n=r(814);var s=r(156);var a=r(649);var u=r(205);var o=r(433);function renderValueWithValues(e){return function(t){return renderValue(t,e)}}var l=e.exports=function(e,t,r){var n=prepareItems(e,t,r);var s=n.map(renderValueWithValues(r)).join("");return i.left(a(s,e),e)};function preType(e){var t=e.type[0].toUpperCase()+e.type.slice(1);return"pre"+t}function postType(e){var t=e.type[0].toUpperCase()+e.type.slice(1);return"post"+t}function hasPreOrPost(e,t){if(!e.type)return;return t[preType(e)]||t[postType(e)]}function generatePreAndPost(e,t){var r=s({},e);var i=Object.create(t);var n=[];var a=preType(r);var u=postType(r);if(i[a]){n.push({value:i[a]});i[a]=null}r.minLength=null;r.length=null;r.maxLength=null;n.push(r);i[r.type]=i[r.type];if(i[u]){n.push({value:i[u]});i[u]=null}return function(e,t,r){return l(r,n,i)}}function prepareItems(e,t,r){function cloneAndObjectify(t,i,n){var s=new o(t,e);var a=s.type;if(s.value==null){if(!(a in r)){if(s.default==null){throw new u.MissingTemplateValue(s,r)}else{s.value=s.default}}else{s.value=r[a]}}if(s.value==null||s.value==="")return null;s.index=i;s.first=i===0;s.last=i===n.length-1;if(hasPreOrPost(s,r))s.value=generatePreAndPost(s,r);return s}var i=t.map(cloneAndObjectify).filter(function(e){return e!=null});var n=0;var s=e;var a=i.length;function consumeSpace(e){if(e>s)e=s;n+=e;s-=e}function finishSizing(e,t){if(e.finished)throw new u.Internal("Tried to finish template item that was already finished");if(t===Infinity)throw new u.Internal("Length of template item cannot be infinity");if(t!=null)e.length=t;e.minLength=null;e.maxLength=null;--a;e.finished=true;if(e.length==null)e.length=e.getBaseLength();if(e.length==null)throw new u.Internal("Finished template items must have a length");consumeSpace(e.getLength())}i.forEach(function(e){if(!e.kerning)return;var t=e.first?0:i[e.index-1].padRight;if(!e.first&&t<e.kerning)e.padLeft=e.kerning-t;if(!e.last)e.padRight=e.kerning});i.forEach(function(e){if(e.getBaseLength()==null)return;finishSizing(e)});var l=0;var f;var h;do{f=false;h=Math.round(s/a);i.forEach(function(e){if(e.finished)return;if(!e.maxLength)return;if(e.getMaxLength()<h){finishSizing(e,e.maxLength);f=true}})}while(f&&l++<i.length);if(f)throw new u.Internal("Resize loop iterated too many times while determining maxLength");l=0;do{f=false;h=Math.round(s/a);i.forEach(function(e){if(e.finished)return;if(!e.minLength)return;if(e.getMinLength()>=h){finishSizing(e,e.minLength);f=true}})}while(f&&l++<i.length);if(f)throw new u.Internal("Resize loop iterated too many times while determining minLength");h=Math.round(s/a);i.forEach(function(e){if(e.finished)return;finishSizing(e,h)});return i}function renderFunction(e,t,r){n("OON",arguments);if(e.type){return e.value(t,t[e.type+"Theme"]||{},r)}else{return e.value(t,{},r)}}function renderValue(e,t){var r=e.getBaseLength();var n=typeof e.value==="function"?renderFunction(e,t,r):e.value;if(n==null||n==="")return"";var s=i[e.align]||i.left;var u=e.padLeft?i.left("",e.padLeft):"";var o=e.padRight?i.right("",e.padRight):"";var l=a(String(n),r);var f=s(l,r);return u+f+o}},768:function(e){"use strict";function parseString(e){try{if(e[0]==='"'){return JSON.parse(e)}if(e[0]==="'"&&e.substr(e.length-1)==="'"){return parseString(e.replace(/\\.|"/g,e=>e==='"'?'\\"':e).replace(/^'|'$/g,'"'))}return JSON.parse('"'+e+'"')}catch(t){return e}}e.exports=parseString},779:function(e,t,r){const i=r(431);const n=r(813);const s=r(327);const a=r(589);let u;switch(i.platform()){case"darwin":u="/**/*.@(dylib|so?(.*))";break;case"win32":u="/**/*.dll";break;default:u="/**/*.so?(.*)"}e.exports=async function(e,t,r,i,o){const l=await new Promise((t,r)=>s(e+u,{ignore:"node_modules/**/*"},(e,i)=>e?r(e):t(i)));await Promise.all(l.map(async s=>{const[u,l]=await Promise.all([new Promise((e,t)=>n.readFile(s,(r,i)=>r?t(r):e(i))),await new Promise((e,t)=>n.lstat(s,(r,i)=>r?t(r):e(i)))]);if(l.isSymbolicLink()){const i=await new Promise((e,t)=>{n.readlink(s,(r,i)=>r?t(r):e(i))});const u=a.dirname(s);t.assetSymlinks[r+s.substr(e.length+1)]=a.relative(u,a.resolve(u,i))}else{t.assetPermissions[s.substr(e.length)]=l.mode;if(o)console.log("Emitting "+s+" for shared library support in "+e);i(r+s.substr(e.length+1),u)}}))}},781:function(__unusedmodule,exports,__nested_webpack_require_585035__){const path=__nested_webpack_require_585035__(589);const fs=__nested_webpack_require_585035__(66);const versioning=__nested_webpack_require_585035__(503);const napi=__nested_webpack_require_585035__(445);const pregypFind=(e,t)=>{const r=JSON.parse(fs.readFileSync(e).toString());versioning.validate_config(r,t);var i;if(napi.get_napi_build_versions(r,t)){i=napi.get_best_napi_build_version(r,t)}t=t||{};if(!t.module_root)t.module_root=path.dirname(e);var n=versioning.evaluate(r,t,i);return n.module};exports.pregyp={default:{find:pregypFind},find:pregypFind};function makeModulePathList(e,t){return[[e,t],[e,"build",t],[e,"build","Debug",t],[e,"build","Release",t],[e,"out","Debug",t],[e,"Debug",t],[e,"out","Release",t],[e,"Release",t],[e,"build","default",t],[e,process.env["NODE_BINDINGS_COMPILED_DIR"]||"compiled",process.versions.node,process.platform,process.arch,t]]}function findCompiledModule(basePath,specList){var resolvedList=[];var ext=path.extname(basePath);for(var _i=0,specList_1=specList;_i<specList_1.length;_i++){var spec=specList_1[_i];if(ext==spec.ext){try{spec.path=eval("require.resolve(basePath)");return spec}catch(e){resolvedList.push(basePath)}}}for(var _a=0,specList_2=specList;_a<specList_2.length;_a++){var spec=specList_2[_a];for(var _b=0,_c=makeModulePathList(basePath,spec.name);_b<_c.length;_b++){var pathParts=_c[_b];var resolvedPath=path.resolve.apply(path,pathParts);try{spec.path=eval("require.resolve(resolvedPath)")}catch(e){resolvedList.push(resolvedPath);continue}return spec}}return null}function find(e=process.cwd()){const t=findCompiledModule(e,[{ext:".node",name:"nbind.node",type:"node"},{ext:".js",name:"nbind.js",type:"emcc"}]);return t}exports.nbind=find},807:function(e,t,r){var i=r(589);var n=i.parse||r(946);var s=function getNodeModulesDirs(e,t){var r="/";if(/^([A-Za-z]:)/.test(e)){r=""}else if(/^\\\\/.test(e)){r="\\\\"}var s=[e];var a=n(e);while(a.dir!==s[s.length-1]){s.push(a.dir);a=n(a.dir)}return s.reduce(function(e,n){return e.concat(t.map(function(e){return i.join(r,n,e)}))},[])};e.exports=function nodeModulesPaths(e,t,r){var i=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"];if(t&&typeof t.paths==="function"){return t.paths(r,e,function(){return s(e,i)},t)}var n=s(e,i);return t&&t.paths?n.concat(t.paths):n}},809:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=stringify;var n=r(320);var s=_interopRequireWildcard(n);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function stringify(e,t,r){var n=[];var a="";var u=void 0;var o=void 0;var l="";var f=void 0;if(t!=null&&(typeof t==="undefined"?"undefined":i(t))==="object"&&!Array.isArray(t)){r=t.space;f=t.quote;t=t.replacer}if(typeof t==="function"){o=t}else if(Array.isArray(t)){u=[];var h=true;var c=false;var p=undefined;try{for(var d=t[Symbol.iterator](),v;!(h=(v=d.next()).done);h=true){var y=v.value;var g=void 0;if(typeof y==="string"){g=y}else if(typeof y==="number"||y instanceof String||y instanceof Number){g=String(y)}if(g!==undefined&&u.indexOf(g)<0){u.push(g)}}}catch(e){c=true;p=e}finally{try{if(!h&&d.return){d.return()}}finally{if(c){throw p}}}}if(r instanceof Number){r=Number(r)}else if(r instanceof String){r=String(r)}if(typeof r==="number"){if(r>0){r=Math.min(10,Math.floor(r));l="          ".substr(0,r)}}else if(typeof r==="string"){l=r.substr(0,10)}return serializeProperty("",{"":e});function serializeProperty(e,t){var r=t[e];if(r!=null){if(typeof r.toJSON5==="function"){r=r.toJSON5(e)}else if(typeof r.toJSON==="function"){r=r.toJSON(e)}}if(o){r=o.call(t,e,r)}if(r instanceof Number){r=Number(r)}else if(r instanceof String){r=String(r)}else if(r instanceof Boolean){r=r.valueOf()}switch(r){case null:return"null";case true:return"true";case false:return"false"}if(typeof r==="string"){return quoteString(r,false)}if(typeof r==="number"){return String(r)}if((typeof r==="undefined"?"undefined":i(r))==="object"){return Array.isArray(r)?serializeArray(r):serializeObject(r)}return undefined}function quoteString(e){var t={"'":.1,'"':.2};var r={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};var i="";var n=true;var s=false;var a=undefined;try{for(var u=e[Symbol.iterator](),o;!(n=(o=u.next()).done);n=true){var l=o.value;switch(l){case"'":case'"':t[l]++;i+=l;continue}if(r[l]){i+=r[l];continue}if(l<" "){var h=l.charCodeAt(0).toString(16);i+="\\x"+("00"+h).substring(h.length);continue}i+=l}}catch(e){s=true;a=e}finally{try{if(!n&&u.return){u.return()}}finally{if(s){throw a}}}var c=f||Object.keys(t).reduce(function(e,r){return t[e]<t[r]?e:r});i=i.replace(new RegExp(c,"g"),r[c]);return c+i+c}function serializeObject(e){if(n.indexOf(e)>=0){throw TypeError("Converting circular structure to JSON5")}n.push(e);var t=a;a=a+l;var r=u||Object.keys(e);var i=[];var s=true;var o=false;var f=undefined;try{for(var h=r[Symbol.iterator](),c;!(s=(c=h.next()).done);s=true){var p=c.value;var d=serializeProperty(p,e);if(d!==undefined){var v=serializeKey(p)+":";if(l!==""){v+=" "}v+=d;i.push(v)}}}catch(e){o=true;f=e}finally{try{if(!s&&h.return){h.return()}}finally{if(o){throw f}}}var y=void 0;if(i.length===0){y="{}"}else{var g=void 0;if(l===""){g=i.join(",");y="{"+g+"}"}else{var b=",\n"+a;g=i.join(b);y="{\n"+a+g+",\n"+t+"}"}}n.pop();a=t;return y}function serializeKey(e){if(e.length===0){return quoteString(e,true)}var t=String.fromCodePoint(e.codePointAt(0));if(!s.isIdStartChar(t)){return quoteString(e,true)}for(var r=t.length;r<e.length;r++){if(!s.isIdContinueChar(String.fromCodePoint(e.codePointAt(r)))){return quoteString(e,true)}}return e}function serializeArray(e){if(n.indexOf(e)>=0){throw TypeError("Converting circular structure to JSON5")}n.push(e);var t=a;a=a+l;var r=[];for(var i=0;i<e.length;i++){var s=serializeProperty(String(i),e);r.push(s!==undefined?s:"null")}var u=void 0;if(r.length===0){u="[]"}else{if(l===""){var o=r.join(",");u="["+o+"]"}else{var f=",\n"+a;var h=r.join(f);u="[\n"+a+h+",\n"+t+"]"}}n.pop();a=t;return u}}e.exports=t["default"]},813:function(e,t,r){var i=r(66);var n=r(832);var s=r(597);var a=r(840);var u=[];var o=r(64);function noop(){}var l=noop;if(o.debuglog)l=o.debuglog("gfs4");else if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))l=function(){var e=o.format.apply(o,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: ");console.error(e)};if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")){process.on("exit",function(){l(u);r(393).equal(u.length,0)})}e.exports=patch(a(i));if(process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!i.__patched){e.exports=patch(i);i.__patched=true}e.exports.close=function(e){return function(t,r){return e.call(i,t,function(e){if(!e)retry();if(typeof r==="function")r.apply(this,arguments)})}}(i.close);e.exports.closeSync=function(e){return function(t){var r=e.apply(i,arguments);retry();return r}}(i.closeSync);if(!/\bgraceful-fs\b/.test(i.closeSync.toString())){i.closeSync=e.exports.closeSync;i.close=e.exports.close}function patch(e){n(e);e.gracefulify=patch;e.FileReadStream=ReadStream;e.FileWriteStream=WriteStream;e.createReadStream=createReadStream;e.createWriteStream=createWriteStream;var t=e.readFile;e.readFile=readFile;function readFile(e,r,i){if(typeof r==="function")i=r,r=null;return go$readFile(e,r,i);function go$readFile(e,r,i){return t(e,r,function(t){if(t&&(t.code==="EMFILE"||t.code==="ENFILE"))enqueue([go$readFile,[e,r,i]]);else{if(typeof i==="function")i.apply(this,arguments);retry()}})}}var r=e.writeFile;e.writeFile=writeFile;function writeFile(e,t,i,n){if(typeof i==="function")n=i,i=null;return go$writeFile(e,t,i,n);function go$writeFile(e,t,i,n){return r(e,t,i,function(r){if(r&&(r.code==="EMFILE"||r.code==="ENFILE"))enqueue([go$writeFile,[e,t,i,n]]);else{if(typeof n==="function")n.apply(this,arguments);retry()}})}}var i=e.appendFile;if(i)e.appendFile=appendFile;function appendFile(e,t,r,n){if(typeof r==="function")n=r,r=null;return go$appendFile(e,t,r,n);function go$appendFile(e,t,r,n){return i(e,t,r,function(i){if(i&&(i.code==="EMFILE"||i.code==="ENFILE"))enqueue([go$appendFile,[e,t,r,n]]);else{if(typeof n==="function")n.apply(this,arguments);retry()}})}}var a=e.readdir;e.readdir=readdir;function readdir(e,t,r){var i=[e];if(typeof t!=="function"){i.push(t)}else{r=t}i.push(go$readdir$cb);return go$readdir(i);function go$readdir$cb(e,t){if(t&&t.sort)t.sort();if(e&&(e.code==="EMFILE"||e.code==="ENFILE"))enqueue([go$readdir,[i]]);else{if(typeof r==="function")r.apply(this,arguments);retry()}}}function go$readdir(t){return a.apply(e,t)}if(process.version.substr(0,4)==="v0.8"){var u=s(e);ReadStream=u.ReadStream;WriteStream=u.WriteStream}var o=e.ReadStream;if(o){ReadStream.prototype=Object.create(o.prototype);ReadStream.prototype.open=ReadStream$open}var l=e.WriteStream;if(l){WriteStream.prototype=Object.create(l.prototype);WriteStream.prototype.open=WriteStream$open}e.ReadStream=ReadStream;e.WriteStream=WriteStream;function ReadStream(e,t){if(this instanceof ReadStream)return o.apply(this,arguments),this;else return ReadStream.apply(Object.create(ReadStream.prototype),arguments)}function ReadStream$open(){var e=this;open(e.path,e.flags,e.mode,function(t,r){if(t){if(e.autoClose)e.destroy();e.emit("error",t)}else{e.fd=r;e.emit("open",r);e.read()}})}function WriteStream(e,t){if(this instanceof WriteStream)return l.apply(this,arguments),this;else return WriteStream.apply(Object.create(WriteStream.prototype),arguments)}function WriteStream$open(){var e=this;open(e.path,e.flags,e.mode,function(t,r){if(t){e.destroy();e.emit("error",t)}else{e.fd=r;e.emit("open",r)}})}function createReadStream(e,t){return new ReadStream(e,t)}function createWriteStream(e,t){return new WriteStream(e,t)}var f=e.open;e.open=open;function open(e,t,r,i){if(typeof r==="function")i=r,r=null;return go$open(e,t,r,i);function go$open(e,t,r,i){return f(e,t,r,function(n,s){if(n&&(n.code==="EMFILE"||n.code==="ENFILE"))enqueue([go$open,[e,t,r,i]]);else{if(typeof i==="function")i.apply(this,arguments);retry()}})}}return e}function enqueue(e){l("ENQUEUE",e[0].name,e[1]);u.push(e)}function retry(){var e=u.shift();if(e){l("RETRY",e[0].name,e[1]);e[0].apply(null,e[1])}}},814:function(e){"use strict";function isArguments(e){return e!=null&&typeof e==="object"&&e.hasOwnProperty("callee")}var t={"*":{label:"any",check:function(){return true}},A:{label:"array",check:function(e){return Array.isArray(e)||isArguments(e)}},S:{label:"string",check:function(e){return typeof e==="string"}},N:{label:"number",check:function(e){return typeof e==="number"}},F:{label:"function",check:function(e){return typeof e==="function"}},O:{label:"object",check:function(e){return typeof e==="object"&&e!=null&&!t.A.check(e)&&!t.E.check(e)}},B:{label:"boolean",check:function(e){return typeof e==="boolean"}},E:{label:"error",check:function(e){return e instanceof Error}},Z:{label:"null",check:function(e){return e==null}}};function addSchema(e,t){var r=t[e.length]=t[e.length]||[];if(r.indexOf(e)===-1)r.push(e)}var r=e.exports=function(e,r){if(arguments.length!==2)throw wrongNumberOfArgs(["SA"],arguments.length);if(!e)throw missingRequiredArg(0,"rawSchemas");if(!r)throw missingRequiredArg(1,"args");if(!t.S.check(e))throw invalidType(0,["string"],e);if(!t.A.check(r))throw invalidType(1,["array"],r);var i=e.split("|");var n={};i.forEach(function(e){for(var r=0;r<e.length;++r){var i=e[r];if(!t[i])throw unknownType(r,i)}if(/E.*E/.test(e))throw moreThanOneError(e);addSchema(e,n);if(/E/.test(e)){addSchema(e.replace(/E.*$/,"E"),n);addSchema(e.replace(/E/,"Z"),n);if(e.length===1)addSchema("",n)}});var s=n[r.length];if(!s){throw wrongNumberOfArgs(Object.keys(n),r.length)}for(var a=0;a<r.length;++a){var u=s.filter(function(e){var i=e[a];var n=t[i].check;return n(r[a])});if(!u.length){var o=s.map(function(e){return t[e[a]].label}).filter(function(e){return e!=null});throw invalidType(a,o,r[a])}s=u}};function missingRequiredArg(e){return newException("EMISSINGARG","Missing required argument #"+(e+1))}function unknownType(e,t){return newException("EUNKNOWNTYPE","Unknown type "+t+" in argument #"+(e+1))}function invalidType(e,r,i){var n;Object.keys(t).forEach(function(e){if(t[e].check(i))n=t[e].label});return newException("EINVALIDTYPE","Argument #"+(e+1)+": Expected "+englishList(r)+" but got "+n)}function englishList(e){return e.join(", ").replace(/, ([^,]+)$/," or $1")}function wrongNumberOfArgs(e,t){var r=englishList(e);var i=e.every(function(e){return e.length===1})?"argument":"arguments";return newException("EWRONGARGCOUNT","Expected "+r+" "+i+" but got "+t)}function moreThanOneError(e){return newException("ETOOMANYERRORTYPES",'Only one error type per argument signature is allowed, more than one found in "'+e+'"')}function newException(e,t){var i=new Error(t);i.code=e;if(Error.captureStackTrace)Error.captureStackTrace(i,r);return i}},817:function(e){"use strict";e.exports=function(e,t){if(e===null||e===undefined){throw TypeError()}e=String(e);var r=e.length;var i=t?Number(t):0;if(Number.isNaN(i)){i=0}if(i<0||i>=r){return undefined}var n=e.charCodeAt(i);if(n>=55296&&n<=56319&&r>i+1){var s=e.charCodeAt(i+1);if(s>=56320&&s<=57343){return(n-55296)*1024+s-56320+65536}}return n}},825:function(e,t){(function(e,r){true?r(t):0})(this,function(e){"use strict";function walk(e,{enter:t,leave:r}){visit(e,null,t,r)}let t=false;const r={skip:()=>t=true};const i={};const n=Object.prototype.toString;function isArray(e){return n.call(e)==="[object Array]"}function visit(e,n,s,a,u,o){if(!e)return;if(s){const i=t;t=false;s.call(r,e,n,u,o);const a=t;t=i;if(a)return}const l=e.type&&i[e.type]||(i[e.type]=Object.keys(e).filter(t=>typeof e[t]==="object"));for(let t=0;t<l.length;t+=1){const r=l[t];const i=e[r];if(isArray(i)){for(let t=0;t<i.length;t+=1){i[t]&&i[t].type&&visit(i[t],e,s,a,r,t)}}else if(i&&i.type){visit(i,e,s,a,r,null)}}if(a){a(e,n,u,o)}}e.walk=walk;e.childKeys=i;Object.defineProperty(e,"__esModule",{value:true})})},832:function(e,t,r){var i=r(938);var n=process.cwd;var s=null;var a=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){if(!s)s=n.call(process);return s};try{process.cwd()}catch(e){}var u=process.chdir;process.chdir=function(e){s=null;u.call(process,e)};e.exports=patch;function patch(e){if(i.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)){patchLchmod(e)}if(!e.lutimes){patchLutimes(e)}e.chown=chownFix(e.chown);e.fchown=chownFix(e.fchown);e.lchown=chownFix(e.lchown);e.chmod=chmodFix(e.chmod);e.fchmod=chmodFix(e.fchmod);e.lchmod=chmodFix(e.lchmod);e.chownSync=chownFixSync(e.chownSync);e.fchownSync=chownFixSync(e.fchownSync);e.lchownSync=chownFixSync(e.lchownSync);e.chmodSync=chmodFixSync(e.chmodSync);e.fchmodSync=chmodFixSync(e.fchmodSync);e.lchmodSync=chmodFixSync(e.lchmodSync);e.stat=statFix(e.stat);e.fstat=statFix(e.fstat);e.lstat=statFix(e.lstat);e.statSync=statFixSync(e.statSync);e.fstatSync=statFixSync(e.fstatSync);e.lstatSync=statFixSync(e.lstatSync);if(!e.lchmod){e.lchmod=function(e,t,r){if(r)process.nextTick(r)};e.lchmodSync=function(){}}if(!e.lchown){e.lchown=function(e,t,r,i){if(i)process.nextTick(i)};e.lchownSync=function(){}}if(a==="win32"){e.rename=function(t){return function(r,i,n){var s=Date.now();var a=0;t(r,i,function CB(u){if(u&&(u.code==="EACCES"||u.code==="EPERM")&&Date.now()-s<6e4){setTimeout(function(){e.stat(i,function(e,s){if(e&&e.code==="ENOENT")t(r,i,CB);else n(u)})},a);if(a<100)a+=10;return}if(n)n(u)})}}(e.rename)}e.read=function(t){return function(r,i,n,s,a,u){var o;if(u&&typeof u==="function"){var l=0;o=function(f,h,c){if(f&&f.code==="EAGAIN"&&l<10){l++;return t.call(e,r,i,n,s,a,o)}u.apply(this,arguments)}}return t.call(e,r,i,n,s,a,o)}}(e.read);e.readSync=function(t){return function(r,i,n,s,a){var u=0;while(true){try{return t.call(e,r,i,n,s,a)}catch(e){if(e.code==="EAGAIN"&&u<10){u++;continue}throw e}}}}(e.readSync);function patchLchmod(e){e.lchmod=function(t,r,n){e.open(t,i.O_WRONLY|i.O_SYMLINK,r,function(t,i){if(t){if(n)n(t);return}e.fchmod(i,r,function(t){e.close(i,function(e){if(n)n(t||e)})})})};e.lchmodSync=function(t,r){var n=e.openSync(t,i.O_WRONLY|i.O_SYMLINK,r);var s=true;var a;try{a=e.fchmodSync(n,r);s=false}finally{if(s){try{e.closeSync(n)}catch(e){}}else{e.closeSync(n)}}return a}}function patchLutimes(e){if(i.hasOwnProperty("O_SYMLINK")){e.lutimes=function(t,r,n,s){e.open(t,i.O_SYMLINK,function(t,i){if(t){if(s)s(t);return}e.futimes(i,r,n,function(t){e.close(i,function(e){if(s)s(t||e)})})})};e.lutimesSync=function(t,r,n){var s=e.openSync(t,i.O_SYMLINK);var a;var u=true;try{a=e.futimesSync(s,r,n);u=false}finally{if(u){try{e.closeSync(s)}catch(e){}}else{e.closeSync(s)}}return a}}else{e.lutimes=function(e,t,r,i){if(i)process.nextTick(i)};e.lutimesSync=function(){}}}function chmodFix(t){if(!t)return t;return function(r,i,n){return t.call(e,r,i,function(e){if(chownErOk(e))e=null;if(n)n.apply(this,arguments)})}}function chmodFixSync(t){if(!t)return t;return function(r,i){try{return t.call(e,r,i)}catch(e){if(!chownErOk(e))throw e}}}function chownFix(t){if(!t)return t;return function(r,i,n,s){return t.call(e,r,i,n,function(e){if(chownErOk(e))e=null;if(s)s.apply(this,arguments)})}}function chownFixSync(t){if(!t)return t;return function(r,i,n){try{return t.call(e,r,i,n)}catch(e){if(!chownErOk(e))throw e}}}function statFix(t){if(!t)return t;return function(r,i){return t.call(e,r,function(e,t){if(!t)return i.apply(this,arguments);if(t.uid<0)t.uid+=4294967296;if(t.gid<0)t.gid+=4294967296;if(i)i.apply(this,arguments)})}}function statFixSync(t){if(!t)return t;return function(r){var i=t.call(e,r);if(i.uid<0)i.uid+=4294967296;if(i.gid<0)i.gid+=4294967296;return i}}function chownErOk(e){if(!e)return true;if(e.code==="ENOSYS")return true;var t=!process.getuid||process.getuid()!==0;if(t){if(e.code==="EINVAL"||e.code==="EPERM")return true}return false}}},840:function(e){"use strict";e.exports=clone;function clone(e){if(e===null||typeof e!=="object")return e;if(e instanceof Object)var t={__proto__:e.__proto__};else var t=Object.create(null);Object.getOwnPropertyNames(e).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))});return t}},858:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:true});var r=t.Space_Separator=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/;var i=t.ID_Start=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/;var n=t.ID_Continue=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},860:function(e,t,r){"use strict";var i=r(899);var n=function Chunk(e,t,r){this.start=e;this.end=t;this.original=r;this.intro="";this.outro="";this.content=r;this.storeName=false;this.edited=false;Object.defineProperties(this,{previous:{writable:true,value:null},next:{writable:true,value:null}})};n.prototype.appendLeft=function appendLeft(e){this.outro+=e};n.prototype.appendRight=function appendRight(e){this.intro=this.intro+e};n.prototype.clone=function clone(){var e=new n(this.start,this.end,this.original);e.intro=this.intro;e.outro=this.outro;e.content=this.content;e.storeName=this.storeName;e.edited=this.edited;return e};n.prototype.contains=function contains(e){return this.start<e&&e<this.end};n.prototype.eachNext=function eachNext(e){var t=this;while(t){e(t);t=t.next}};n.prototype.eachPrevious=function eachPrevious(e){var t=this;while(t){e(t);t=t.previous}};n.prototype.edit=function edit(e,t,r){this.content=e;if(!r){this.intro="";this.outro=""}this.storeName=t;this.edited=true;return this};n.prototype.prependLeft=function prependLeft(e){this.outro=e+this.outro};n.prototype.prependRight=function prependRight(e){this.intro=e+this.intro};n.prototype.split=function split(e){var t=e-this.start;var r=this.original.slice(0,t);var i=this.original.slice(t);this.original=r;var s=new n(e,this.end,i);s.outro=this.outro;this.outro="";this.end=e;if(this.edited){s.edit("",false);this.content=""}else{this.content=r}s.next=this.next;if(s.next){s.next.previous=s}s.previous=this;this.next=s;return s};n.prototype.toString=function toString(){return this.intro+this.content+this.outro};n.prototype.trimEnd=function trimEnd(e){this.outro=this.outro.replace(e,"");if(this.outro.length){return true}var t=this.content.replace(e,"");if(t.length){if(t!==this.content){this.split(this.start+t.length).edit("",undefined,true)}return true}else{this.edit("",undefined,true);this.intro=this.intro.replace(e,"");if(this.intro.length){return true}}};n.prototype.trimStart=function trimStart(e){this.intro=this.intro.replace(e,"");if(this.intro.length){return true}var t=this.content.replace(e,"");if(t.length){if(t!==this.content){this.split(this.end-t.length);this.edit("",undefined,true)}return true}else{this.edit("",undefined,true);this.outro=this.outro.replace(e,"");if(this.outro.length){return true}}};var s=function(){throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")};if(typeof window!=="undefined"&&typeof window.btoa==="function"){s=function(e){return window.btoa(unescape(encodeURIComponent(e)))}}else if(typeof Buffer==="function"){s=function(e){return Buffer.from(e,"utf-8").toString("base64")}}var a=function SourceMap(e){this.version=3;this.file=e.file;this.sources=e.sources;this.sourcesContent=e.sourcesContent;this.names=e.names;this.mappings=i.encode(e.mappings)};a.prototype.toString=function toString(){return JSON.stringify(this)};a.prototype.toUrl=function toUrl(){return"data:application/json;charset=utf-8;base64,"+s(this.toString())};function guessIndent(e){var t=e.split("\n");var r=t.filter(function(e){return/^\t+/.test(e)});var i=t.filter(function(e){return/^ {2,}/.test(e)});if(r.length===0&&i.length===0){return null}if(r.length>=i.length){return"\t"}var n=i.reduce(function(e,t){var r=/^ +/.exec(t)[0].length;return Math.min(r,e)},Infinity);return new Array(n+1).join(" ")}function getRelativePath(e,t){var r=e.split(/[/\\]/);var i=t.split(/[/\\]/);r.pop();while(r[0]===i[0]){r.shift();i.shift()}if(r.length){var n=r.length;while(n--){r[n]=".."}}return r.concat(i).join("/")}var u=Object.prototype.toString;function isObject(e){return u.call(e)==="[object Object]"}function getLocator(e){var t=e.split("\n");var r=[];for(var i=0,n=0;i<t.length;i++){r.push(n);n+=t[i].length+1}return function locate(e){var t=0;var i=r.length;while(t<i){var n=t+i>>1;if(e<r[n]){i=n}else{t=n+1}}var s=t-1;var a=e-r[s];return{line:s,column:a}}}var o=function Mappings(e){this.hires=e;this.generatedCodeLine=0;this.generatedCodeColumn=0;this.raw=[];this.rawSegments=this.raw[this.generatedCodeLine]=[];this.pending=null};o.prototype.addEdit=function addEdit(e,t,r,i){if(t.length){var n=[this.generatedCodeColumn,e,r.line,r.column];if(i>=0){n.push(i)}this.rawSegments.push(n)}else if(this.pending){this.rawSegments.push(this.pending)}this.advance(t);this.pending=null};o.prototype.addUneditedChunk=function addUneditedChunk(e,t,r,i,n){var s=t.start;var a=true;while(s<t.end){if(this.hires||a||n[s]){this.rawSegments.push([this.generatedCodeColumn,e,i.line,i.column])}if(r[s]==="\n"){i.line+=1;i.column=0;this.generatedCodeLine+=1;this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}else{i.column+=1;this.generatedCodeColumn+=1}s+=1;a=false}this.pending=[this.generatedCodeColumn,e,i.line,i.column]};o.prototype.advance=function advance(e){if(!e){return}var t=e.split("\n");if(t.length>1){for(var r=0;r<t.length-1;r++){this.generatedCodeLine++;this.raw[this.generatedCodeLine]=this.rawSegments=[]}this.generatedCodeColumn=0}this.generatedCodeColumn+=t[t.length-1].length};var l="\n";var f={insertLeft:false,insertRight:false,storeName:false};var h=function MagicString(e,t){if(t===void 0)t={};var r=new n(0,e.length,e);Object.defineProperties(this,{original:{writable:true,value:e},outro:{writable:true,value:""},intro:{writable:true,value:""},firstChunk:{writable:true,value:r},lastChunk:{writable:true,value:r},lastSearchedChunk:{writable:true,value:r},byStart:{writable:true,value:{}},byEnd:{writable:true,value:{}},filename:{writable:true,value:t.filename},indentExclusionRanges:{writable:true,value:t.indentExclusionRanges},sourcemapLocations:{writable:true,value:{}},storedNames:{writable:true,value:{}},indentStr:{writable:true,value:guessIndent(e)}});this.byStart[0]=r;this.byEnd[e.length]=r};h.prototype.addSourcemapLocation=function addSourcemapLocation(e){this.sourcemapLocations[e]=true};h.prototype.append=function append(e){if(typeof e!=="string"){throw new TypeError("outro content must be a string")}this.outro+=e;return this};h.prototype.appendLeft=function appendLeft(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byEnd[e];if(r){r.appendLeft(t)}else{this.intro+=t}return this};h.prototype.appendRight=function appendRight(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byStart[e];if(r){r.appendRight(t)}else{this.outro+=t}return this};h.prototype.clone=function clone(){var e=new h(this.original,{filename:this.filename});var t=this.firstChunk;var r=e.firstChunk=e.lastSearchedChunk=t.clone();while(t){e.byStart[r.start]=r;e.byEnd[r.end]=r;var i=t.next;var n=i&&i.clone();if(n){r.next=n;n.previous=r;r=n}t=i}e.lastChunk=r;if(this.indentExclusionRanges){e.indentExclusionRanges=this.indentExclusionRanges.slice()}Object.keys(this.sourcemapLocations).forEach(function(t){e.sourcemapLocations[t]=true});return e};h.prototype.generateDecodedMap=function generateDecodedMap(e){var t=this;e=e||{};var r=0;var i=Object.keys(this.storedNames);var n=new o(e.hires);var s=getLocator(this.original);if(this.intro){n.advance(this.intro)}this.firstChunk.eachNext(function(e){var a=s(e.start);if(e.intro.length){n.advance(e.intro)}if(e.edited){n.addEdit(r,e.content,a,e.storeName?i.indexOf(e.original):-1)}else{n.addUneditedChunk(r,e,t.original,a,t.sourcemapLocations)}if(e.outro.length){n.advance(e.outro)}});return{file:e.file?e.file.split(/[/\\]/).pop():null,sources:[e.source?getRelativePath(e.file||"",e.source):null],sourcesContent:e.includeContent?[this.original]:[null],names:i,mappings:n.raw}};h.prototype.generateMap=function generateMap(e){return new a(this.generateDecodedMap(e))};h.prototype.getIndentString=function getIndentString(){return this.indentStr===null?"\t":this.indentStr};h.prototype.indent=function indent(e,t){var r=/^[^\r\n]/gm;if(isObject(e)){t=e;e=undefined}e=e!==undefined?e:this.indentStr||"\t";if(e===""){return this}t=t||{};var i={};if(t.exclude){var n=typeof t.exclude[0]==="number"?[t.exclude]:t.exclude;n.forEach(function(e){for(var t=e[0];t<e[1];t+=1){i[t]=true}})}var s=t.indentStart!==false;var a=function(t){if(s){return""+e+t}s=true;return t};this.intro=this.intro.replace(r,a);var u=0;var o=this.firstChunk;while(o){var l=o.end;if(o.edited){if(!i[u]){o.content=o.content.replace(r,a);if(o.content.length){s=o.content[o.content.length-1]==="\n"}}}else{u=o.start;while(u<l){if(!i[u]){var f=this.original[u];if(f==="\n"){s=true}else if(f!=="\r"&&s){s=false;if(u===o.start){o.prependRight(e)}else{this._splitChunk(o,u);o=o.next;o.prependRight(e)}}}u+=1}}u=o.end;o=o.next}this.outro=this.outro.replace(r,a);return this};h.prototype.insert=function insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")};h.prototype.insertLeft=function insertLeft(e,t){if(!f.insertLeft){console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead");f.insertLeft=true}return this.appendLeft(e,t)};h.prototype.insertRight=function insertRight(e,t){if(!f.insertRight){console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead");f.insertRight=true}return this.prependRight(e,t)};h.prototype.move=function move(e,t,r){if(r>=e&&r<=t){throw new Error("Cannot move a selection inside itself")}this._split(e);this._split(t);this._split(r);var i=this.byStart[e];var n=this.byEnd[t];var s=i.previous;var a=n.next;var u=this.byStart[r];if(!u&&n===this.lastChunk){return this}var o=u?u.previous:this.lastChunk;if(s){s.next=a}if(a){a.previous=s}if(o){o.next=i}if(u){u.previous=n}if(!i.previous){this.firstChunk=n.next}if(!n.next){this.lastChunk=i.previous;this.lastChunk.next=null}i.previous=o;n.next=u||null;if(!o){this.firstChunk=i}if(!u){this.lastChunk=n}return this};h.prototype.overwrite=function overwrite(e,t,r,i){if(typeof r!=="string"){throw new TypeError("replacement content must be a string")}while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}if(t>this.original.length){throw new Error("end is out of bounds")}if(e===t){throw new Error("Cannot overwrite a zero-length range – use appendLeft or prependRight instead")}this._split(e);this._split(t);if(i===true){if(!f.storeName){console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string");f.storeName=true}i={storeName:true}}var s=i!==undefined?i.storeName:false;var a=i!==undefined?i.contentOnly:false;if(s){var u=this.original.slice(e,t);this.storedNames[u]=true}var o=this.byStart[e];var l=this.byEnd[t];if(o){if(t>o.end&&o.next!==this.byStart[o.end]){throw new Error("Cannot overwrite across a split point")}o.edit(r,s,a);if(o!==l){var h=o.next;while(h!==l){h.edit("",false);h=h.next}h.edit("",false)}}else{var c=new n(e,t,"").edit(r,s);l.next=c;c.previous=l}return this};h.prototype.prepend=function prepend(e){if(typeof e!=="string"){throw new TypeError("outro content must be a string")}this.intro=e+this.intro;return this};h.prototype.prependLeft=function prependLeft(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byEnd[e];if(r){r.prependLeft(t)}else{this.intro=t+this.intro}return this};h.prototype.prependRight=function prependRight(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byStart[e];if(r){r.prependRight(t)}else{this.outro=t+this.outro}return this};h.prototype.remove=function remove(e,t){while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}if(e===t){return this}if(e<0||t>this.original.length){throw new Error("Character is out of bounds")}if(e>t){throw new Error("end must be greater than start")}this._split(e);this._split(t);var r=this.byStart[e];while(r){r.intro="";r.outro="";r.edit("");r=t>r.end?this.byStart[r.end]:null}return this};h.prototype.lastChar=function lastChar(){if(this.outro.length){return this.outro[this.outro.length-1]}var e=this.lastChunk;do{if(e.outro.length){return e.outro[e.outro.length-1]}if(e.content.length){return e.content[e.content.length-1]}if(e.intro.length){return e.intro[e.intro.length-1]}}while(e=e.previous);if(this.intro.length){return this.intro[this.intro.length-1]}return""};h.prototype.lastLine=function lastLine(){var e=this.outro.lastIndexOf(l);if(e!==-1){return this.outro.substr(e+1)}var t=this.outro;var r=this.lastChunk;do{if(r.outro.length>0){e=r.outro.lastIndexOf(l);if(e!==-1){return r.outro.substr(e+1)+t}t=r.outro+t}if(r.content.length>0){e=r.content.lastIndexOf(l);if(e!==-1){return r.content.substr(e+1)+t}t=r.content+t}if(r.intro.length>0){e=r.intro.lastIndexOf(l);if(e!==-1){return r.intro.substr(e+1)+t}t=r.intro+t}}while(r=r.previous);e=this.intro.lastIndexOf(l);if(e!==-1){return this.intro.substr(e+1)+t}return this.intro+t};h.prototype.slice=function slice(e,t){if(e===void 0)e=0;if(t===void 0)t=this.original.length;while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}var r="";var i=this.firstChunk;while(i&&(i.start>e||i.end<=e)){if(i.start<t&&i.end>=t){return r}i=i.next}if(i&&i.edited&&i.start!==e){throw new Error("Cannot use replaced character "+e+" as slice start anchor.")}var n=i;while(i){if(i.intro&&(n!==i||i.start===e)){r+=i.intro}var s=i.start<t&&i.end>=t;if(s&&i.edited&&i.end!==t){throw new Error("Cannot use replaced character "+t+" as slice end anchor.")}var a=n===i?e-i.start:0;var u=s?i.content.length+t-i.end:i.content.length;r+=i.content.slice(a,u);if(i.outro&&(!s||i.end===t)){r+=i.outro}if(s){break}i=i.next}return r};h.prototype.snip=function snip(e,t){var r=this.clone();r.remove(0,e);r.remove(t,r.original.length);return r};h.prototype._split=function _split(e){if(this.byStart[e]||this.byEnd[e]){return}var t=this.lastSearchedChunk;var r=e>t.end;while(t){if(t.contains(e)){return this._splitChunk(t,e)}t=r?this.byStart[t.end]:this.byEnd[t.start]}};h.prototype._splitChunk=function _splitChunk(e,t){if(e.edited&&e.content.length){var r=getLocator(this.original)(t);throw new Error("Cannot split a chunk that has already been edited ("+r.line+":"+r.column+' – "'+e.original+'")')}var i=e.split(t);this.byEnd[t]=e;this.byStart[t]=i;this.byEnd[i.end]=i;if(e===this.lastChunk){this.lastChunk=i}this.lastSearchedChunk=e;return true};h.prototype.toString=function toString(){var e=this.intro;var t=this.firstChunk;while(t){e+=t.toString();t=t.next}return e+this.outro};h.prototype.isEmpty=function isEmpty(){var e=this.firstChunk;do{if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim()){return false}}while(e=e.next);return true};h.prototype.length=function length(){var e=this.firstChunk;var length=0;do{length+=e.intro.length+e.content.length+e.outro.length}while(e=e.next);return length};h.prototype.trimLines=function trimLines(){return this.trim("[\\r\\n]")};h.prototype.trim=function trim(e){return this.trimStart(e).trimEnd(e)};h.prototype.trimEndAborted=function trimEndAborted(e){var t=new RegExp((e||"\\s")+"+$");this.outro=this.outro.replace(t,"");if(this.outro.length){return true}var r=this.lastChunk;do{var i=r.end;var n=r.trimEnd(t);if(r.end!==i){if(this.lastChunk===r){this.lastChunk=r.next}this.byEnd[r.end]=r;this.byStart[r.next.start]=r.next;this.byEnd[r.next.end]=r.next}if(n){return true}r=r.previous}while(r);return false};h.prototype.trimEnd=function trimEnd(e){this.trimEndAborted(e);return this};h.prototype.trimStartAborted=function trimStartAborted(e){var t=new RegExp("^"+(e||"\\s")+"+");this.intro=this.intro.replace(t,"");if(this.intro.length){return true}var r=this.firstChunk;do{var i=r.end;var n=r.trimStart(t);if(r.end!==i){if(r===this.lastChunk){this.lastChunk=r.next}this.byEnd[r.end]=r;this.byStart[r.next.start]=r.next;this.byEnd[r.next.end]=r.next}if(n){return true}r=r.next}while(r);return false};h.prototype.trimStart=function trimStart(e){this.trimStartAborted(e);return this};var c=Object.prototype.hasOwnProperty;var p=function Bundle(e){if(e===void 0)e={};this.intro=e.intro||"";this.separator=e.separator!==undefined?e.separator:"\n";this.sources=[];this.uniqueSources=[];this.uniqueSourceIndexByFilename={}};p.prototype.addSource=function addSource(e){if(e instanceof h){return this.addSource({content:e,filename:e.filename,separator:this.separator})}if(!isObject(e)||!e.content){throw new Error("bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`")}["filename","indentExclusionRanges","separator"].forEach(function(t){if(!c.call(e,t)){e[t]=e.content[t]}});if(e.separator===undefined){e.separator=this.separator}if(e.filename){if(!c.call(this.uniqueSourceIndexByFilename,e.filename)){this.uniqueSourceIndexByFilename[e.filename]=this.uniqueSources.length;this.uniqueSources.push({filename:e.filename,content:e.content.original})}else{var t=this.uniqueSources[this.uniqueSourceIndexByFilename[e.filename]];if(e.content.original!==t.content){throw new Error("Illegal source: same filename ("+e.filename+"), different contents")}}}this.sources.push(e);return this};p.prototype.append=function append(e,t){this.addSource({content:new h(e),separator:t&&t.separator||""});return this};p.prototype.clone=function clone(){var e=new p({intro:this.intro,separator:this.separator});this.sources.forEach(function(t){e.addSource({filename:t.filename,content:t.content.clone(),separator:t.separator})});return e};p.prototype.generateDecodedMap=function generateDecodedMap(e){var t=this;if(e===void 0)e={};var r=[];this.sources.forEach(function(e){Object.keys(e.content.storedNames).forEach(function(e){if(!~r.indexOf(e)){r.push(e)}})});var i=new o(e.hires);if(this.intro){i.advance(this.intro)}this.sources.forEach(function(e,n){if(n>0){i.advance(t.separator)}var s=e.filename?t.uniqueSourceIndexByFilename[e.filename]:-1;var a=e.content;var u=getLocator(a.original);if(a.intro){i.advance(a.intro)}a.firstChunk.eachNext(function(t){var n=u(t.start);if(t.intro.length){i.advance(t.intro)}if(e.filename){if(t.edited){i.addEdit(s,t.content,n,t.storeName?r.indexOf(t.original):-1)}else{i.addUneditedChunk(s,t,a.original,n,a.sourcemapLocations)}}else{i.advance(t.content)}if(t.outro.length){i.advance(t.outro)}});if(a.outro){i.advance(a.outro)}});return{file:e.file?e.file.split(/[/\\]/).pop():null,sources:this.uniqueSources.map(function(t){return e.file?getRelativePath(e.file,t.filename):t.filename}),sourcesContent:this.uniqueSources.map(function(t){return e.includeContent?t.content:null}),names:r,mappings:i.raw}};p.prototype.generateMap=function generateMap(e){return new a(this.generateDecodedMap(e))};p.prototype.getIndentString=function getIndentString(){var e={};this.sources.forEach(function(t){var r=t.content.indentStr;if(r===null){return}if(!e[r]){e[r]=0}e[r]+=1});return Object.keys(e).sort(function(t,r){return e[t]-e[r]})[0]||"\t"};p.prototype.indent=function indent(e){var t=this;if(!arguments.length){e=this.getIndentString()}if(e===""){return this}var r=!this.intro||this.intro.slice(-1)==="\n";this.sources.forEach(function(i,n){var s=i.separator!==undefined?i.separator:t.separator;var a=r||n>0&&/\r?\n$/.test(s);i.content.indent(e,{exclude:i.indentExclusionRanges,indentStart:a});r=i.content.lastChar()==="\n"});if(this.intro){this.intro=e+this.intro.replace(/^[^\n]/gm,function(t,r){return r>0?e+t:t})}return this};p.prototype.prepend=function prepend(e){this.intro=e+this.intro;return this};p.prototype.toString=function toString(){var e=this;var t=this.sources.map(function(t,r){var i=t.separator!==undefined?t.separator:e.separator;var n=(r>0?i:"")+t.content.toString();return n}).join("");return this.intro+t};p.prototype.isEmpty=function isEmpty(){if(this.intro.length&&this.intro.trim()){return false}if(this.sources.some(function(e){return!e.content.isEmpty()})){return false}return true};p.prototype.length=function length(){return this.sources.reduce(function(e,t){return e+t.content.length()},this.intro.length)};p.prototype.trimLines=function trimLines(){return this.trim("[\\r\\n]")};p.prototype.trim=function trim(e){return this.trimStart(e).trimEnd(e)};p.prototype.trimStart=function trimStart(e){var t=new RegExp("^"+(e||"\\s")+"+");this.intro=this.intro.replace(t,"");if(!this.intro){var r;var i=0;do{r=this.sources[i++];if(!r){break}}while(!r.content.trimStartAborted(e))}return this};p.prototype.trimEnd=function trimEnd(e){var t=new RegExp((e||"\\s")+"+$");var r;var i=this.sources.length-1;do{r=this.sources[i--];if(!r){this.intro=this.intro.replace(t,"");break}}while(!r.content.trimEndAborted(e));return this};h.Bundle=p;h.default=h;e.exports=h},872:function(e){"use strict";e.exports=function(e){return class extends e{readInt(e,t){if(t!=null)return super.readInt(e,t);let r=this.pos,i=0,n=false;for(;;){let t=this.input.charCodeAt(this.pos),r;if(t>=97)r=t-97+10;else if(t==95){if(!n)this.raise(this.pos,"Invalid numeric separator");++this.pos;n=false;continue}else if(t>=65)r=t-65+10;else if(t>=48&&t<=57)r=t-48;else r=Infinity;if(r>=e)break;++this.pos;i=i*e+r;n=true}if(this.pos===r)return null;if(!n)this.raise(this.pos-1,"Invalid numeric separator");return i}readNumber(e){const t=super.readNumber(e);let r=this.end-this.start>=2&&this.input.charCodeAt(this.start)===48;const i=this.getNumberInput(this.start,this.end);if(i.length<this.end-this.start){if(r)this.raise(this.start,"Invalid number");this.value=parseFloat(i)}return t}getNumberInput(e,t){return this.input.slice(e,t).replace(/_/g,"")}}}},873:function(e,t,r){"use strict";var i=r(431);var n=e.exports=function(){if(i.type()=="Windows_NT"){return false}var e=/UTF-?8$/i;var t=process.env.LC_ALL||process.env.LC_CTYPE||process.env.LANG;return e.test(t)}},877:function(e){"use strict";e.exports=Number.isNaN||function(e){return e!==e}},899:function(e,t){(function(e,r){true?r(t):0})(this,function(e){"use strict";var t={};var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";for(var i=0;i<r.length;i++){t[r.charCodeAt(i)]=i}function decode(e){var r=0;var i=0;var n=0;var s=0;var a=0;var u=[];var o=[];var l=[];for(var f=0,h=0,c=0,p=0,d=e.length;f<d;f++){var v=e.charCodeAt(f);if(v===44){if(l.length)o.push(new Int32Array(l));l=[];h=0}else if(v===59){if(l.length)o.push(new Int32Array(l));l=[];h=0;u.push(o);o=[];r=0}else{var y=t[v];if(y===undefined){throw new Error("Invalid character ("+String.fromCharCode(v)+")")}var g=y&32;y&=31;p+=y<<c;if(g){c+=5}else{var b=p&1;p>>=1;var _=b?-p:p;if(h==0){r+=_;l.push(r)}else if(h===1){i+=_;l.push(i)}else if(h===2){n+=_;l.push(n)}else if(h===3){s+=_;l.push(s)}else if(h===4){a+=_;l.push(a)}h++;p=c=0}}}if(l.length)o.push(new Int32Array(l));u.push(o);return u}function encode(e){var t=0;var r=0;var i=0;var n=0;var s="";for(var a=0;a<e.length;a++){var u=e[a];if(a>0)s+=";";if(u.length===0)continue;var o=0;var l=[];for(var f=0,h=u;f<h.length;f++){var c=h[f];var p=encodeInteger(c[0]-o);o=c[0];if(c.length>1){p+=encodeInteger(c[1]-t)+encodeInteger(c[2]-r)+encodeInteger(c[3]-i);t=c[1];r=c[2];i=c[3]}if(c.length===5){p+=encodeInteger(c[4]-n);n=c[4]}l.push(p)}s+=l.join(",")}return s}function encodeInteger(e){var t="";e=e<0?-e<<1|1:e<<1;do{var i=e&31;e>>=5;if(e>0){i|=32}t+=r[i]}while(e>0);return t}e.decode=decode;e.encode=encode;Object.defineProperty(e,"__esModule",{value:true})})},906:function(e,t,r){"use strict";const i=r(996).tokTypes;const n=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;const s=e=>{n.lastIndex=e.pos;let t=n.exec(e.input);let r=e.pos+t[0].length;return e.input.slice(r,r+1)==="."};e.exports=function(e){return class extends e{parseExprAtom(e){if(this.type!==i._import||!s(this))return super.parseExprAtom(e);if(!this.options.allowImportExportEverywhere&&!this.inModule){this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")}let t=this.startNode();if(this.containsEsc)this.raiseRecoverable(this.start,"Escape sequence in keyword import");t.meta=this.parseIdent(true);this.expect(i.dot);t.property=this.parseIdent(true);if(t.property.name!=="meta"){this.raiseRecoverable(t.property.start,"The only valid meta property for import is import.meta")}if(this.containsEsc){this.raiseRecoverable(t.property.start,'"meta" in import.meta must not contain escape sequences')}return this.finishNode(t,"MetaProperty")}parseStatement(e,t,r){if(this.type!==i._import||!s(this)){return super.parseStatement(e,t,r)}let n=this.startNode();let a=this.parseExpression();return this.parseExpressionStatement(n,a)}}}},922:function(e,t,r){t.alphasort=alphasort;t.alphasorti=alphasorti;t.setopts=setopts;t.ownProp=ownProp;t.makeAbs=makeAbs;t.finish=finish;t.mark=mark;t.isIgnored=isIgnored;t.childrenIgnored=childrenIgnored;function ownProp(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var i=r(589);var n=r(620);var s=r(969);var a=n.Minimatch;function alphasorti(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())}function alphasort(e,t){return e.localeCompare(t)}function setupIgnores(e,t){e.ignore=t.ignore||[];if(!Array.isArray(e.ignore))e.ignore=[e.ignore];if(e.ignore.length){e.ignore=e.ignore.map(ignoreMap)}}function ignoreMap(e){var t=null;if(e.slice(-3)==="/**"){var r=e.replace(/(\/\*\*)+$/,"");t=new a(r,{dot:true})}return{matcher:new a(e,{dot:true}),gmatcher:t}}function setopts(e,t,r){if(!r)r={};if(r.matchBase&&-1===t.indexOf("/")){if(r.noglobstar){throw new Error("base matching requires globstar")}t="**/"+t}e.silent=!!r.silent;e.pattern=t;e.strict=r.strict!==false;e.realpath=!!r.realpath;e.realpathCache=r.realpathCache||Object.create(null);e.follow=!!r.follow;e.dot=!!r.dot;e.mark=!!r.mark;e.nodir=!!r.nodir;if(e.nodir)e.mark=true;e.sync=!!r.sync;e.nounique=!!r.nounique;e.nonull=!!r.nonull;e.nosort=!!r.nosort;e.nocase=!!r.nocase;e.stat=!!r.stat;e.noprocess=!!r.noprocess;e.absolute=!!r.absolute;e.maxLength=r.maxLength||Infinity;e.cache=r.cache||Object.create(null);e.statCache=r.statCache||Object.create(null);e.symlinks=r.symlinks||Object.create(null);setupIgnores(e,r);e.changedCwd=false;var n=process.cwd();if(!ownProp(r,"cwd"))e.cwd=n;else{e.cwd=i.resolve(r.cwd);e.changedCwd=e.cwd!==n}e.root=r.root||i.resolve(e.cwd,"/");e.root=i.resolve(e.root);if(process.platform==="win32")e.root=e.root.replace(/\\/g,"/");e.cwdAbs=s(e.cwd)?e.cwd:makeAbs(e,e.cwd);if(process.platform==="win32")e.cwdAbs=e.cwdAbs.replace(/\\/g,"/");e.nomount=!!r.nomount;r.nonegate=true;r.nocomment=true;e.minimatch=new a(t,r);e.options=e.minimatch.options}function finish(e){var t=e.nounique;var r=t?[]:Object.create(null);for(var i=0,n=e.matches.length;i<n;i++){var s=e.matches[i];if(!s||Object.keys(s).length===0){if(e.nonull){var a=e.minimatch.globSet[i];if(t)r.push(a);else r[a]=true}}else{var u=Object.keys(s);if(t)r.push.apply(r,u);else u.forEach(function(e){r[e]=true})}}if(!t)r=Object.keys(r);if(!e.nosort)r=r.sort(e.nocase?alphasorti:alphasort);if(e.mark){for(var i=0;i<r.length;i++){r[i]=e._mark(r[i])}if(e.nodir){r=r.filter(function(t){var r=!/\/$/.test(t);var i=e.cache[t]||e.cache[makeAbs(e,t)];if(r&&i)r=i!=="DIR"&&!Array.isArray(i);return r})}}if(e.ignore.length)r=r.filter(function(t){return!isIgnored(e,t)});e.found=r}function mark(e,t){var r=makeAbs(e,t);var i=e.cache[r];var n=t;if(i){var s=i==="DIR"||Array.isArray(i);var a=t.slice(-1)==="/";if(s&&!a)n+="/";else if(!s&&a)n=n.slice(0,-1);if(n!==t){var u=makeAbs(e,n);e.statCache[u]=e.statCache[r];e.cache[u]=e.cache[r]}}return n}function makeAbs(e,t){var r=t;if(t.charAt(0)==="/"){r=i.join(e.root,t)}else if(s(t)||t===""){r=t}else if(e.changedCwd){r=i.resolve(e.cwd,t)}else{r=i.resolve(t)}if(process.platform==="win32")r=r.replace(/\\/g,"/");return r}function isIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some(function(e){return e.matcher.match(t)||!!(e.gmatcher&&e.gmatcher.match(t))})}function childrenIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some(function(e){return!!(e.gmatcher&&e.gmatcher.match(t))})}},923:function(e,t,r){"use strict";var i=r(12);e.exports=Readable;var n=r(341);var s;Readable.ReadableState=ReadableState;var a=r(485).EventEmitter;var u=function(e,t){return e.listeners(t).length};var o=r(569);var l=r(945).Buffer;var f=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return l.from(e)}function _isUint8Array(e){return l.isBuffer(e)||e instanceof f}var h=r(683);h.inherits=r(207);var c=r(64);var p=void 0;if(c&&c.debuglog){p=c.debuglog("stream")}else{p=function(){}}var d=r(402);var v=r(972);var y;h.inherits(Readable,o);var g=["error","close","destroy","pause","resume"];function prependListener(e,t,r){if(typeof e.prependListener==="function")return e.prependListener(t,r);if(!e._events||!e._events[t])e.on(t,r);else if(n(e._events[t]))e._events[t].unshift(r);else e._events[t]=[r,e._events[t]]}function ReadableState(e,t){s=s||r(98);e=e||{};var i=t instanceof s;this.objectMode=!!e.objectMode;if(i)this.objectMode=this.objectMode||!!e.readableObjectMode;var n=e.highWaterMark;var a=e.readableHighWaterMark;var u=this.objectMode?16:16*1024;if(n||n===0)this.highWaterMark=n;else if(i&&(a||a===0))this.highWaterMark=a;else this.highWaterMark=u;this.highWaterMark=Math.floor(this.highWaterMark);this.buffer=new d;this.length=0;this.pipes=null;this.pipesCount=0;this.flowing=null;this.ended=false;this.endEmitted=false;this.reading=false;this.sync=true;this.needReadable=false;this.emittedReadable=false;this.readableListening=false;this.resumeScheduled=false;this.destroyed=false;this.defaultEncoding=e.defaultEncoding||"utf8";this.awaitDrain=0;this.readingMore=false;this.decoder=null;this.encoding=null;if(e.encoding){if(!y)y=r(421).StringDecoder;this.decoder=new y(e.encoding);this.encoding=e.encoding}}function Readable(e){s=s||r(98);if(!(this instanceof Readable))return new Readable(e);this._readableState=new ReadableState(e,this);this.readable=true;if(e){if(typeof e.read==="function")this._read=e.read;if(typeof e.destroy==="function")this._destroy=e.destroy}o.call(this)}Object.defineProperty(Readable.prototype,"destroyed",{get:function(){if(this._readableState===undefined){return false}return this._readableState.destroyed},set:function(e){if(!this._readableState){return}this._readableState.destroyed=e}});Readable.prototype.destroy=v.destroy;Readable.prototype._undestroy=v.undestroy;Readable.prototype._destroy=function(e,t){this.push(null);t(e)};Readable.prototype.push=function(e,t){var r=this._readableState;var i;if(!r.objectMode){if(typeof e==="string"){t=t||r.defaultEncoding;if(t!==r.encoding){e=l.from(e,t);t=""}i=true}}else{i=true}return readableAddChunk(this,e,t,false,i)};Readable.prototype.unshift=function(e){return readableAddChunk(this,e,null,true,false)};function readableAddChunk(e,t,r,i,n){var s=e._readableState;if(t===null){s.reading=false;onEofChunk(e,s)}else{var a;if(!n)a=chunkInvalid(s,t);if(a){e.emit("error",a)}else if(s.objectMode||t&&t.length>0){if(typeof t!=="string"&&!s.objectMode&&Object.getPrototypeOf(t)!==l.prototype){t=_uint8ArrayToBuffer(t)}if(i){if(s.endEmitted)e.emit("error",new Error("stream.unshift() after end event"));else addChunk(e,s,t,true)}else if(s.ended){e.emit("error",new Error("stream.push() after EOF"))}else{s.reading=false;if(s.decoder&&!r){t=s.decoder.write(t);if(s.objectMode||t.length!==0)addChunk(e,s,t,false);else maybeReadMore(e,s)}else{addChunk(e,s,t,false)}}}else if(!i){s.reading=false}}return needMoreData(s)}function addChunk(e,t,r,i){if(t.flowing&&t.length===0&&!t.sync){e.emit("data",r);e.read(0)}else{t.length+=t.objectMode?1:r.length;if(i)t.buffer.unshift(r);else t.buffer.push(r);if(t.needReadable)emitReadable(e)}maybeReadMore(e,t)}function chunkInvalid(e,t){var r;if(!_isUint8Array(t)&&typeof t!=="string"&&t!==undefined&&!e.objectMode){r=new TypeError("Invalid non-string/buffer chunk")}return r}function needMoreData(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||e.length===0)}Readable.prototype.isPaused=function(){return this._readableState.flowing===false};Readable.prototype.setEncoding=function(e){if(!y)y=r(421).StringDecoder;this._readableState.decoder=new y(e);this._readableState.encoding=e;return this};var b=8388608;function computeNewHighWaterMark(e){if(e>=b){e=b}else{e--;e|=e>>>1;e|=e>>>2;e|=e>>>4;e|=e>>>8;e|=e>>>16;e++}return e}function howMuchToRead(e,t){if(e<=0||t.length===0&&t.ended)return 0;if(t.objectMode)return 1;if(e!==e){if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length}if(e>t.highWaterMark)t.highWaterMark=computeNewHighWaterMark(e);if(e<=t.length)return e;if(!t.ended){t.needReadable=true;return 0}return t.length}Readable.prototype.read=function(e){p("read",e);e=parseInt(e,10);var t=this._readableState;var r=e;if(e!==0)t.emittedReadable=false;if(e===0&&t.needReadable&&(t.length>=t.highWaterMark||t.ended)){p("read: emitReadable",t.length,t.ended);if(t.length===0&&t.ended)endReadable(this);else emitReadable(this);return null}e=howMuchToRead(e,t);if(e===0&&t.ended){if(t.length===0)endReadable(this);return null}var i=t.needReadable;p("need readable",i);if(t.length===0||t.length-e<t.highWaterMark){i=true;p("length less than watermark",i)}if(t.ended||t.reading){i=false;p("reading or ended",i)}else if(i){p("do read");t.reading=true;t.sync=true;if(t.length===0)t.needReadable=true;this._read(t.highWaterMark);t.sync=false;if(!t.reading)e=howMuchToRead(r,t)}var n;if(e>0)n=fromList(e,t);else n=null;if(n===null){t.needReadable=true;e=0}else{t.length-=e}if(t.length===0){if(!t.ended)t.needReadable=true;if(r!==e&&t.ended)endReadable(this)}if(n!==null)this.emit("data",n);return n};function onEofChunk(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();if(r&&r.length){t.buffer.push(r);t.length+=t.objectMode?1:r.length}}t.ended=true;emitReadable(e)}function emitReadable(e){var t=e._readableState;t.needReadable=false;if(!t.emittedReadable){p("emitReadable",t.flowing);t.emittedReadable=true;if(t.sync)i.nextTick(emitReadable_,e);else emitReadable_(e)}}function emitReadable_(e){p("emit readable");e.emit("readable");flow(e)}function maybeReadMore(e,t){if(!t.readingMore){t.readingMore=true;i.nextTick(maybeReadMore_,e,t)}}function maybeReadMore_(e,t){var r=t.length;while(!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark){p("maybeReadMore read 0");e.read(0);if(r===t.length)break;else r=t.length}t.readingMore=false}Readable.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))};Readable.prototype.pipe=function(e,t){var r=this;var n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e);break}n.pipesCount+=1;p("pipe count=%d opts=%j",n.pipesCount,t);var s=(!t||t.end!==false)&&e!==process.stdout&&e!==process.stderr;var a=s?onend:unpipe;if(n.endEmitted)i.nextTick(a);else r.once("end",a);e.on("unpipe",onunpipe);function onunpipe(e,t){p("onunpipe");if(e===r){if(t&&t.hasUnpiped===false){t.hasUnpiped=true;cleanup()}}}function onend(){p("onend");e.end()}var o=pipeOnDrain(r);e.on("drain",o);var l=false;function cleanup(){p("cleanup");e.removeListener("close",onclose);e.removeListener("finish",onfinish);e.removeListener("drain",o);e.removeListener("error",onerror);e.removeListener("unpipe",onunpipe);r.removeListener("end",onend);r.removeListener("end",unpipe);r.removeListener("data",ondata);l=true;if(n.awaitDrain&&(!e._writableState||e._writableState.needDrain))o()}var f=false;r.on("data",ondata);function ondata(t){p("ondata");f=false;var i=e.write(t);if(false===i&&!f){if((n.pipesCount===1&&n.pipes===e||n.pipesCount>1&&indexOf(n.pipes,e)!==-1)&&!l){p("false write response, pause",r._readableState.awaitDrain);r._readableState.awaitDrain++;f=true}r.pause()}}function onerror(t){p("onerror",t);unpipe();e.removeListener("error",onerror);if(u(e,"error")===0)e.emit("error",t)}prependListener(e,"error",onerror);function onclose(){e.removeListener("finish",onfinish);unpipe()}e.once("close",onclose);function onfinish(){p("onfinish");e.removeListener("close",onclose);unpipe()}e.once("finish",onfinish);function unpipe(){p("unpipe");r.unpipe(e)}e.emit("pipe",r);if(!n.flowing){p("pipe resume");r.resume()}return e};function pipeOnDrain(e){return function(){var t=e._readableState;p("pipeOnDrain",t.awaitDrain);if(t.awaitDrain)t.awaitDrain--;if(t.awaitDrain===0&&u(e,"data")){t.flowing=true;flow(e)}}}Readable.prototype.unpipe=function(e){var t=this._readableState;var r={hasUnpiped:false};if(t.pipesCount===0)return this;if(t.pipesCount===1){if(e&&e!==t.pipes)return this;if(!e)e=t.pipes;t.pipes=null;t.pipesCount=0;t.flowing=false;if(e)e.emit("unpipe",this,r);return this}if(!e){var i=t.pipes;var n=t.pipesCount;t.pipes=null;t.pipesCount=0;t.flowing=false;for(var s=0;s<n;s++){i[s].emit("unpipe",this,r)}return this}var a=indexOf(t.pipes,e);if(a===-1)return this;t.pipes.splice(a,1);t.pipesCount-=1;if(t.pipesCount===1)t.pipes=t.pipes[0];e.emit("unpipe",this,r);return this};Readable.prototype.on=function(e,t){var r=o.prototype.on.call(this,e,t);if(e==="data"){if(this._readableState.flowing!==false)this.resume()}else if(e==="readable"){var n=this._readableState;if(!n.endEmitted&&!n.readableListening){n.readableListening=n.needReadable=true;n.emittedReadable=false;if(!n.reading){i.nextTick(nReadingNextTick,this)}else if(n.length){emitReadable(this)}}}return r};Readable.prototype.addListener=Readable.prototype.on;function nReadingNextTick(e){p("readable nexttick read 0");e.read(0)}Readable.prototype.resume=function(){var e=this._readableState;if(!e.flowing){p("resume");e.flowing=true;resume(this,e)}return this};function resume(e,t){if(!t.resumeScheduled){t.resumeScheduled=true;i.nextTick(resume_,e,t)}}function resume_(e,t){if(!t.reading){p("resume read 0");e.read(0)}t.resumeScheduled=false;t.awaitDrain=0;e.emit("resume");flow(e);if(t.flowing&&!t.reading)e.read(0)}Readable.prototype.pause=function(){p("call pause flowing=%j",this._readableState.flowing);if(false!==this._readableState.flowing){p("pause");this._readableState.flowing=false;this.emit("pause")}return this};function flow(e){var t=e._readableState;p("flow",t.flowing);while(t.flowing&&e.read()!==null){}}Readable.prototype.wrap=function(e){var t=this;var r=this._readableState;var i=false;e.on("end",function(){p("wrapped end");if(r.decoder&&!r.ended){var e=r.decoder.end();if(e&&e.length)t.push(e)}t.push(null)});e.on("data",function(n){p("wrapped data");if(r.decoder)n=r.decoder.write(n);if(r.objectMode&&(n===null||n===undefined))return;else if(!r.objectMode&&(!n||!n.length))return;var s=t.push(n);if(!s){i=true;e.pause()}});for(var n in e){if(this[n]===undefined&&typeof e[n]==="function"){this[n]=function(t){return function(){return e[t].apply(e,arguments)}}(n)}}for(var s=0;s<g.length;s++){e.on(g[s],this.emit.bind(this,g[s]))}this._read=function(t){p("wrapped _read",t);if(i){i=false;e.resume()}};return this};Object.defineProperty(Readable.prototype,"readableHighWaterMark",{enumerable:false,get:function(){return this._readableState.highWaterMark}});Readable._fromList=fromList;function fromList(e,t){if(t.length===0)return null;var r;if(t.objectMode)r=t.buffer.shift();else if(!e||e>=t.length){if(t.decoder)r=t.buffer.join("");else if(t.buffer.length===1)r=t.buffer.head.data;else r=t.buffer.concat(t.length);t.buffer.clear()}else{r=fromListPartial(e,t.buffer,t.decoder)}return r}function fromListPartial(e,t,r){var i;if(e<t.head.data.length){i=t.head.data.slice(0,e);t.head.data=t.head.data.slice(e)}else if(e===t.head.data.length){i=t.shift()}else{i=r?copyFromBufferString(e,t):copyFromBuffer(e,t)}return i}function copyFromBufferString(e,t){var r=t.head;var i=1;var n=r.data;e-=n.length;while(r=r.next){var s=r.data;var a=e>s.length?s.length:e;if(a===s.length)n+=s;else n+=s.slice(0,e);e-=a;if(e===0){if(a===s.length){++i;if(r.next)t.head=r.next;else t.head=t.tail=null}else{t.head=r;r.data=s.slice(a)}break}++i}t.length-=i;return n}function copyFromBuffer(e,t){var r=l.allocUnsafe(e);var i=t.head;var n=1;i.data.copy(r);e-=i.data.length;while(i=i.next){var s=i.data;var a=e>s.length?s.length:e;s.copy(r,r.length-e,0,a);e-=a;if(e===0){if(a===s.length){++n;if(i.next)t.head=i.next;else t.head=t.tail=null}else{t.head=i;i.data=s.slice(a)}break}++n}t.length-=n;return r}function endReadable(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');if(!t.endEmitted){t.ended=true;i.nextTick(endReadableNT,t,e)}}function endReadableNT(e,t){if(!e.endEmitted&&e.length===0){e.endEmitted=true;t.readable=false;t.emit("end")}}function indexOf(e,t){for(var r=0,i=e.length;r<i;r++){if(e[r]===t)return r}return-1}},928:function(e){e.exports=function(e,t={},r=true){const i={computeBranches:r,sawIdentifier:false,vars:t};const n=walk(e);return{result:n,sawIdentifier:i.sawIdentifier};function walk(e){const t=s[e.type];if(t)return t.call(i,e,walk)}};const t=e.exports.UNKNOWN=Symbol();const r=e.exports.FUNCTION=Symbol();const i=e.exports.WILDCARD="";const n=e.exports.wildcardRegEx=/\x1a/g;function countWildcards(e){n.lastIndex=0;let t=0;while(n.exec(e))t++;return t}const s={ArrayExpression(e,t){const r=[];for(let i=0,n=e.elements.length;i<n;i++){if(e.elements[i]===null){r.push(null);continue}const n=t(e.elements[i]);if(!n)return;if("value"in n===false)return;r.push(n.value)}return{value:r}},BinaryExpression(e,t){const r=e.operator;let n=t(e.left);if(!n&&r!=="+")return;let s=t(e.right);if(!n&&!s)return;if(!n){if(this.computeBranches&&typeof s.value==="string")return{value:i+s.value,wildcards:[e.left,...s.wildcards||[]]};return}if(!s){if(this.computeBranches&&r==="+"){if(typeof n.value==="string")return{value:n.value+i,wildcards:[...n.wildcards||[],e.right]}}if(!("test"in n)&&r==="||"&&n.value)return e.right;return}if("test"in n&&"test"in s)return;if("test"in n){s=s.value;if(r==="==")return{test:n.test,then:n.then==s,else:n.else==s};if(r==="===")return{test:n.test,then:n.then===s,else:n.else===s};if(r==="!=")return{test:n.test,then:n.then!=s,else:n.else!=s};if(r==="!==")return{test:n.test,then:n.then!==s,else:n.else!==s};if(r==="+")return{test:n.test,then:n.then+s,else:n.else+s};if(r==="-")return{test:n.test,then:n.then-s,else:n.else-s};if(r==="*")return{test:n.test,then:n.then*s,else:n.else*s};if(r==="/")return{test:n.test,then:n.then/s,else:n.else/s};if(r==="%")return{test:n.test,then:n.then%s,else:n.else%s};if(r==="<")return{test:n.test,then:n.then<s,else:n.else<s};if(r==="<=")return{test:n.test,then:n.then<=s,else:n.else<=s};if(r===">")return{test:n.test,then:n.then>s,else:n.else>s};if(r===">=")return{test:n.test,then:n.then>=s,else:n.else>=s};if(r==="|")return{test:n.test,then:n.then|s,else:n.else|s};if(r==="&")return{test:n.test,then:n.then&s,else:n.else&s};if(r==="^")return{test:n.test,then:n.then^s,else:n.else^s};if(r==="&&")return{test:n.test,then:n.then&&s,else:n.else&&s};if(r==="||")return{test:n.test,then:n.then||s,else:n.else||s}}else if("test"in s){n=n.value;if(r==="==")return{test:s.test,then:n==s.then,else:n==s.else};if(r==="===")return{test:s.test,then:n===s.then,else:n===s.else};if(r==="!=")return{test:s.test,then:n!=s.then,else:n!=s.else};if(r==="!==")return{test:s.test,then:n!==s.then,else:n!==s.else};if(r==="+")return{test:s.test,then:n+s.then,else:n+s.else};if(r==="-")return{test:s.test,then:n-s.then,else:n-s.else};if(r==="*")return{test:s.test,then:n*s.then,else:n*s.else};if(r==="/")return{test:s.test,then:n/s.then,else:n/s.else};if(r==="%")return{test:s.test,then:n%s.then,else:n%s.else};if(r==="<")return{test:s.test,then:n<s.then,else:n<s.else};if(r==="<=")return{test:s.test,then:n<=s.then,else:n<=s.else};if(r===">")return{test:s.test,then:n>s.then,else:n>s.else};if(r===">=")return{test:s.test,then:n>=s.then,else:n>=s.else};if(r==="|")return{test:s.test,then:n|s.then,else:n|s.else};if(r==="&")return{test:s.test,then:n&s.then,else:n&s.else};if(r==="^")return{test:s.test,then:n^s.then,else:n^s.else};if(r==="&&")return{test:s.test,then:n&&s.then,else:n&&s.else};if(r==="||")return{test:s.test,then:n||s.then,else:n||s.else}}else{if(r==="==")return{value:n.value==s.value};if(r==="===")return{value:n.value===s.value};if(r==="!=")return{value:n.value!=s.value};if(r==="!==")return{value:n.value!==s.value};if(r==="+"){const e={value:n.value+s.value};if(n.wildcards||s.wildcards)e.wildcards=[...n.wildcards||[],...s.wildcards||[]];return e}if(r==="-")return{value:n.value-s.value};if(r==="*")return{value:n.value*s.value};if(r==="/")return{value:n.value/s.value};if(r==="%")return{value:n.value%s.value};if(r==="<")return{value:n.value<s.value};if(r==="<=")return{value:n.value<=s.value};if(r===">")return{value:n.value>s.value};if(r===">=")return{value:n.value>=s.value};if(r==="|")return{value:n.value|s.value};if(r==="&")return{value:n.value&s.value};if(r==="^")return{value:n.value^s.value};if(r==="&&")return{value:n.value&&s.value};if(r==="||")return{value:n.value||s.value}}return},CallExpression(e,n){const s=n(e.callee);if(!s||"test"in s)return;let a=s.value;if(typeof a==="object"&&a!==null)a=a[r];if(typeof a!=="function")return;const u=e.callee.object&&n(e.callee.object).value||null;let o;let l=[];let f;let h=e.arguments.length>0;const c=[];for(let t=0,r=e.arguments.length;t<r;t++){let r=n(e.arguments[t]);if(r){h=false;if(typeof r.value==="string"&&r.wildcards)r.wildcards.forEach(e=>c.push(e))}else{if(!this.computeBranches)return;r={value:i};c.push(e.arguments[t])}if("test"in r){if(c.length)return;if(o)return;o=r.test;f=l.concat([]);l.push(r.then);f.push(r.else)}else{l.push(r.value);if(f)f.push(r.value)}}if(h)return;try{const e=a.apply(u,l);if(e===t)return;if(!o){if(c.length){if(typeof e!=="string"||countWildcards(e)!==c.length)return;return{value:e,wildcards:c}}return{value:e}}const r=a.apply(u,f);if(e===t)return;return{test:o,then:e,else:r}}catch(e){return}},ConditionalExpression(e,t){const r=t(e.test);if(r&&"value"in r)return r.value?t(e.consequent):t(e.alternate);if(!this.computeBranches)return;const i=t(e.consequent);if(!i||"wildcards"in i||"test"in i)return;const n=t(e.alternate);if(!n||"wildcards"in n||"test"in n)return;return{test:e.test,then:i.value,else:n.value}},ExpressionStatement(e,t){return t(e.expression)},Identifier(e){this.sawIdentifier=true;if(Object.hasOwnProperty.call(this.vars,e.name)){const r=this.vars[e.name];if(r===t)return;return{value:r}}return},Literal(e){return{value:e.value}},MemberExpression(e,r){const i=r(e.object);if(!i||"test"in i||typeof i.value==="function")return;if(e.property.type==="Identifier"){if(typeof i.value==="object"&&i.value!==null){if(e.property.name in i.value){const r=i.value[e.property.name];if(r===t)return;return{value:r}}else if(i.value[t])return}else{return{value:undefined}}}const n=r(e.property);if(!n||"test"in n)return;if(typeof i.value==="object"&&i.value!==null){if(n.value in i.value){const e=i.value[n.value];if(e===t)return;return{value:e}}else if(i.value[t]){return}}else{return{value:undefined}}},ObjectExpression(e,r){const i={};for(let n=0;n<e.properties.length;n++){const s=e.properties[n];const a=s.computed?r(s.key):s.key&&{value:s.key.name||s.key.value};if(!a||"test"in a)return;const u=r(s.value);if(!u||"test"in u)return;if(u.value===t)return;i[a.value]=u.value}return i},TemplateLiteral(e,t){let r={value:""};for(var n=0;n<e.expressions.length;n++){if("value"in r){r.value+=e.quasis[n].value.cooked}else{r.then+=e.quasis[n].value.cooked;r.else+=e.quasis[n].value.cooked}let s=t(e.expressions[n]);if(!s){if(!this.computeBranches)return;s={value:i,wildcards:[e.expressions[n]]}}if("value"in s){if("value"in r){r.value+=s.value;if(s.wildcards)r.wildcards=[...r.wildcards||[],...s.wildcards]}else{if(s.wildcards)return;r.then+=s.value;r.else+=s.value}}else{if("value"in r===false||r.wildcards)return;r={test:s.test,then:r.value+s.then,else:r.value+s.else}}}if("value"in r){r.value+=e.quasis[n].value.cooked}else{r.then+=e.quasis[n].value.cooked;r.else+=e.quasis[n].value.cooked}return r},ThisExpression(){if(Object.hasOwnProperty.call(this.vars,"this"))return{value:this.vars["this"]}},UnaryExpression(e,t){const r=t(e.argument);if(!r)return;if("value"in r&&"wildcards"in r===false){if(e.operator==="+")return{value:+r.value};if(e.operator==="-")return{value:-r.value};if(e.operator==="~")return{value:~r.value};if(e.operator==="!")return{value:!r.value}}else if("test"in r&&"wildcards"in r===false){if(e.operator==="+")return{test:r.test,then:+r.then,else:+r.else};if(e.operator==="-")return{test:r.test,then:-r.then,else:-r.else};if(e.operator==="~")return{test:r.test,then:~r.then,else:~r.else};if(e.operator==="!")return{test:r.test,then:!r.then,else:!r.else}}return}};s.LogicalExpression=s.BinaryExpression},938:function(e){e.exports=__webpack_require__(619)},942:function(e){e.exports=__webpack_require__(293)},945:function(e,t,r){var i=r(942);var n=i.Buffer;function copyProps(e,t){for(var r in e){t[r]=e[r]}}if(n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow){e.exports=i}else{copyProps(i,t);t.Buffer=SafeBuffer}function SafeBuffer(e,t,r){return n(e,t,r)}copyProps(n,SafeBuffer);SafeBuffer.from=function(e,t,r){if(typeof e==="number"){throw new TypeError("Argument must not be a number")}return n(e,t,r)};SafeBuffer.alloc=function(e,t,r){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}var i=n(e);if(t!==undefined){if(typeof r==="string"){i.fill(t,r)}else{i.fill(t)}}else{i.fill(0)}return i};SafeBuffer.allocUnsafe=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return n(e)};SafeBuffer.allocUnsafeSlow=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return i.SlowBuffer(e)}},946:function(e){"use strict";var t=process.platform==="win32";var r=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var i=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/;var n={};function win32SplitPath(e){var t=r.exec(e),n=(t[1]||"")+(t[2]||""),s=t[3]||"";var a=i.exec(s),u=a[1],o=a[2],l=a[3];return[n,u,o,l]}n.parse=function(e){if(typeof e!=="string"){throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e)}var t=win32SplitPath(e);if(!t||t.length!==4){throw new TypeError("Invalid path '"+e+"'")}return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};var s=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;var a={};function posixSplitPath(e){return s.exec(e).slice(1)}a.parse=function(e){if(typeof e!=="string"){throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e)}var t=posixSplitPath(e);if(!t||t.length!==4){throw new TypeError("Invalid path '"+e+"'")}t[1]=t[1]||"";t[2]=t[2]||"";t[3]=t[3]||"";return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};if(t)e.exports=n.parse;else e.exports=a.parse;e.exports.posix=a.parse;e.exports.win32=n.parse},955:function(e,t,r){"use strict";e.exports=Transform;var i=r(98);var n=r(683);n.inherits=r(207);n.inherits(Transform,i);function afterTransform(e,t){var r=this._transformState;r.transforming=false;var i=r.writecb;if(!i){return this.emit("error",new Error("write callback called multiple times"))}r.writechunk=null;r.writecb=null;if(t!=null)this.push(t);i(e);var n=this._readableState;n.reading=false;if(n.needReadable||n.length<n.highWaterMark){this._read(n.highWaterMark)}}function Transform(e){if(!(this instanceof Transform))return new Transform(e);i.call(this,e);this._transformState={afterTransform:afterTransform.bind(this),needTransform:false,transforming:false,writecb:null,writechunk:null,writeencoding:null};this._readableState.needReadable=true;this._readableState.sync=false;if(e){if(typeof e.transform==="function")this._transform=e.transform;if(typeof e.flush==="function")this._flush=e.flush}this.on("prefinish",prefinish)}function prefinish(){var e=this;if(typeof this._flush==="function"){this._flush(function(t,r){done(e,t,r)})}else{done(this,null,null)}}Transform.prototype.push=function(e,t){this._transformState.needTransform=false;return i.prototype.push.call(this,e,t)};Transform.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")};Transform.prototype._write=function(e,t,r){var i=this._transformState;i.writecb=r;i.writechunk=e;i.writeencoding=t;if(!i.transforming){var n=this._readableState;if(i.needTransform||n.needReadable||n.length<n.highWaterMark)this._read(n.highWaterMark)}};Transform.prototype._read=function(e){var t=this._transformState;if(t.writechunk!==null&&t.writecb&&!t.transforming){t.transforming=true;this._transform(t.writechunk,t.writeencoding,t.afterTransform)}else{t.needTransform=true}};Transform.prototype._destroy=function(e,t){var r=this;i.prototype._destroy.call(this,e,function(e){t(e);r.emit("close")})};function done(e,t,r){if(t)return e.emit("error",t);if(r!=null)e.push(r);if(e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}},963:function(module,exports,__nested_webpack_require_740784__){var fs=__nested_webpack_require_740784__(66),path=__nested_webpack_require_740784__(589),fileURLToPath=__nested_webpack_require_740784__(131),join=path.join,dirname=path.dirname,exists=fs.accessSync&&function(e){try{fs.accessSync(e)}catch(e){return false}return true}||fs.existsSync||path.existsSync,defaults={arrow:process.env.NODE_BINDINGS_ARROW||" → ",compiled:process.env.NODE_BINDINGS_COMPILED_DIR||"compiled",platform:process.platform,arch:process.arch,nodePreGyp:"node-v"+process.versions.modules+"-"+process.platform+"-"+process.arch,version:process.versions.node,bindings:"bindings.node",try:[["module_root","build","bindings"],["module_root","build","Debug","bindings"],["module_root","build","Release","bindings"],["module_root","out","Debug","bindings"],["module_root","Debug","bindings"],["module_root","out","Release","bindings"],["module_root","Release","bindings"],["module_root","build","default","bindings"],["module_root","compiled","version","platform","arch","bindings"],["module_root","addon-build","release","install-root","bindings"],["module_root","addon-build","debug","install-root","bindings"],["module_root","addon-build","default","install-root","bindings"],["module_root","lib","binding","nodePreGyp","bindings"]]};function bindings(opts){if(typeof opts=="string"){opts={bindings:opts}}else if(!opts){opts={}}Object.keys(defaults).map(function(e){if(!(e in opts))opts[e]=defaults[e]});if(!opts.module_root){opts.module_root=exports.getRoot(exports.getFileName())}if(path.extname(opts.bindings)!=".node"){opts.bindings+=".node"}var requireFunc=true?eval("require"):0;var tries=[],i=0,l=opts.try.length,n,b,err;for(;i<l;i++){n=join.apply(null,opts.try[i].map(function(e){return opts[e]||e}));tries.push(n);try{b=opts.path?requireFunc.resolve(n):requireFunc(n);if(!opts.path){b.path=n}return b}catch(e){if(e.code!=="MODULE_NOT_FOUND"&&e.code!=="QUALIFIED_PATH_RESOLUTION_FAILED"&&!/not find/i.test(e.message)){throw e}}}err=new Error("Could not locate the bindings file. Tried:\n"+tries.map(function(e){return opts.arrow+e}).join("\n"));err.tries=tries;throw err}module.exports=exports=bindings;exports.getFileName=function getFileName(e){var t=Error.prepareStackTrace,r=Error.stackTraceLimit,i={},n;Error.stackTraceLimit=10;Error.prepareStackTrace=function(t,r){for(var i=0,s=r.length;i<s;i++){n=r[i].getFileName();if(n!==__filename){if(e){if(n!==e){return}}else{return}}}};Error.captureStackTrace(i);i.stack;Error.prepareStackTrace=t;Error.stackTraceLimit=r;var s="file://";if(n.indexOf(s)===0){n=fileURLToPath(n)}return n};exports.getRoot=function getRoot(e){var t=dirname(e),r;while(true){if(t==="."){t=process.cwd()}if(exists(join(t,"package.json"))||exists(join(t,"node_modules"))){return t}if(r===t){throw new Error('Could not find module root given file: "'+e+'". Do you have a `package.json` file? ')}r=t;t=join(t,"..")}}},969:function(e){"use strict";function posix(e){return e.charAt(0)==="/"}function win32(e){var t=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var r=t.exec(e);var i=r[1]||"";var n=Boolean(i&&i.charAt(1)!==":");return Boolean(r[2]||n)}e.exports=process.platform==="win32"?win32:posix;e.exports.posix=posix;e.exports.win32=win32},972:function(e,t,r){"use strict";var i=r(12);function destroy(e,t){var r=this;var n=this._readableState&&this._readableState.destroyed;var s=this._writableState&&this._writableState.destroyed;if(n||s){if(t){t(e)}else if(e&&(!this._writableState||!this._writableState.errorEmitted)){i.nextTick(emitErrorNT,this,e)}return this}if(this._readableState){this._readableState.destroyed=true}if(this._writableState){this._writableState.destroyed=true}this._destroy(e||null,function(e){if(!t&&e){i.nextTick(emitErrorNT,r,e);if(r._writableState){r._writableState.errorEmitted=true}}else if(t){t(e)}});return this}function undestroy(){if(this._readableState){this._readableState.destroyed=false;this._readableState.reading=false;this._readableState.ended=false;this._readableState.endEmitted=false}if(this._writableState){this._writableState.destroyed=false;this._writableState.ended=false;this._writableState.ending=false;this._writableState.finished=false;this._writableState.errorEmitted=false}}function emitErrorNT(e,t){e.emit("error",t)}e.exports={destroy:destroy,undestroy:undestroy}},982:function(e,t,r){"use strict";var i=r(684);t.center=alignCenter;t.left=alignLeft;t.right=alignRight;function createPadding(e){var t="";var r=" ";var i=e;do{if(i%2){t+=r}i=Math.floor(i/2);r+=r}while(i);return t}function alignLeft(e,t){var r=e.trimRight();if(r.length===0&&e.length>=t)return e;var n="";var s=i(r);if(s<t){n=createPadding(t-s)}return r+n}function alignRight(e,t){var r=e.trimLeft();if(r.length===0&&e.length>=t)return e;var n="";var s=i(r);if(s<t){n=createPadding(t-s)}return n+r}function alignCenter(e,t){var r=e.trim();if(r.length===0&&e.length>=t)return e;var n="";var s="";var a=i(r);if(a<t){var u=parseInt((t-a)/2,10);n=createPadding(u);s=createPadding(t-(a+u))}return n+r+s}},991:function(e){const t=/^(@[^\\\/]+[\\\/])?[^\\\/]+/;e.exports=function(e){const r=e.lastIndexOf("node_modules");if(r!==-1&&(e[r-1]==="/"||e[r-1]==="\\")&&(e[r+12]==="/"||e[r+12]==="\\")){const i=e.substr(r+13).match(t);if(i)return e.substr(0,r+13+i[0].length)}};e.exports.pkgNameRegEx=t},996:function(e,t){(function(e,r){true?r(t):0})(this,function(e){"use strict";var t={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"};var r="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this";var i={5:r,"5module":r+" export import",6:r+" const class extends export import super"};var n=/^in(stanceof)?$/;var s="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿯ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-Ᶎꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭧꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ";var a="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ංඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";var u=new RegExp("["+s+"]");var o=new RegExp("["+s+a+"]");s=a=null;var l=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,477,28,11,0,9,21,155,22,13,52,76,44,33,24,27,35,30,0,12,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,0,33,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,0,161,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,270,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,754,9486,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,15,7472,3104,541];var f=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,525,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,4,9,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,232,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,792487,239];function isInAstralSet(e,t){var r=65536;for(var i=0;i<t.length;i+=2){r+=t[i];if(r>e){return false}r+=t[i+1];if(r>=e){return true}}}function isIdentifierStart(e,t){if(e<65){return e===36}if(e<91){return true}if(e<97){return e===95}if(e<123){return true}if(e<=65535){return e>=170&&u.test(String.fromCharCode(e))}if(t===false){return false}return isInAstralSet(e,l)}function isIdentifierChar(e,t){if(e<48){return e===36}if(e<58){return true}if(e<65){return false}if(e<91){return true}if(e<97){return e===95}if(e<123){return true}if(e<=65535){return e>=170&&o.test(String.fromCharCode(e))}if(t===false){return false}return isInAstralSet(e,l)||isInAstralSet(e,f)}var h=function TokenType(e,t){if(t===void 0)t={};this.label=e;this.keyword=t.keyword;this.beforeExpr=!!t.beforeExpr;this.startsExpr=!!t.startsExpr;this.isLoop=!!t.isLoop;this.isAssign=!!t.isAssign;this.prefix=!!t.prefix;this.postfix=!!t.postfix;this.binop=t.binop||null;this.updateContext=null};function binop(e,t){return new h(e,{beforeExpr:true,binop:t})}var c={beforeExpr:true},p={startsExpr:true};var d={};function kw(e,t){if(t===void 0)t={};t.keyword=e;return d[e]=new h(e,t)}var v={num:new h("num",p),regexp:new h("regexp",p),string:new h("string",p),name:new h("name",p),eof:new h("eof"),bracketL:new h("[",{beforeExpr:true,startsExpr:true}),bracketR:new h("]"),braceL:new h("{",{beforeExpr:true,startsExpr:true}),braceR:new h("}"),parenL:new h("(",{beforeExpr:true,startsExpr:true}),parenR:new h(")"),comma:new h(",",c),semi:new h(";",c),colon:new h(":",c),dot:new h("."),question:new h("?",c),arrow:new h("=>",c),template:new h("template"),invalidTemplate:new h("invalidTemplate"),ellipsis:new h("...",c),backQuote:new h("`",p),dollarBraceL:new h("${",{beforeExpr:true,startsExpr:true}),eq:new h("=",{beforeExpr:true,isAssign:true}),assign:new h("_=",{beforeExpr:true,isAssign:true}),incDec:new h("++/--",{prefix:true,postfix:true,startsExpr:true}),prefix:new h("!/~",{beforeExpr:true,prefix:true,startsExpr:true}),logicalOR:binop("||",1),logicalAND:binop("&&",2),bitwiseOR:binop("|",3),bitwiseXOR:binop("^",4),bitwiseAND:binop("&",5),equality:binop("==/!=/===/!==",6),relational:binop("</>/<=/>=",7),bitShift:binop("<</>>/>>>",8),plusMin:new h("+/-",{beforeExpr:true,binop:9,prefix:true,startsExpr:true}),modulo:binop("%",10),star:binop("*",10),slash:binop("/",10),starstar:new h("**",{beforeExpr:true}),_break:kw("break"),_case:kw("case",c),_catch:kw("catch"),_continue:kw("continue"),_debugger:kw("debugger"),_default:kw("default",c),_do:kw("do",{isLoop:true,beforeExpr:true}),_else:kw("else",c),_finally:kw("finally"),_for:kw("for",{isLoop:true}),_function:kw("function",p),_if:kw("if"),_return:kw("return",c),_switch:kw("switch"),_throw:kw("throw",c),_try:kw("try"),_var:kw("var"),_const:kw("const"),_while:kw("while",{isLoop:true}),_with:kw("with"),_new:kw("new",{beforeExpr:true,startsExpr:true}),_this:kw("this",p),_super:kw("super",p),_class:kw("class",p),_extends:kw("extends",c),_export:kw("export"),_import:kw("import",p),_null:kw("null",p),_true:kw("true",p),_false:kw("false",p),_in:kw("in",{beforeExpr:true,binop:7}),_instanceof:kw("instanceof",{beforeExpr:true,binop:7}),_typeof:kw("typeof",{beforeExpr:true,prefix:true,startsExpr:true}),_void:kw("void",{beforeExpr:true,prefix:true,startsExpr:true}),_delete:kw("delete",{beforeExpr:true,prefix:true,startsExpr:true})};var y=/\r\n?|\n|\u2028|\u2029/;var g=new RegExp(y.source,"g");function isNewLine(e,t){return e===10||e===13||!t&&(e===8232||e===8233)}var b=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/;var _=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;var m=Object.prototype;var E=m.hasOwnProperty;var D=m.toString;function has(e,t){return E.call(e,t)}var w=Array.isArray||function(e){return D.call(e)==="[object Array]"};function wordsRegexp(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}var A=function Position(e,t){this.line=e;this.column=t};A.prototype.offset=function offset(e){return new A(this.line,this.column+e)};var C=function SourceLocation(e,t,r){this.start=t;this.end=r;if(e.sourceFile!==null){this.source=e.sourceFile}};function getLineInfo(e,t){for(var r=1,i=0;;){g.lastIndex=i;var n=g.exec(e);if(n&&n.index<t){++r;i=n.index+n[0].length}else{return new A(r,t-i)}}}var x={ecmaVersion:10,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:false,allowImportExportEverywhere:false,allowAwaitOutsideFunction:false,allowHashBang:false,locations:false,onToken:null,onComment:null,ranges:false,program:null,sourceFile:null,directSourceFile:null,preserveParens:false};function getOptions(e){var t={};for(var r in x){t[r]=e&&has(e,r)?e[r]:x[r]}if(t.ecmaVersion>=2015){t.ecmaVersion-=2009}if(t.allowReserved==null){t.allowReserved=t.ecmaVersion<5}if(w(t.onToken)){var i=t.onToken;t.onToken=function(e){return i.push(e)}}if(w(t.onComment)){t.onComment=pushComment(t,t.onComment)}return t}function pushComment(e,t){return function(r,i,n,s,a,u){var o={type:r?"Block":"Line",value:i,start:n,end:s};if(e.locations){o.loc=new C(this,a,u)}if(e.ranges){o.range=[n,s]}t.push(o)}}var S=1,k=2,F=S|k,R=4,T=8,O=16,I=32,B=64,N=128;function functionFlags(e,t){return k|(e?R:0)|(t?T:0)}var L=0,P=1,j=2,q=3,$=4,M=5;var U=function Parser(e,r,n){this.options=e=getOptions(e);this.sourceFile=e.sourceFile;this.keywords=wordsRegexp(i[e.ecmaVersion>=6?6:e.sourceType==="module"?"5module":5]);var s="";if(e.allowReserved!==true){for(var a=e.ecmaVersion;;a--){if(s=t[a]){break}}if(e.sourceType==="module"){s+=" await"}}this.reservedWords=wordsRegexp(s);var u=(s?s+" ":"")+t.strict;this.reservedWordsStrict=wordsRegexp(u);this.reservedWordsStrictBind=wordsRegexp(u+" "+t.strictBind);this.input=String(r);this.containsEsc=false;if(n){this.pos=n;this.lineStart=this.input.lastIndexOf("\n",n-1)+1;this.curLine=this.input.slice(0,this.lineStart).split(y).length}else{this.pos=this.lineStart=0;this.curLine=1}this.type=v.eof;this.value=null;this.start=this.end=this.pos;this.startLoc=this.endLoc=this.curPosition();this.lastTokEndLoc=this.lastTokStartLoc=null;this.lastTokStart=this.lastTokEnd=this.pos;this.context=this.initialContext();this.exprAllowed=true;this.inModule=e.sourceType==="module";this.strict=this.inModule||this.strictDirective(this.pos);this.potentialArrowAt=-1;this.yieldPos=this.awaitPos=this.awaitIdentPos=0;this.labels=[];this.undefinedExports={};if(this.pos===0&&e.allowHashBang&&this.input.slice(0,2)==="#!"){this.skipLineComment(2)}this.scopeStack=[];this.enterScope(S);this.regexpState=null};var H={inFunction:{configurable:true},inGenerator:{configurable:true},inAsync:{configurable:true},allowSuper:{configurable:true},allowDirectSuper:{configurable:true},treatFunctionsAsVar:{configurable:true}};U.prototype.parse=function parse(){var e=this.options.program||this.startNode();this.nextToken();return this.parseTopLevel(e)};H.inFunction.get=function(){return(this.currentVarScope().flags&k)>0};H.inGenerator.get=function(){return(this.currentVarScope().flags&T)>0};H.inAsync.get=function(){return(this.currentVarScope().flags&R)>0};H.allowSuper.get=function(){return(this.currentThisScope().flags&B)>0};H.allowDirectSuper.get=function(){return(this.currentThisScope().flags&N)>0};H.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())};U.prototype.inNonArrowFunction=function inNonArrowFunction(){return(this.currentThisScope().flags&k)>0};U.extend=function extend(){var e=[],t=arguments.length;while(t--)e[t]=arguments[t];var r=this;for(var i=0;i<e.length;i++){r=e[i](r)}return r};U.parse=function parse(e,t){return new this(t,e).parse()};U.parseExpressionAt=function parseExpressionAt(e,t,r){var i=new this(r,e,t);i.nextToken();return i.parseExpression()};U.tokenizer=function tokenizer(e,t){return new this(t,e)};Object.defineProperties(U.prototype,H);var W=U.prototype;var G=/^(?:'((?:\\.|[^'])*?)'|"((?:\\.|[^"])*?)")/;W.strictDirective=function(e){for(;;){_.lastIndex=e;e+=_.exec(this.input)[0].length;var t=G.exec(this.input.slice(e));if(!t){return false}if((t[1]||t[2])==="use strict"){return true}e+=t[0].length;_.lastIndex=e;e+=_.exec(this.input)[0].length;if(this.input[e]===";"){e++}}};W.eat=function(e){if(this.type===e){this.next();return true}else{return false}};W.isContextual=function(e){return this.type===v.name&&this.value===e&&!this.containsEsc};W.eatContextual=function(e){if(!this.isContextual(e)){return false}this.next();return true};W.expectContextual=function(e){if(!this.eatContextual(e)){this.unexpected()}};W.canInsertSemicolon=function(){return this.type===v.eof||this.type===v.braceR||y.test(this.input.slice(this.lastTokEnd,this.start))};W.insertSemicolon=function(){if(this.canInsertSemicolon()){if(this.options.onInsertedSemicolon){this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc)}return true}};W.semicolon=function(){if(!this.eat(v.semi)&&!this.insertSemicolon()){this.unexpected()}};W.afterTrailingComma=function(e,t){if(this.type===e){if(this.options.onTrailingComma){this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc)}if(!t){this.next()}return true}};W.expect=function(e){this.eat(e)||this.unexpected()};W.unexpected=function(e){this.raise(e!=null?e:this.start,"Unexpected token")};function DestructuringErrors(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}W.checkPatternErrors=function(e,t){if(!e){return}if(e.trailingComma>-1){this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element")}var r=t?e.parenthesizedAssign:e.parenthesizedBind;if(r>-1){this.raiseRecoverable(r,"Parenthesized pattern")}};W.checkExpressionErrors=function(e,t){if(!e){return false}var r=e.shorthandAssign;var i=e.doubleProto;if(!t){return r>=0||i>=0}if(r>=0){this.raise(r,"Shorthand property assignments are valid only in destructuring patterns")}if(i>=0){this.raiseRecoverable(i,"Redefinition of __proto__ property")}};W.checkYieldAwaitInDefaultParams=function(){if(this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)){this.raise(this.yieldPos,"Yield expression cannot be a default value")}if(this.awaitPos){this.raise(this.awaitPos,"Await expression cannot be a default value")}};W.isSimpleAssignTarget=function(e){if(e.type==="ParenthesizedExpression"){return this.isSimpleAssignTarget(e.expression)}return e.type==="Identifier"||e.type==="MemberExpression"};var V=U.prototype;V.parseTopLevel=function(e){var t={};if(!e.body){e.body=[]}while(this.type!==v.eof){var r=this.parseStatement(null,true,t);e.body.push(r)}if(this.inModule){for(var i=0,n=Object.keys(this.undefinedExports);i<n.length;i+=1){var s=n[i];this.raiseRecoverable(this.undefinedExports[s].start,"Export '"+s+"' is not defined")}}this.adaptDirectivePrologue(e.body);this.next();e.sourceType=this.options.sourceType;return this.finishNode(e,"Program")};var z={kind:"loop"},K={kind:"switch"};V.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let")){return false}_.lastIndex=this.pos;var t=_.exec(this.input);var r=this.pos+t[0].length,i=this.input.charCodeAt(r);if(i===91){return true}if(e){return false}if(i===123){return true}if(isIdentifierStart(i,true)){var s=r+1;while(isIdentifierChar(this.input.charCodeAt(s),true)){++s}var a=this.input.slice(r,s);if(!n.test(a)){return true}}return false};V.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async")){return false}_.lastIndex=this.pos;var e=_.exec(this.input);var t=this.pos+e[0].length;return!y.test(this.input.slice(this.pos,t))&&this.input.slice(t,t+8)==="function"&&(t+8===this.input.length||!isIdentifierChar(this.input.charAt(t+8)))};V.parseStatement=function(e,t,r){var i=this.type,n=this.startNode(),s;if(this.isLet(e)){i=v._var;s="let"}switch(i){case v._break:case v._continue:return this.parseBreakContinueStatement(n,i.keyword);case v._debugger:return this.parseDebuggerStatement(n);case v._do:return this.parseDoStatement(n);case v._for:return this.parseForStatement(n);case v._function:if(e&&(this.strict||e!=="if"&&e!=="label")&&this.options.ecmaVersion>=6){this.unexpected()}return this.parseFunctionStatement(n,false,!e);case v._class:if(e){this.unexpected()}return this.parseClass(n,true);case v._if:return this.parseIfStatement(n);case v._return:return this.parseReturnStatement(n);case v._switch:return this.parseSwitchStatement(n);case v._throw:return this.parseThrowStatement(n);case v._try:return this.parseTryStatement(n);case v._const:case v._var:s=s||this.value;if(e&&s!=="var"){this.unexpected()}return this.parseVarStatement(n,s);case v._while:return this.parseWhileStatement(n);case v._with:return this.parseWithStatement(n);case v.braceL:return this.parseBlock(true,n);case v.semi:return this.parseEmptyStatement(n);case v._export:case v._import:if(this.options.ecmaVersion>10&&i===v._import){_.lastIndex=this.pos;var a=_.exec(this.input);var u=this.pos+a[0].length,o=this.input.charCodeAt(u);if(o===40){return this.parseExpressionStatement(n,this.parseExpression())}}if(!this.options.allowImportExportEverywhere){if(!t){this.raise(this.start,"'import' and 'export' may only appear at the top level")}if(!this.inModule){this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")}}return i===v._import?this.parseImport(n):this.parseExport(n,r);default:if(this.isAsyncFunction()){if(e){this.unexpected()}this.next();return this.parseFunctionStatement(n,true,!e)}var l=this.value,f=this.parseExpression();if(i===v.name&&f.type==="Identifier"&&this.eat(v.colon)){return this.parseLabeledStatement(n,l,f,e)}else{return this.parseExpressionStatement(n,f)}}};V.parseBreakContinueStatement=function(e,t){var r=t==="break";this.next();if(this.eat(v.semi)||this.insertSemicolon()){e.label=null}else if(this.type!==v.name){this.unexpected()}else{e.label=this.parseIdent();this.semicolon()}var i=0;for(;i<this.labels.length;++i){var n=this.labels[i];if(e.label==null||n.name===e.label.name){if(n.kind!=null&&(r||n.kind==="loop")){break}if(e.label&&r){break}}}if(i===this.labels.length){this.raise(e.start,"Unsyntactic "+t)}return this.finishNode(e,r?"BreakStatement":"ContinueStatement")};V.parseDebuggerStatement=function(e){this.next();this.semicolon();return this.finishNode(e,"DebuggerStatement")};V.parseDoStatement=function(e){this.next();this.labels.push(z);e.body=this.parseStatement("do");this.labels.pop();this.expect(v._while);e.test=this.parseParenExpression();if(this.options.ecmaVersion>=6){this.eat(v.semi)}else{this.semicolon()}return this.finishNode(e,"DoWhileStatement")};V.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;this.labels.push(z);this.enterScope(0);this.expect(v.parenL);if(this.type===v.semi){if(t>-1){this.unexpected(t)}return this.parseFor(e,null)}var r=this.isLet();if(this.type===v._var||this.type===v._const||r){var i=this.startNode(),n=r?"let":this.value;this.next();this.parseVar(i,true,n);this.finishNode(i,"VariableDeclaration");if((this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&i.declarations.length===1){if(this.options.ecmaVersion>=9){if(this.type===v._in){if(t>-1){this.unexpected(t)}}else{e.await=t>-1}}return this.parseForIn(e,i)}if(t>-1){this.unexpected(t)}return this.parseFor(e,i)}var s=new DestructuringErrors;var a=this.parseExpression(true,s);if(this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of")){if(this.options.ecmaVersion>=9){if(this.type===v._in){if(t>-1){this.unexpected(t)}}else{e.await=t>-1}}this.toAssignable(a,false,s);this.checkLVal(a);return this.parseForIn(e,a)}else{this.checkExpressionErrors(s,true)}if(t>-1){this.unexpected(t)}return this.parseFor(e,a)};V.parseFunctionStatement=function(e,t,r){this.next();return this.parseFunction(e,X|(r?0:Z),false,t)};V.parseIfStatement=function(e){this.next();e.test=this.parseParenExpression();e.consequent=this.parseStatement("if");e.alternate=this.eat(v._else)?this.parseStatement("if"):null;return this.finishNode(e,"IfStatement")};V.parseReturnStatement=function(e){if(!this.inFunction&&!this.options.allowReturnOutsideFunction){this.raise(this.start,"'return' outside of function")}this.next();if(this.eat(v.semi)||this.insertSemicolon()){e.argument=null}else{e.argument=this.parseExpression();this.semicolon()}return this.finishNode(e,"ReturnStatement")};V.parseSwitchStatement=function(e){this.next();e.discriminant=this.parseParenExpression();e.cases=[];this.expect(v.braceL);this.labels.push(K);this.enterScope(0);var t;for(var r=false;this.type!==v.braceR;){if(this.type===v._case||this.type===v._default){var i=this.type===v._case;if(t){this.finishNode(t,"SwitchCase")}e.cases.push(t=this.startNode());t.consequent=[];this.next();if(i){t.test=this.parseExpression()}else{if(r){this.raiseRecoverable(this.lastTokStart,"Multiple default clauses")}r=true;t.test=null}this.expect(v.colon)}else{if(!t){this.unexpected()}t.consequent.push(this.parseStatement(null))}}this.exitScope();if(t){this.finishNode(t,"SwitchCase")}this.next();this.labels.pop();return this.finishNode(e,"SwitchStatement")};V.parseThrowStatement=function(e){this.next();if(y.test(this.input.slice(this.lastTokEnd,this.start))){this.raise(this.lastTokEnd,"Illegal newline after throw")}e.argument=this.parseExpression();this.semicolon();return this.finishNode(e,"ThrowStatement")};var Q=[];V.parseTryStatement=function(e){this.next();e.block=this.parseBlock();e.handler=null;if(this.type===v._catch){var t=this.startNode();this.next();if(this.eat(v.parenL)){t.param=this.parseBindingAtom();var r=t.param.type==="Identifier";this.enterScope(r?I:0);this.checkLVal(t.param,r?$:j);this.expect(v.parenR)}else{if(this.options.ecmaVersion<10){this.unexpected()}t.param=null;this.enterScope(0)}t.body=this.parseBlock(false);this.exitScope();e.handler=this.finishNode(t,"CatchClause")}e.finalizer=this.eat(v._finally)?this.parseBlock():null;if(!e.handler&&!e.finalizer){this.raise(e.start,"Missing catch or finally clause")}return this.finishNode(e,"TryStatement")};V.parseVarStatement=function(e,t){this.next();this.parseVar(e,false,t);this.semicolon();return this.finishNode(e,"VariableDeclaration")};V.parseWhileStatement=function(e){this.next();e.test=this.parseParenExpression();this.labels.push(z);e.body=this.parseStatement("while");this.labels.pop();return this.finishNode(e,"WhileStatement")};V.parseWithStatement=function(e){if(this.strict){this.raise(this.start,"'with' in strict mode")}this.next();e.object=this.parseParenExpression();e.body=this.parseStatement("with");return this.finishNode(e,"WithStatement")};V.parseEmptyStatement=function(e){this.next();return this.finishNode(e,"EmptyStatement")};V.parseLabeledStatement=function(e,t,r,i){for(var n=0,s=this.labels;n<s.length;n+=1){var a=s[n];if(a.name===t){this.raise(r.start,"Label '"+t+"' is already declared")}}var u=this.type.isLoop?"loop":this.type===v._switch?"switch":null;for(var o=this.labels.length-1;o>=0;o--){var l=this.labels[o];if(l.statementStart===e.start){l.statementStart=this.start;l.kind=u}else{break}}this.labels.push({name:t,kind:u,statementStart:this.start});e.body=this.parseStatement(i?i.indexOf("label")===-1?i+"label":i:"label");this.labels.pop();e.label=r;return this.finishNode(e,"LabeledStatement")};V.parseExpressionStatement=function(e,t){e.expression=t;this.semicolon();return this.finishNode(e,"ExpressionStatement")};V.parseBlock=function(e,t){if(e===void 0)e=true;if(t===void 0)t=this.startNode();t.body=[];this.expect(v.braceL);if(e){this.enterScope(0)}while(!this.eat(v.braceR)){var r=this.parseStatement(null);t.body.push(r)}if(e){this.exitScope()}return this.finishNode(t,"BlockStatement")};V.parseFor=function(e,t){e.init=t;this.expect(v.semi);e.test=this.type===v.semi?null:this.parseExpression();this.expect(v.semi);e.update=this.type===v.parenR?null:this.parseExpression();this.expect(v.parenR);e.body=this.parseStatement("for");this.exitScope();this.labels.pop();return this.finishNode(e,"ForStatement")};V.parseForIn=function(e,t){var r=this.type===v._in;this.next();if(t.type==="VariableDeclaration"&&t.declarations[0].init!=null&&(!r||this.options.ecmaVersion<8||this.strict||t.kind!=="var"||t.declarations[0].id.type!=="Identifier")){this.raise(t.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer")}else if(t.type==="AssignmentPattern"){this.raise(t.start,"Invalid left-hand side in for-loop")}e.left=t;e.right=r?this.parseExpression():this.parseMaybeAssign();this.expect(v.parenR);e.body=this.parseStatement("for");this.exitScope();this.labels.pop();return this.finishNode(e,r?"ForInStatement":"ForOfStatement")};V.parseVar=function(e,t,r){e.declarations=[];e.kind=r;for(;;){var i=this.startNode();this.parseVarId(i,r);if(this.eat(v.eq)){i.init=this.parseMaybeAssign(t)}else if(r==="const"&&!(this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of"))){this.unexpected()}else if(i.id.type!=="Identifier"&&!(t&&(this.type===v._in||this.isContextual("of")))){this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value")}else{i.init=null}e.declarations.push(this.finishNode(i,"VariableDeclarator"));if(!this.eat(v.comma)){break}}return e};V.parseVarId=function(e,t){e.id=this.parseBindingAtom();this.checkLVal(e.id,t==="var"?P:j,false)};var X=1,Z=2,J=4;V.parseFunction=function(e,t,r,i){this.initFunction(e);if(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!i){if(this.type===v.star&&t&Z){this.unexpected()}e.generator=this.eat(v.star)}if(this.options.ecmaVersion>=8){e.async=!!i}if(t&X){e.id=t&J&&this.type!==v.name?null:this.parseIdent();if(e.id&&!(t&Z)){this.checkLVal(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?P:j:q)}}var n=this.yieldPos,s=this.awaitPos,a=this.awaitIdentPos;this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;this.enterScope(functionFlags(e.async,e.generator));if(!(t&X)){e.id=this.type===v.name?this.parseIdent():null}this.parseFunctionParams(e);this.parseFunctionBody(e,r,false);this.yieldPos=n;this.awaitPos=s;this.awaitIdentPos=a;return this.finishNode(e,t&X?"FunctionDeclaration":"FunctionExpression")};V.parseFunctionParams=function(e){this.expect(v.parenL);e.params=this.parseBindingList(v.parenR,false,this.options.ecmaVersion>=8);this.checkYieldAwaitInDefaultParams()};V.parseClass=function(e,t){this.next();var r=this.strict;this.strict=true;this.parseClassId(e,t);this.parseClassSuper(e);var i=this.startNode();var n=false;i.body=[];this.expect(v.braceL);while(!this.eat(v.braceR)){var s=this.parseClassElement(e.superClass!==null);if(s){i.body.push(s);if(s.type==="MethodDefinition"&&s.kind==="constructor"){if(n){this.raise(s.start,"Duplicate constructor in the same class")}n=true}}}e.body=this.finishNode(i,"ClassBody");this.strict=r;return this.finishNode(e,t?"ClassDeclaration":"ClassExpression")};V.parseClassElement=function(e){var t=this;if(this.eat(v.semi)){return null}var r=this.startNode();var i=function(e,i){if(i===void 0)i=false;var n=t.start,s=t.startLoc;if(!t.eatContextual(e)){return false}if(t.type!==v.parenL&&(!i||!t.canInsertSemicolon())){return true}if(r.key){t.unexpected()}r.computed=false;r.key=t.startNodeAt(n,s);r.key.name=e;t.finishNode(r.key,"Identifier");return false};r.kind="method";r.static=i("static");var n=this.eat(v.star);var s=false;if(!n){if(this.options.ecmaVersion>=8&&i("async",true)){s=true;n=this.options.ecmaVersion>=9&&this.eat(v.star)}else if(i("get")){r.kind="get"}else if(i("set")){r.kind="set"}}if(!r.key){this.parsePropertyName(r)}var a=r.key;var u=false;if(!r.computed&&!r.static&&(a.type==="Identifier"&&a.name==="constructor"||a.type==="Literal"&&a.value==="constructor")){if(r.kind!=="method"){this.raise(a.start,"Constructor can't have get/set modifier")}if(n){this.raise(a.start,"Constructor can't be a generator")}if(s){this.raise(a.start,"Constructor can't be an async method")}r.kind="constructor";u=e}else if(r.static&&a.type==="Identifier"&&a.name==="prototype"){this.raise(a.start,"Classes may not have a static property named prototype")}this.parseClassMethod(r,n,s,u);if(r.kind==="get"&&r.value.params.length!==0){this.raiseRecoverable(r.value.start,"getter should have no params")}if(r.kind==="set"&&r.value.params.length!==1){this.raiseRecoverable(r.value.start,"setter should have exactly one param")}if(r.kind==="set"&&r.value.params[0].type==="RestElement"){this.raiseRecoverable(r.value.params[0].start,"Setter cannot use rest params")}return r};V.parseClassMethod=function(e,t,r,i){e.value=this.parseMethod(t,r,i);return this.finishNode(e,"MethodDefinition")};V.parseClassId=function(e,t){if(this.type===v.name){e.id=this.parseIdent();if(t){this.checkLVal(e.id,j,false)}}else{if(t===true){this.unexpected()}e.id=null}};V.parseClassSuper=function(e){e.superClass=this.eat(v._extends)?this.parseExprSubscripts():null};V.parseExport=function(e,t){this.next();if(this.eat(v.star)){this.expectContextual("from");if(this.type!==v.string){this.unexpected()}e.source=this.parseExprAtom();this.semicolon();return this.finishNode(e,"ExportAllDeclaration")}if(this.eat(v._default)){this.checkExport(t,"default",this.lastTokStart);var r;if(this.type===v._function||(r=this.isAsyncFunction())){var i=this.startNode();this.next();if(r){this.next()}e.declaration=this.parseFunction(i,X|J,false,r)}else if(this.type===v._class){var n=this.startNode();e.declaration=this.parseClass(n,"nullableID")}else{e.declaration=this.parseMaybeAssign();this.semicolon()}return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement()){e.declaration=this.parseStatement(null);if(e.declaration.type==="VariableDeclaration"){this.checkVariableExport(t,e.declaration.declarations)}else{this.checkExport(t,e.declaration.id.name,e.declaration.id.start)}e.specifiers=[];e.source=null}else{e.declaration=null;e.specifiers=this.parseExportSpecifiers(t);if(this.eatContextual("from")){if(this.type!==v.string){this.unexpected()}e.source=this.parseExprAtom()}else{for(var s=0,a=e.specifiers;s<a.length;s+=1){var u=a[s];this.checkUnreserved(u.local);this.checkLocalExport(u.local)}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")};V.checkExport=function(e,t,r){if(!e){return}if(has(e,t)){this.raiseRecoverable(r,"Duplicate export '"+t+"'")}e[t]=true};V.checkPatternExport=function(e,t){var r=t.type;if(r==="Identifier"){this.checkExport(e,t.name,t.start)}else if(r==="ObjectPattern"){for(var i=0,n=t.properties;i<n.length;i+=1){var s=n[i];this.checkPatternExport(e,s)}}else if(r==="ArrayPattern"){for(var a=0,u=t.elements;a<u.length;a+=1){var o=u[a];if(o){this.checkPatternExport(e,o)}}}else if(r==="Property"){this.checkPatternExport(e,t.value)}else if(r==="AssignmentPattern"){this.checkPatternExport(e,t.left)}else if(r==="RestElement"){this.checkPatternExport(e,t.argument)}else if(r==="ParenthesizedExpression"){this.checkPatternExport(e,t.expression)}};V.checkVariableExport=function(e,t){if(!e){return}for(var r=0,i=t;r<i.length;r+=1){var n=i[r];this.checkPatternExport(e,n.id)}};V.shouldParseExportStatement=function(){return this.type.keyword==="var"||this.type.keyword==="const"||this.type.keyword==="class"||this.type.keyword==="function"||this.isLet()||this.isAsyncFunction()};V.parseExportSpecifiers=function(e){var t=[],r=true;this.expect(v.braceL);while(!this.eat(v.braceR)){if(!r){this.expect(v.comma);if(this.afterTrailingComma(v.braceR)){break}}else{r=false}var i=this.startNode();i.local=this.parseIdent(true);i.exported=this.eatContextual("as")?this.parseIdent(true):i.local;this.checkExport(e,i.exported.name,i.exported.start);t.push(this.finishNode(i,"ExportSpecifier"))}return t};V.parseImport=function(e){this.next();if(this.type===v.string){e.specifiers=Q;e.source=this.parseExprAtom()}else{e.specifiers=this.parseImportSpecifiers();this.expectContextual("from");e.source=this.type===v.string?this.parseExprAtom():this.unexpected()}this.semicolon();return this.finishNode(e,"ImportDeclaration")};V.parseImportSpecifiers=function(){var e=[],t=true;if(this.type===v.name){var r=this.startNode();r.local=this.parseIdent();this.checkLVal(r.local,j);e.push(this.finishNode(r,"ImportDefaultSpecifier"));if(!this.eat(v.comma)){return e}}if(this.type===v.star){var i=this.startNode();this.next();this.expectContextual("as");i.local=this.parseIdent();this.checkLVal(i.local,j);e.push(this.finishNode(i,"ImportNamespaceSpecifier"));return e}this.expect(v.braceL);while(!this.eat(v.braceR)){if(!t){this.expect(v.comma);if(this.afterTrailingComma(v.braceR)){break}}else{t=false}var n=this.startNode();n.imported=this.parseIdent(true);if(this.eatContextual("as")){n.local=this.parseIdent()}else{this.checkUnreserved(n.imported);n.local=n.imported}this.checkLVal(n.local,j);e.push(this.finishNode(n,"ImportSpecifier"))}return e};V.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t){e[t].directive=e[t].expression.raw.slice(1,-1)}};V.isDirectiveCandidate=function(e){return e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value==="string"&&(this.input[e.start]==='"'||this.input[e.start]==="'")};var Y=U.prototype;Y.toAssignable=function(e,t,r){if(this.options.ecmaVersion>=6&&e){switch(e.type){case"Identifier":if(this.inAsync&&e.name==="await"){this.raise(e.start,"Cannot use 'await' as identifier inside an async function")}break;case"ObjectPattern":case"ArrayPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern";if(r){this.checkPatternErrors(r,true)}for(var i=0,n=e.properties;i<n.length;i+=1){var s=n[i];this.toAssignable(s,t);if(s.type==="RestElement"&&(s.argument.type==="ArrayPattern"||s.argument.type==="ObjectPattern")){this.raise(s.argument.start,"Unexpected token")}}break;case"Property":if(e.kind!=="init"){this.raise(e.key.start,"Object pattern can't contain getter or setter")}this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern";if(r){this.checkPatternErrors(r,true)}this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement";this.toAssignable(e.argument,t);if(e.argument.type==="AssignmentPattern"){this.raise(e.argument.start,"Rest elements cannot have a default value")}break;case"AssignmentExpression":if(e.operator!=="="){this.raise(e.left.end,"Only '=' operator can be used for specifying default value.")}e.type="AssignmentPattern";delete e.operator;this.toAssignable(e.left,t);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,r);break;case"MemberExpression":if(!t){break}default:this.raise(e.start,"Assigning to rvalue")}}else if(r){this.checkPatternErrors(r,true)}return e};Y.toAssignableList=function(e,t){var r=e.length;for(var i=0;i<r;i++){var n=e[i];if(n){this.toAssignable(n,t)}}if(r){var s=e[r-1];if(this.options.ecmaVersion===6&&t&&s&&s.type==="RestElement"&&s.argument.type!=="Identifier"){this.unexpected(s.argument.start)}}return e};Y.parseSpread=function(e){var t=this.startNode();this.next();t.argument=this.parseMaybeAssign(false,e);return this.finishNode(t,"SpreadElement")};Y.parseRestBinding=function(){var e=this.startNode();this.next();if(this.options.ecmaVersion===6&&this.type!==v.name){this.unexpected()}e.argument=this.parseBindingAtom();return this.finishNode(e,"RestElement")};Y.parseBindingAtom=function(){if(this.options.ecmaVersion>=6){switch(this.type){case v.bracketL:var e=this.startNode();this.next();e.elements=this.parseBindingList(v.bracketR,true,true);return this.finishNode(e,"ArrayPattern");case v.braceL:return this.parseObj(true)}}return this.parseIdent()};Y.parseBindingList=function(e,t,r){var i=[],n=true;while(!this.eat(e)){if(n){n=false}else{this.expect(v.comma)}if(t&&this.type===v.comma){i.push(null)}else if(r&&this.afterTrailingComma(e)){break}else if(this.type===v.ellipsis){var s=this.parseRestBinding();this.parseBindingListItem(s);i.push(s);if(this.type===v.comma){this.raise(this.start,"Comma is not permitted after the rest element")}this.expect(e);break}else{var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a);i.push(a)}}return i};Y.parseBindingListItem=function(e){return e};Y.parseMaybeDefault=function(e,t,r){r=r||this.parseBindingAtom();if(this.options.ecmaVersion<6||!this.eat(v.eq)){return r}var i=this.startNodeAt(e,t);i.left=r;i.right=this.parseMaybeAssign();return this.finishNode(i,"AssignmentPattern")};Y.checkLVal=function(e,t,r){if(t===void 0)t=L;switch(e.type){case"Identifier":if(t===j&&e.name==="let"){this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name")}if(this.strict&&this.reservedWordsStrictBind.test(e.name)){this.raiseRecoverable(e.start,(t?"Binding ":"Assigning to ")+e.name+" in strict mode")}if(r){if(has(r,e.name)){this.raiseRecoverable(e.start,"Argument name clash")}r[e.name]=true}if(t!==L&&t!==M){this.declareName(e.name,t,e.start)}break;case"MemberExpression":if(t){this.raiseRecoverable(e.start,"Binding member expression")}break;case"ObjectPattern":for(var i=0,n=e.properties;i<n.length;i+=1){var s=n[i];this.checkLVal(s,t,r)}break;case"Property":this.checkLVal(e.value,t,r);break;case"ArrayPattern":for(var a=0,u=e.elements;a<u.length;a+=1){var o=u[a];if(o){this.checkLVal(o,t,r)}}break;case"AssignmentPattern":this.checkLVal(e.left,t,r);break;case"RestElement":this.checkLVal(e.argument,t,r);break;case"ParenthesizedExpression":this.checkLVal(e.expression,t,r);break;default:this.raise(e.start,(t?"Binding":"Assigning to")+" rvalue")}};var ee=U.prototype;ee.checkPropClash=function(e,t,r){if(this.options.ecmaVersion>=9&&e.type==="SpreadElement"){return}if(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand)){return}var i=e.key;var n;switch(i.type){case"Identifier":n=i.name;break;case"Literal":n=String(i.value);break;default:return}var s=e.kind;if(this.options.ecmaVersion>=6){if(n==="__proto__"&&s==="init"){if(t.proto){if(r){if(r.doubleProto<0){r.doubleProto=i.start}}else{this.raiseRecoverable(i.start,"Redefinition of __proto__ property")}}t.proto=true}return}n="$"+n;var a=t[n];if(a){var u;if(s==="init"){u=this.strict&&a.init||a.get||a.set}else{u=a.init||a[s]}if(u){this.raiseRecoverable(i.start,"Redefinition of property")}}else{a=t[n]={init:false,get:false,set:false}}a[s]=true};ee.parseExpression=function(e,t){var r=this.start,i=this.startLoc;var n=this.parseMaybeAssign(e,t);if(this.type===v.comma){var s=this.startNodeAt(r,i);s.expressions=[n];while(this.eat(v.comma)){s.expressions.push(this.parseMaybeAssign(e,t))}return this.finishNode(s,"SequenceExpression")}return n};ee.parseMaybeAssign=function(e,t,r){if(this.isContextual("yield")){if(this.inGenerator){return this.parseYield(e)}else{this.exprAllowed=false}}var i=false,n=-1,s=-1;if(t){n=t.parenthesizedAssign;s=t.trailingComma;t.parenthesizedAssign=t.trailingComma=-1}else{t=new DestructuringErrors;i=true}var a=this.start,u=this.startLoc;if(this.type===v.parenL||this.type===v.name){this.potentialArrowAt=this.start}var o=this.parseMaybeConditional(e,t);if(r){o=r.call(this,o,a,u)}if(this.type.isAssign){var l=this.startNodeAt(a,u);l.operator=this.value;l.left=this.type===v.eq?this.toAssignable(o,false,t):o;if(!i){t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1}if(t.shorthandAssign>=l.left.start){t.shorthandAssign=-1}this.checkLVal(o);this.next();l.right=this.parseMaybeAssign(e);return this.finishNode(l,"AssignmentExpression")}else{if(i){this.checkExpressionErrors(t,true)}}if(n>-1){t.parenthesizedAssign=n}if(s>-1){t.trailingComma=s}return o};ee.parseMaybeConditional=function(e,t){var r=this.start,i=this.startLoc;var n=this.parseExprOps(e,t);if(this.checkExpressionErrors(t)){return n}if(this.eat(v.question)){var s=this.startNodeAt(r,i);s.test=n;s.consequent=this.parseMaybeAssign();this.expect(v.colon);s.alternate=this.parseMaybeAssign(e);return this.finishNode(s,"ConditionalExpression")}return n};ee.parseExprOps=function(e,t){var r=this.start,i=this.startLoc;var n=this.parseMaybeUnary(t,false);if(this.checkExpressionErrors(t)){return n}return n.start===r&&n.type==="ArrowFunctionExpression"?n:this.parseExprOp(n,r,i,-1,e)};ee.parseExprOp=function(e,t,r,i,n){var s=this.type.binop;if(s!=null&&(!n||this.type!==v._in)){if(s>i){var a=this.type===v.logicalOR||this.type===v.logicalAND;var u=this.value;this.next();var o=this.start,l=this.startLoc;var f=this.parseExprOp(this.parseMaybeUnary(null,false),o,l,s,n);var h=this.buildBinary(t,r,e,f,u,a);return this.parseExprOp(h,t,r,i,n)}}return e};ee.buildBinary=function(e,t,r,i,n,s){var a=this.startNodeAt(e,t);a.left=r;a.operator=n;a.right=i;return this.finishNode(a,s?"LogicalExpression":"BinaryExpression")};ee.parseMaybeUnary=function(e,t){var r=this.start,i=this.startLoc,n;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)){n=this.parseAwait();t=true}else if(this.type.prefix){var s=this.startNode(),a=this.type===v.incDec;s.operator=this.value;s.prefix=true;this.next();s.argument=this.parseMaybeUnary(null,true);this.checkExpressionErrors(e,true);if(a){this.checkLVal(s.argument)}else if(this.strict&&s.operator==="delete"&&s.argument.type==="Identifier"){this.raiseRecoverable(s.start,"Deleting local variable in strict mode")}else{t=true}n=this.finishNode(s,a?"UpdateExpression":"UnaryExpression")}else{n=this.parseExprSubscripts(e);if(this.checkExpressionErrors(e)){return n}while(this.type.postfix&&!this.canInsertSemicolon()){var u=this.startNodeAt(r,i);u.operator=this.value;u.prefix=false;u.argument=n;this.checkLVal(n);this.next();n=this.finishNode(u,"UpdateExpression")}}if(!t&&this.eat(v.starstar)){return this.buildBinary(r,i,n,this.parseMaybeUnary(null,false),"**",false)}else{return n}};ee.parseExprSubscripts=function(e){var t=this.start,r=this.startLoc;var i=this.parseExprAtom(e);if(i.type==="ArrowFunctionExpression"&&this.input.slice(this.lastTokStart,this.lastTokEnd)!==")"){return i}var n=this.parseSubscripts(i,t,r);if(e&&n.type==="MemberExpression"){if(e.parenthesizedAssign>=n.start){e.parenthesizedAssign=-1}if(e.parenthesizedBind>=n.start){e.parenthesizedBind=-1}}return n};ee.parseSubscripts=function(e,t,r,i){var n=this.options.ecmaVersion>=8&&e.type==="Identifier"&&e.name==="async"&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&this.input.slice(e.start,e.end)==="async";while(true){var s=this.parseSubscript(e,t,r,i,n);if(s===e||s.type==="ArrowFunctionExpression"){return s}e=s}};ee.parseSubscript=function(e,t,r,i,n){var s=this.eat(v.bracketL);if(s||this.eat(v.dot)){var a=this.startNodeAt(t,r);a.object=e;a.property=s?this.parseExpression():this.parseIdent(this.options.allowReserved!=="never");a.computed=!!s;if(s){this.expect(v.bracketR)}e=this.finishNode(a,"MemberExpression")}else if(!i&&this.eat(v.parenL)){var u=new DestructuringErrors,o=this.yieldPos,l=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;var h=this.parseExprList(v.parenR,this.options.ecmaVersion>=8,false,u);if(n&&!this.canInsertSemicolon()&&this.eat(v.arrow)){this.checkPatternErrors(u,false);this.checkYieldAwaitInDefaultParams();if(this.awaitIdentPos>0){this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function")}this.yieldPos=o;this.awaitPos=l;this.awaitIdentPos=f;return this.parseArrowExpression(this.startNodeAt(t,r),h,true)}this.checkExpressionErrors(u,true);this.yieldPos=o||this.yieldPos;this.awaitPos=l||this.awaitPos;this.awaitIdentPos=f||this.awaitIdentPos;var c=this.startNodeAt(t,r);c.callee=e;c.arguments=h;e=this.finishNode(c,"CallExpression")}else if(this.type===v.backQuote){var p=this.startNodeAt(t,r);p.tag=e;p.quasi=this.parseTemplate({isTagged:true});e=this.finishNode(p,"TaggedTemplateExpression")}return e};ee.parseExprAtom=function(e){if(this.type===v.slash){this.readRegexp()}var t,r=this.potentialArrowAt===this.start;switch(this.type){case v._super:if(!this.allowSuper){this.raise(this.start,"'super' keyword outside a method")}t=this.startNode();this.next();if(this.type===v.parenL&&!this.allowDirectSuper){this.raise(t.start,"super() call outside constructor of a subclass")}if(this.type!==v.dot&&this.type!==v.bracketL&&this.type!==v.parenL){this.unexpected()}return this.finishNode(t,"Super");case v._this:t=this.startNode();this.next();return this.finishNode(t,"ThisExpression");case v.name:var i=this.start,n=this.startLoc,s=this.containsEsc;var a=this.parseIdent(false);if(this.options.ecmaVersion>=8&&!s&&a.name==="async"&&!this.canInsertSemicolon()&&this.eat(v._function)){return this.parseFunction(this.startNodeAt(i,n),0,false,true)}if(r&&!this.canInsertSemicolon()){if(this.eat(v.arrow)){return this.parseArrowExpression(this.startNodeAt(i,n),[a],false)}if(this.options.ecmaVersion>=8&&a.name==="async"&&this.type===v.name&&!s){a=this.parseIdent(false);if(this.canInsertSemicolon()||!this.eat(v.arrow)){this.unexpected()}return this.parseArrowExpression(this.startNodeAt(i,n),[a],true)}}return a;case v.regexp:var u=this.value;t=this.parseLiteral(u.value);t.regex={pattern:u.pattern,flags:u.flags};return t;case v.num:case v.string:return this.parseLiteral(this.value);case v._null:case v._true:case v._false:t=this.startNode();t.value=this.type===v._null?null:this.type===v._true;t.raw=this.type.keyword;this.next();return this.finishNode(t,"Literal");case v.parenL:var o=this.start,l=this.parseParenAndDistinguishExpression(r);if(e){if(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(l)){e.parenthesizedAssign=o}if(e.parenthesizedBind<0){e.parenthesizedBind=o}}return l;case v.bracketL:t=this.startNode();this.next();t.elements=this.parseExprList(v.bracketR,true,true,e);return this.finishNode(t,"ArrayExpression");case v.braceL:return this.parseObj(false,e);case v._function:t=this.startNode();this.next();return this.parseFunction(t,0);case v._class:return this.parseClass(this.startNode(),false);case v._new:return this.parseNew();case v.backQuote:return this.parseTemplate();case v._import:if(this.options.ecmaVersion>=11){return this.parseExprImport()}else{return this.unexpected()}default:this.unexpected()}};ee.parseExprImport=function(){var e=this.startNode();this.next();switch(this.type){case v.parenL:return this.parseDynamicImport(e);default:this.unexpected()}};ee.parseDynamicImport=function(e){this.next();e.source=this.parseMaybeAssign();if(!this.eat(v.parenR)){var t=this.start;if(this.eat(v.comma)&&this.eat(v.parenR)){this.raiseRecoverable(t,"Trailing comma is not allowed in import()")}else{this.unexpected(t)}}return this.finishNode(e,"ImportExpression")};ee.parseLiteral=function(e){var t=this.startNode();t.value=e;t.raw=this.input.slice(this.start,this.end);if(t.raw.charCodeAt(t.raw.length-1)===110){t.bigint=t.raw.slice(0,-1)}this.next();return this.finishNode(t,"Literal")};ee.parseParenExpression=function(){this.expect(v.parenL);var e=this.parseExpression();this.expect(v.parenR);return e};ee.parseParenAndDistinguishExpression=function(e){var t=this.start,r=this.startLoc,i,n=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var s=this.start,a=this.startLoc;var u=[],o=true,l=false;var f=new DestructuringErrors,h=this.yieldPos,c=this.awaitPos,p;this.yieldPos=0;this.awaitPos=0;while(this.type!==v.parenR){o?o=false:this.expect(v.comma);if(n&&this.afterTrailingComma(v.parenR,true)){l=true;break}else if(this.type===v.ellipsis){p=this.start;u.push(this.parseParenItem(this.parseRestBinding()));if(this.type===v.comma){this.raise(this.start,"Comma is not permitted after the rest element")}break}else{u.push(this.parseMaybeAssign(false,f,this.parseParenItem))}}var d=this.start,y=this.startLoc;this.expect(v.parenR);if(e&&!this.canInsertSemicolon()&&this.eat(v.arrow)){this.checkPatternErrors(f,false);this.checkYieldAwaitInDefaultParams();this.yieldPos=h;this.awaitPos=c;return this.parseParenArrowList(t,r,u)}if(!u.length||l){this.unexpected(this.lastTokStart)}if(p){this.unexpected(p)}this.checkExpressionErrors(f,true);this.yieldPos=h||this.yieldPos;this.awaitPos=c||this.awaitPos;if(u.length>1){i=this.startNodeAt(s,a);i.expressions=u;this.finishNodeAt(i,"SequenceExpression",d,y)}else{i=u[0]}}else{i=this.parseParenExpression()}if(this.options.preserveParens){var g=this.startNodeAt(t,r);g.expression=i;return this.finishNode(g,"ParenthesizedExpression")}else{return i}};ee.parseParenItem=function(e){return e};ee.parseParenArrowList=function(e,t,r){return this.parseArrowExpression(this.startNodeAt(e,t),r)};var te=[];ee.parseNew=function(){if(this.containsEsc){this.raiseRecoverable(this.start,"Escape sequence in keyword new")}var e=this.startNode();var t=this.parseIdent(true);if(this.options.ecmaVersion>=6&&this.eat(v.dot)){e.meta=t;var r=this.containsEsc;e.property=this.parseIdent(true);if(e.property.name!=="target"||r){this.raiseRecoverable(e.property.start,"The only valid meta property for new is new.target")}if(!this.inNonArrowFunction()){this.raiseRecoverable(e.start,"new.target can only be used in functions")}return this.finishNode(e,"MetaProperty")}var i=this.start,n=this.startLoc,s=this.type===v._import;e.callee=this.parseSubscripts(this.parseExprAtom(),i,n,true);if(s&&e.callee.type==="ImportExpression"){this.raise(i,"Cannot use new with import()")}if(this.eat(v.parenL)){e.arguments=this.parseExprList(v.parenR,this.options.ecmaVersion>=8,false)}else{e.arguments=te}return this.finishNode(e,"NewExpression")};ee.parseTemplateElement=function(e){var t=e.isTagged;var r=this.startNode();if(this.type===v.invalidTemplate){if(!t){this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal")}r.value={raw:this.value,cooked:null}}else{r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value}}this.next();r.tail=this.type===v.backQuote;return this.finishNode(r,"TemplateElement")};ee.parseTemplate=function(e){if(e===void 0)e={};var t=e.isTagged;if(t===void 0)t=false;var r=this.startNode();this.next();r.expressions=[];var i=this.parseTemplateElement({isTagged:t});r.quasis=[i];while(!i.tail){if(this.type===v.eof){this.raise(this.pos,"Unterminated template literal")}this.expect(v.dollarBraceL);r.expressions.push(this.parseExpression());this.expect(v.braceR);r.quasis.push(i=this.parseTemplateElement({isTagged:t}))}this.next();return this.finishNode(r,"TemplateLiteral")};ee.isAsyncProp=function(e){return!e.computed&&e.key.type==="Identifier"&&e.key.name==="async"&&(this.type===v.name||this.type===v.num||this.type===v.string||this.type===v.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===v.star)&&!y.test(this.input.slice(this.lastTokEnd,this.start))};ee.parseObj=function(e,t){var r=this.startNode(),i=true,n={};r.properties=[];this.next();while(!this.eat(v.braceR)){if(!i){this.expect(v.comma);if(this.options.ecmaVersion>=5&&this.afterTrailingComma(v.braceR)){break}}else{i=false}var s=this.parseProperty(e,t);if(!e){this.checkPropClash(s,n,t)}r.properties.push(s)}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")};ee.parseProperty=function(e,t){var r=this.startNode(),i,n,s,a;if(this.options.ecmaVersion>=9&&this.eat(v.ellipsis)){if(e){r.argument=this.parseIdent(false);if(this.type===v.comma){this.raise(this.start,"Comma is not permitted after the rest element")}return this.finishNode(r,"RestElement")}if(this.type===v.parenL&&t){if(t.parenthesizedAssign<0){t.parenthesizedAssign=this.start}if(t.parenthesizedBind<0){t.parenthesizedBind=this.start}}r.argument=this.parseMaybeAssign(false,t);if(this.type===v.comma&&t&&t.trailingComma<0){t.trailingComma=this.start}return this.finishNode(r,"SpreadElement")}if(this.options.ecmaVersion>=6){r.method=false;r.shorthand=false;if(e||t){s=this.start;a=this.startLoc}if(!e){i=this.eat(v.star)}}var u=this.containsEsc;this.parsePropertyName(r);if(!e&&!u&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(r)){n=true;i=this.options.ecmaVersion>=9&&this.eat(v.star);this.parsePropertyName(r,t)}else{n=false}this.parsePropertyValue(r,e,i,n,s,a,t,u);return this.finishNode(r,"Property")};ee.parsePropertyValue=function(e,t,r,i,n,s,a,u){if((r||i)&&this.type===v.colon){this.unexpected()}if(this.eat(v.colon)){e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(false,a);e.kind="init"}else if(this.options.ecmaVersion>=6&&this.type===v.parenL){if(t){this.unexpected()}e.kind="init";e.method=true;e.value=this.parseMethod(r,i)}else if(!t&&!u&&this.options.ecmaVersion>=5&&!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&(this.type!==v.comma&&this.type!==v.braceR)){if(r||i){this.unexpected()}e.kind=e.key.name;this.parsePropertyName(e);e.value=this.parseMethod(false);var o=e.kind==="get"?0:1;if(e.value.params.length!==o){var l=e.value.start;if(e.kind==="get"){this.raiseRecoverable(l,"getter should have no params")}else{this.raiseRecoverable(l,"setter should have exactly one param")}}else{if(e.kind==="set"&&e.value.params[0].type==="RestElement"){this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}}}else if(this.options.ecmaVersion>=6&&!e.computed&&e.key.type==="Identifier"){if(r||i){this.unexpected()}this.checkUnreserved(e.key);if(e.key.name==="await"&&!this.awaitIdentPos){this.awaitIdentPos=n}e.kind="init";if(t){e.value=this.parseMaybeDefault(n,s,e.key)}else if(this.type===v.eq&&a){if(a.shorthandAssign<0){a.shorthandAssign=this.start}e.value=this.parseMaybeDefault(n,s,e.key)}else{e.value=e.key}e.shorthand=true}else{this.unexpected()}};ee.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(v.bracketL)){e.computed=true;e.key=this.parseMaybeAssign();this.expect(v.bracketR);return e.key}else{e.computed=false}}return e.key=this.type===v.num||this.type===v.string?this.parseExprAtom():this.parseIdent(this.options.allowReserved!=="never")};ee.initFunction=function(e){e.id=null;if(this.options.ecmaVersion>=6){e.generator=e.expression=false}if(this.options.ecmaVersion>=8){e.async=false}};ee.parseMethod=function(e,t,r){var i=this.startNode(),n=this.yieldPos,s=this.awaitPos,a=this.awaitIdentPos;this.initFunction(i);if(this.options.ecmaVersion>=6){i.generator=e}if(this.options.ecmaVersion>=8){i.async=!!t}this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;this.enterScope(functionFlags(t,i.generator)|B|(r?N:0));this.expect(v.parenL);i.params=this.parseBindingList(v.parenR,false,this.options.ecmaVersion>=8);this.checkYieldAwaitInDefaultParams();this.parseFunctionBody(i,false,true);this.yieldPos=n;this.awaitPos=s;this.awaitIdentPos=a;return this.finishNode(i,"FunctionExpression")};ee.parseArrowExpression=function(e,t,r){var i=this.yieldPos,n=this.awaitPos,s=this.awaitIdentPos;this.enterScope(functionFlags(r,false)|O);this.initFunction(e);if(this.options.ecmaVersion>=8){e.async=!!r}this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;e.params=this.toAssignableList(t,true);this.parseFunctionBody(e,true,false);this.yieldPos=i;this.awaitPos=n;this.awaitIdentPos=s;return this.finishNode(e,"ArrowFunctionExpression")};ee.parseFunctionBody=function(e,t,r){var i=t&&this.type!==v.braceL;var n=this.strict,s=false;if(i){e.body=this.parseMaybeAssign();e.expression=true;this.checkParams(e,false)}else{var a=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);if(!n||a){s=this.strictDirective(this.end);if(s&&a){this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list")}}var u=this.labels;this.labels=[];if(s){this.strict=true}this.checkParams(e,!n&&!s&&!t&&!r&&this.isSimpleParamList(e.params));e.body=this.parseBlock(false);e.expression=false;this.adaptDirectivePrologue(e.body.body);this.labels=u}this.exitScope();if(this.strict&&e.id){this.checkLVal(e.id,M)}this.strict=n};ee.isSimpleParamList=function(e){for(var t=0,r=e;t<r.length;t+=1){var i=r[t];if(i.type!=="Identifier"){return false}}return true};ee.checkParams=function(e,t){var r={};for(var i=0,n=e.params;i<n.length;i+=1){var s=n[i];this.checkLVal(s,P,t?null:r)}};ee.parseExprList=function(e,t,r,i){var n=[],s=true;while(!this.eat(e)){if(!s){this.expect(v.comma);if(t&&this.afterTrailingComma(e)){break}}else{s=false}var a=void 0;if(r&&this.type===v.comma){a=null}else if(this.type===v.ellipsis){a=this.parseSpread(i);if(i&&this.type===v.comma&&i.trailingComma<0){i.trailingComma=this.start}}else{a=this.parseMaybeAssign(false,i)}n.push(a)}return n};ee.checkUnreserved=function(e){var t=e.start;var r=e.end;var i=e.name;if(this.inGenerator&&i==="yield"){this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator")}if(this.inAsync&&i==="await"){this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function")}if(this.keywords.test(i)){this.raise(t,"Unexpected keyword '"+i+"'")}if(this.options.ecmaVersion<6&&this.input.slice(t,r).indexOf("\\")!==-1){return}var n=this.strict?this.reservedWordsStrict:this.reservedWords;if(n.test(i)){if(!this.inAsync&&i==="await"){this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function")}this.raiseRecoverable(t,"The keyword '"+i+"' is reserved")}};ee.parseIdent=function(e,t){var r=this.startNode();if(this.type===v.name){r.name=this.value}else if(this.type.keyword){r.name=this.type.keyword;if((r.name==="class"||r.name==="function")&&(this.lastTokEnd!==this.lastTokStart+1||this.input.charCodeAt(this.lastTokStart)!==46)){this.context.pop()}}else{this.unexpected()}this.next(!!e);this.finishNode(r,"Identifier");if(!e){this.checkUnreserved(r);if(r.name==="await"&&!this.awaitIdentPos){this.awaitIdentPos=r.start}}return r};ee.parseYield=function(e){if(!this.yieldPos){this.yieldPos=this.start}var t=this.startNode();this.next();if(this.type===v.semi||this.canInsertSemicolon()||this.type!==v.star&&!this.type.startsExpr){t.delegate=false;t.argument=null}else{t.delegate=this.eat(v.star);t.argument=this.parseMaybeAssign(e)}return this.finishNode(t,"YieldExpression")};ee.parseAwait=function(){if(!this.awaitPos){this.awaitPos=this.start}var e=this.startNode();this.next();e.argument=this.parseMaybeUnary(null,false);return this.finishNode(e,"AwaitExpression")};var re=U.prototype;re.raise=function(e,t){var r=getLineInfo(this.input,e);t+=" ("+r.line+":"+r.column+")";var i=new SyntaxError(t);i.pos=e;i.loc=r;i.raisedAt=this.pos;throw i};re.raiseRecoverable=re.raise;re.curPosition=function(){if(this.options.locations){return new A(this.curLine,this.pos-this.lineStart)}};var ie=U.prototype;var ne=function Scope(e){this.flags=e;this.var=[];this.lexical=[];this.functions=[]};ie.enterScope=function(e){this.scopeStack.push(new ne(e))};ie.exitScope=function(){this.scopeStack.pop()};ie.treatFunctionsAsVarInScope=function(e){return e.flags&k||!this.inModule&&e.flags&S};ie.declareName=function(e,t,r){var i=false;if(t===j){var n=this.currentScope();i=n.lexical.indexOf(e)>-1||n.functions.indexOf(e)>-1||n.var.indexOf(e)>-1;n.lexical.push(e);if(this.inModule&&n.flags&S){delete this.undefinedExports[e]}}else if(t===$){var s=this.currentScope();s.lexical.push(e)}else if(t===q){var a=this.currentScope();if(this.treatFunctionsAsVar){i=a.lexical.indexOf(e)>-1}else{i=a.lexical.indexOf(e)>-1||a.var.indexOf(e)>-1}a.functions.push(e)}else{for(var u=this.scopeStack.length-1;u>=0;--u){var o=this.scopeStack[u];if(o.lexical.indexOf(e)>-1&&!(o.flags&I&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){i=true;break}o.var.push(e);if(this.inModule&&o.flags&S){delete this.undefinedExports[e]}if(o.flags&F){break}}}if(i){this.raiseRecoverable(r,"Identifier '"+e+"' has already been declared")}};ie.checkLocalExport=function(e){if(this.scopeStack[0].lexical.indexOf(e.name)===-1&&this.scopeStack[0].var.indexOf(e.name)===-1){this.undefinedExports[e.name]=e}};ie.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]};ie.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&F){return t}}};ie.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&F&&!(t.flags&O)){return t}}};var se=function Node(e,t,r){this.type="";this.start=t;this.end=0;if(e.options.locations){this.loc=new C(e,r)}if(e.options.directSourceFile){this.sourceFile=e.options.directSourceFile}if(e.options.ranges){this.range=[t,0]}};var ae=U.prototype;ae.startNode=function(){return new se(this,this.start,this.startLoc)};ae.startNodeAt=function(e,t){return new se(this,e,t)};function finishNodeAt(e,t,r,i){e.type=t;e.end=r;if(this.options.locations){e.loc.end=i}if(this.options.ranges){e.range[1]=r}return e}ae.finishNode=function(e,t){return finishNodeAt.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)};ae.finishNodeAt=function(e,t,r,i){return finishNodeAt.call(this,e,t,r,i)};var ue=function TokContext(e,t,r,i,n){this.token=e;this.isExpr=!!t;this.preserveSpace=!!r;this.override=i;this.generator=!!n};var oe={b_stat:new ue("{",false),b_expr:new ue("{",true),b_tmpl:new ue("${",false),p_stat:new ue("(",false),p_expr:new ue("(",true),q_tmpl:new ue("`",true,true,function(e){return e.tryReadTemplateToken()}),f_stat:new ue("function",false),f_expr:new ue("function",true),f_expr_gen:new ue("function",true,false,null,true),f_gen:new ue("function",false,false,null,true)};var le=U.prototype;le.initialContext=function(){return[oe.b_stat]};le.braceIsBlock=function(e){var t=this.curContext();if(t===oe.f_expr||t===oe.f_stat){return true}if(e===v.colon&&(t===oe.b_stat||t===oe.b_expr)){return!t.isExpr}if(e===v._return||e===v.name&&this.exprAllowed){return y.test(this.input.slice(this.lastTokEnd,this.start))}if(e===v._else||e===v.semi||e===v.eof||e===v.parenR||e===v.arrow){return true}if(e===v.braceL){return t===oe.b_stat}if(e===v._var||e===v._const||e===v.name){return false}return!this.exprAllowed};le.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if(t.token==="function"){return t.generator}}return false};le.updateContext=function(e){var t,r=this.type;if(r.keyword&&e===v.dot){this.exprAllowed=false}else if(t=r.updateContext){t.call(this,e)}else{this.exprAllowed=r.beforeExpr}};v.parenR.updateContext=v.braceR.updateContext=function(){if(this.context.length===1){this.exprAllowed=true;return}var e=this.context.pop();if(e===oe.b_stat&&this.curContext().token==="function"){e=this.context.pop()}this.exprAllowed=!e.isExpr};v.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?oe.b_stat:oe.b_expr);this.exprAllowed=true};v.dollarBraceL.updateContext=function(){this.context.push(oe.b_tmpl);this.exprAllowed=true};v.parenL.updateContext=function(e){var t=e===v._if||e===v._for||e===v._with||e===v._while;this.context.push(t?oe.p_stat:oe.p_expr);this.exprAllowed=true};v.incDec.updateContext=function(){};v._function.updateContext=v._class.updateContext=function(e){if(e.beforeExpr&&e!==v.semi&&e!==v._else&&!(e===v._return&&y.test(this.input.slice(this.lastTokEnd,this.start)))&&!((e===v.colon||e===v.braceL)&&this.curContext()===oe.b_stat)){this.context.push(oe.f_expr)}else{this.context.push(oe.f_stat)}this.exprAllowed=false};v.backQuote.updateContext=function(){if(this.curContext()===oe.q_tmpl){this.context.pop()}else{this.context.push(oe.q_tmpl)}this.exprAllowed=false};v.star.updateContext=function(e){if(e===v._function){var t=this.context.length-1;if(this.context[t]===oe.f_expr){this.context[t]=oe.f_expr_gen}else{this.context[t]=oe.f_gen}}this.exprAllowed=true};v.name.updateContext=function(e){var t=false;if(this.options.ecmaVersion>=6&&e!==v.dot){if(this.value==="of"&&!this.exprAllowed||this.value==="yield"&&this.inGeneratorContext()){t=true}}this.exprAllowed=t};var fe="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS";var he=fe+" Extended_Pictographic";var ce=he;var pe={9:fe,10:he,11:ce};var de="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu";var ve="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb";var ye=ve+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd";var ge=ye+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho";var be={9:ve,10:ye,11:ge};var _e={};function buildUnicodeData(e){var t=_e[e]={binary:wordsRegexp(pe[e]+" "+de),nonBinary:{General_Category:wordsRegexp(de),Script:wordsRegexp(be[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script;t.nonBinary.gc=t.nonBinary.General_Category;t.nonBinary.sc=t.nonBinary.Script;t.nonBinary.scx=t.nonBinary.Script_Extensions}buildUnicodeData(9);buildUnicodeData(10);buildUnicodeData(11);var me=U.prototype;var Ee=function RegExpValidationState(e){this.parser=e;this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"");this.unicodeProperties=_e[e.options.ecmaVersion>=11?11:e.options.ecmaVersion];this.source="";this.flags="";this.start=0;this.switchU=false;this.switchN=false;this.pos=0;this.lastIntValue=0;this.lastStringValue="";this.lastAssertionIsQuantifiable=false;this.numCapturingParens=0;this.maxBackReference=0;this.groupNames=[];this.backReferenceNames=[]};Ee.prototype.reset=function reset(e,t,r){var i=r.indexOf("u")!==-1;this.start=e|0;this.source=t+"";this.flags=r;this.switchU=i&&this.parser.options.ecmaVersion>=6;this.switchN=i&&this.parser.options.ecmaVersion>=9};Ee.prototype.raise=function raise(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)};Ee.prototype.at=function at(e){var t=this.source;var r=t.length;if(e>=r){return-1}var i=t.charCodeAt(e);if(!this.switchU||i<=55295||i>=57344||e+1>=r){return i}var n=t.charCodeAt(e+1);return n>=56320&&n<=57343?(i<<10)+n-56613888:i};Ee.prototype.nextIndex=function nextIndex(e){var t=this.source;var r=t.length;if(e>=r){return r}var i=t.charCodeAt(e),n;if(!this.switchU||i<=55295||i>=57344||e+1>=r||(n=t.charCodeAt(e+1))<56320||n>57343){return e+1}return e+2};Ee.prototype.current=function current(){return this.at(this.pos)};Ee.prototype.lookahead=function lookahead(){return this.at(this.nextIndex(this.pos))};Ee.prototype.advance=function advance(){this.pos=this.nextIndex(this.pos)};Ee.prototype.eat=function eat(e){if(this.current()===e){this.advance();return true}return false};function codePointToString(e){if(e<=65535){return String.fromCharCode(e)}e-=65536;return String.fromCharCode((e>>10)+55296,(e&1023)+56320)}me.validateRegExpFlags=function(e){var t=e.validFlags;var r=e.flags;for(var i=0;i<r.length;i++){var n=r.charAt(i);if(t.indexOf(n)===-1){this.raise(e.start,"Invalid regular expression flag")}if(r.indexOf(n,i+1)>-1){this.raise(e.start,"Duplicate regular expression flag")}}};me.validateRegExpPattern=function(e){this.regexp_pattern(e);if(!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0){e.switchN=true;this.regexp_pattern(e)}};me.regexp_pattern=function(e){e.pos=0;e.lastIntValue=0;e.lastStringValue="";e.lastAssertionIsQuantifiable=false;e.numCapturingParens=0;e.maxBackReference=0;e.groupNames.length=0;e.backReferenceNames.length=0;this.regexp_disjunction(e);if(e.pos!==e.source.length){if(e.eat(41)){e.raise("Unmatched ')'")}if(e.eat(93)||e.eat(125)){e.raise("Lone quantifier brackets")}}if(e.maxBackReference>e.numCapturingParens){e.raise("Invalid escape")}for(var t=0,r=e.backReferenceNames;t<r.length;t+=1){var i=r[t];if(e.groupNames.indexOf(i)===-1){e.raise("Invalid named capture referenced")}}};me.regexp_disjunction=function(e){this.regexp_alternative(e);while(e.eat(124)){this.regexp_alternative(e)}if(this.regexp_eatQuantifier(e,true)){e.raise("Nothing to repeat")}if(e.eat(123)){e.raise("Lone quantifier brackets")}};me.regexp_alternative=function(e){while(e.pos<e.source.length&&this.regexp_eatTerm(e)){}};me.regexp_eatTerm=function(e){if(this.regexp_eatAssertion(e)){if(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)){if(e.switchU){e.raise("Invalid quantifier")}}return true}if(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e)){this.regexp_eatQuantifier(e);return true}return false};me.regexp_eatAssertion=function(e){var t=e.pos;e.lastAssertionIsQuantifiable=false;if(e.eat(94)||e.eat(36)){return true}if(e.eat(92)){if(e.eat(66)||e.eat(98)){return true}e.pos=t}if(e.eat(40)&&e.eat(63)){var r=false;if(this.options.ecmaVersion>=9){r=e.eat(60)}if(e.eat(61)||e.eat(33)){this.regexp_disjunction(e);if(!e.eat(41)){e.raise("Unterminated group")}e.lastAssertionIsQuantifiable=!r;return true}}e.pos=t;return false};me.regexp_eatQuantifier=function(e,t){if(t===void 0)t=false;if(this.regexp_eatQuantifierPrefix(e,t)){e.eat(63);return true}return false};me.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)};me.regexp_eatBracedQuantifier=function(e,t){var r=e.pos;if(e.eat(123)){var i=0,n=-1;if(this.regexp_eatDecimalDigits(e)){i=e.lastIntValue;if(e.eat(44)&&this.regexp_eatDecimalDigits(e)){n=e.lastIntValue}if(e.eat(125)){if(n!==-1&&n<i&&!t){e.raise("numbers out of order in {} quantifier")}return true}}if(e.switchU&&!t){e.raise("Incomplete quantifier")}e.pos=r}return false};me.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)};me.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e)){return true}e.pos=t}return false};me.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){this.regexp_disjunction(e);if(e.eat(41)){return true}e.raise("Unterminated group")}e.pos=t}return false};me.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9){this.regexp_groupSpecifier(e)}else if(e.current()===63){e.raise("Invalid group")}this.regexp_disjunction(e);if(e.eat(41)){e.numCapturingParens+=1;return true}e.raise("Unterminated group")}return false};me.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)};me.regexp_eatInvalidBracedQuantifier=function(e){if(this.regexp_eatBracedQuantifier(e,true)){e.raise("Nothing to repeat")}return false};me.regexp_eatSyntaxCharacter=function(e){var t=e.current();if(isSyntaxCharacter(t)){e.lastIntValue=t;e.advance();return true}return false};function isSyntaxCharacter(e){return e===36||e>=40&&e<=43||e===46||e===63||e>=91&&e<=94||e>=123&&e<=125}me.regexp_eatPatternCharacters=function(e){var t=e.pos;var r=0;while((r=e.current())!==-1&&!isSyntaxCharacter(r)){e.advance()}return e.pos!==t};me.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();if(t!==-1&&t!==36&&!(t>=40&&t<=43)&&t!==46&&t!==63&&t!==91&&t!==94&&t!==124){e.advance();return true}return false};me.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e)){if(e.groupNames.indexOf(e.lastStringValue)!==-1){e.raise("Duplicate capture group name")}e.groupNames.push(e.lastStringValue);return}e.raise("Invalid group")}};me.regexp_eatGroupName=function(e){e.lastStringValue="";if(e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62)){return true}e.raise("Invalid capture group name")}return false};me.regexp_eatRegExpIdentifierName=function(e){e.lastStringValue="";if(this.regexp_eatRegExpIdentifierStart(e)){e.lastStringValue+=codePointToString(e.lastIntValue);while(this.regexp_eatRegExpIdentifierPart(e)){e.lastStringValue+=codePointToString(e.lastIntValue)}return true}return false};me.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos;var r=e.current();e.advance();if(r===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e)){r=e.lastIntValue}if(isRegExpIdentifierStart(r)){e.lastIntValue=r;return true}e.pos=t;return false};function isRegExpIdentifierStart(e){return isIdentifierStart(e,true)||e===36||e===95}me.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos;var r=e.current();e.advance();if(r===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e)){r=e.lastIntValue}if(isRegExpIdentifierPart(r)){e.lastIntValue=r;return true}e.pos=t;return false};function isRegExpIdentifierPart(e){return isIdentifierChar(e,true)||e===36||e===95||e===8204||e===8205}me.regexp_eatAtomEscape=function(e){if(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e)){return true}if(e.switchU){if(e.current()===99){e.raise("Invalid unicode escape")}e.raise("Invalid escape")}return false};me.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var r=e.lastIntValue;if(e.switchU){if(r>e.maxBackReference){e.maxBackReference=r}return true}if(r<=e.numCapturingParens){return true}e.pos=t}return false};me.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e)){e.backReferenceNames.push(e.lastStringValue);return true}e.raise("Invalid named reference")}return false};me.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)};me.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e)){return true}e.pos=t}return false};me.regexp_eatZero=function(e){if(e.current()===48&&!isDecimalDigit(e.lookahead())){e.lastIntValue=0;e.advance();return true}return false};me.regexp_eatControlEscape=function(e){var t=e.current();if(t===116){e.lastIntValue=9;e.advance();return true}if(t===110){e.lastIntValue=10;e.advance();return true}if(t===118){e.lastIntValue=11;e.advance();return true}if(t===102){e.lastIntValue=12;e.advance();return true}if(t===114){e.lastIntValue=13;e.advance();return true}return false};me.regexp_eatControlLetter=function(e){var t=e.current();if(isControlLetter(t)){e.lastIntValue=t%32;e.advance();return true}return false};function isControlLetter(e){return e>=65&&e<=90||e>=97&&e<=122}me.regexp_eatRegExpUnicodeEscapeSequence=function(e){var t=e.pos;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var r=e.lastIntValue;if(e.switchU&&r>=55296&&r<=56319){var i=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var n=e.lastIntValue;if(n>=56320&&n<=57343){e.lastIntValue=(r-55296)*1024+(n-56320)+65536;return true}}e.pos=i;e.lastIntValue=r}return true}if(e.switchU&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&isValidUnicode(e.lastIntValue)){return true}if(e.switchU){e.raise("Invalid unicode escape")}e.pos=t}return false};function isValidUnicode(e){return e>=0&&e<=1114111}me.regexp_eatIdentityEscape=function(e){if(e.switchU){if(this.regexp_eatSyntaxCharacter(e)){return true}if(e.eat(47)){e.lastIntValue=47;return true}return false}var t=e.current();if(t!==99&&(!e.switchN||t!==107)){e.lastIntValue=t;e.advance();return true}return false};me.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48);e.advance()}while((t=e.current())>=48&&t<=57);return true}return false};me.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(isCharacterClassEscape(t)){e.lastIntValue=-1;e.advance();return true}if(e.switchU&&this.options.ecmaVersion>=9&&(t===80||t===112)){e.lastIntValue=-1;e.advance();if(e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125)){return true}e.raise("Invalid property name")}return false};function isCharacterClassEscape(e){return e===100||e===68||e===115||e===83||e===119||e===87}me.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var r=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var i=e.lastStringValue;this.regexp_validateUnicodePropertyNameAndValue(e,r,i);return true}}e.pos=t;if(this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var n=e.lastStringValue;this.regexp_validateUnicodePropertyNameOrValue(e,n);return true}return false};me.regexp_validateUnicodePropertyNameAndValue=function(e,t,r){if(!has(e.unicodeProperties.nonBinary,t)){e.raise("Invalid property name")}if(!e.unicodeProperties.nonBinary[t].test(r)){e.raise("Invalid property value")}};me.regexp_validateUnicodePropertyNameOrValue=function(e,t){if(!e.unicodeProperties.binary.test(t)){e.raise("Invalid property name")}};me.regexp_eatUnicodePropertyName=function(e){var t=0;e.lastStringValue="";while(isUnicodePropertyNameCharacter(t=e.current())){e.lastStringValue+=codePointToString(t);e.advance()}return e.lastStringValue!==""};function isUnicodePropertyNameCharacter(e){return isControlLetter(e)||e===95}me.regexp_eatUnicodePropertyValue=function(e){var t=0;e.lastStringValue="";while(isUnicodePropertyValueCharacter(t=e.current())){e.lastStringValue+=codePointToString(t);e.advance()}return e.lastStringValue!==""};function isUnicodePropertyValueCharacter(e){return isUnicodePropertyNameCharacter(e)||isDecimalDigit(e)}me.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)};me.regexp_eatCharacterClass=function(e){if(e.eat(91)){e.eat(94);this.regexp_classRanges(e);if(e.eat(93)){return true}e.raise("Unterminated character class")}return false};me.regexp_classRanges=function(e){while(this.regexp_eatClassAtom(e)){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var r=e.lastIntValue;if(e.switchU&&(t===-1||r===-1)){e.raise("Invalid character class")}if(t!==-1&&r!==-1&&t>r){e.raise("Range out of order in character class")}}}};me.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e)){return true}if(e.switchU){var r=e.current();if(r===99||isOctalDigit(r)){e.raise("Invalid class escape")}e.raise("Invalid escape")}e.pos=t}var i=e.current();if(i!==93){e.lastIntValue=i;e.advance();return true}return false};me.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98)){e.lastIntValue=8;return true}if(e.switchU&&e.eat(45)){e.lastIntValue=45;return true}if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e)){return true}e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)};me.regexp_eatClassControlLetter=function(e){var t=e.current();if(isDecimalDigit(t)||t===95){e.lastIntValue=t%32;e.advance();return true}return false};me.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2)){return true}if(e.switchU){e.raise("Invalid escape")}e.pos=t}return false};me.regexp_eatDecimalDigits=function(e){var t=e.pos;var r=0;e.lastIntValue=0;while(isDecimalDigit(r=e.current())){e.lastIntValue=10*e.lastIntValue+(r-48);e.advance()}return e.pos!==t};function isDecimalDigit(e){return e>=48&&e<=57}me.regexp_eatHexDigits=function(e){var t=e.pos;var r=0;e.lastIntValue=0;while(isHexDigit(r=e.current())){e.lastIntValue=16*e.lastIntValue+hexToInt(r);e.advance()}return e.pos!==t};function isHexDigit(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function hexToInt(e){if(e>=65&&e<=70){return 10+(e-65)}if(e>=97&&e<=102){return 10+(e-97)}return e-48}me.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var r=e.lastIntValue;if(t<=3&&this.regexp_eatOctalDigit(e)){e.lastIntValue=t*64+r*8+e.lastIntValue}else{e.lastIntValue=t*8+r}}else{e.lastIntValue=t}return true}return false};me.regexp_eatOctalDigit=function(e){var t=e.current();if(isOctalDigit(t)){e.lastIntValue=t-48;e.advance();return true}e.lastIntValue=0;return false};function isOctalDigit(e){return e>=48&&e<=55}me.regexp_eatFixedHexDigits=function(e,t){var r=e.pos;e.lastIntValue=0;for(var i=0;i<t;++i){var n=e.current();if(!isHexDigit(n)){e.pos=r;return false}e.lastIntValue=16*e.lastIntValue+hexToInt(n);e.advance()}return true};var De=function Token(e){this.type=e.type;this.value=e.value;this.start=e.start;this.end=e.end;if(e.options.locations){this.loc=new C(e,e.startLoc,e.endLoc)}if(e.options.ranges){this.range=[e.start,e.end]}};var we=U.prototype;we.next=function(e){if(!e&&this.type.keyword&&this.containsEsc){this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword)}if(this.options.onToken){this.options.onToken(new De(this))}this.lastTokEnd=this.end;this.lastTokStart=this.start;this.lastTokEndLoc=this.endLoc;this.lastTokStartLoc=this.startLoc;this.nextToken()};we.getToken=function(){this.next();return new De(this)};if(typeof Symbol!=="undefined"){we[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===v.eof,value:t}}}}}we.curContext=function(){return this.context[this.context.length-1]};we.nextToken=function(){var e=this.curContext();if(!e||!e.preserveSpace){this.skipSpace()}this.start=this.pos;if(this.options.locations){this.startLoc=this.curPosition()}if(this.pos>=this.input.length){return this.finishToken(v.eof)}if(e.override){return e.override(this)}else{this.readToken(this.fullCharCodeAtPos())}};we.readToken=function(e){if(isIdentifierStart(e,this.options.ecmaVersion>=6)||e===92){return this.readWord()}return this.getTokenFromCode(e)};we.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=57344){return e}var t=this.input.charCodeAt(this.pos+1);return(e<<10)+t-56613888};we.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition();var t=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(r===-1){this.raise(this.pos-2,"Unterminated comment")}this.pos=r+2;if(this.options.locations){g.lastIndex=t;var i;while((i=g.exec(this.input))&&i.index<this.pos){++this.curLine;this.lineStart=i.index+i[0].length}}if(this.options.onComment){this.options.onComment(true,this.input.slice(t+2,r),t,this.pos,e,this.curPosition())}};we.skipLineComment=function(e){var t=this.pos;var r=this.options.onComment&&this.curPosition();var i=this.input.charCodeAt(this.pos+=e);while(this.pos<this.input.length&&!isNewLine(i)){i=this.input.charCodeAt(++this.pos)}if(this.options.onComment){this.options.onComment(false,this.input.slice(t+e,this.pos),t,this.pos,r,this.curPosition())}};we.skipSpace=function(){e:while(this.pos<this.input.length){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:if(this.input.charCodeAt(this.pos+1)===10){++this.pos}case 10:case 8232:case 8233:++this.pos;if(this.options.locations){++this.curLine;this.lineStart=this.pos}break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&b.test(String.fromCharCode(e))){++this.pos}else{break e}}}};we.finishToken=function(e,t){this.end=this.pos;if(this.options.locations){this.endLoc=this.curPosition()}var r=this.type;this.type=e;this.value=t;this.updateContext(r)};we.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57){return this.readNumber(true)}var t=this.input.charCodeAt(this.pos+2);if(this.options.ecmaVersion>=6&&e===46&&t===46){this.pos+=3;return this.finishToken(v.ellipsis)}else{++this.pos;return this.finishToken(v.dot)}};we.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);if(this.exprAllowed){++this.pos;return this.readRegexp()}if(e===61){return this.finishOp(v.assign,2)}return this.finishOp(v.slash,1)};we.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1);var r=1;var i=e===42?v.star:v.modulo;if(this.options.ecmaVersion>=7&&e===42&&t===42){++r;i=v.starstar;t=this.input.charCodeAt(this.pos+2)}if(t===61){return this.finishOp(v.assign,r+1)}return this.finishOp(i,r)};we.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){return this.finishOp(e===124?v.logicalOR:v.logicalAND,2)}if(t===61){return this.finishOp(v.assign,2)}return this.finishOp(e===124?v.bitwiseOR:v.bitwiseAND,1)};we.readToken_caret=function(){var e=this.input.charCodeAt(this.pos+1);if(e===61){return this.finishOp(v.assign,2)}return this.finishOp(v.bitwiseXOR,1)};we.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(t===45&&!this.inModule&&this.input.charCodeAt(this.pos+2)===62&&(this.lastTokEnd===0||y.test(this.input.slice(this.lastTokEnd,this.pos)))){this.skipLineComment(3);this.skipSpace();return this.nextToken()}return this.finishOp(v.incDec,2)}if(t===61){return this.finishOp(v.assign,2)}return this.finishOp(v.plusMin,1)};we.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1);var r=1;if(t===e){r=e===62&&this.input.charCodeAt(this.pos+2)===62?3:2;if(this.input.charCodeAt(this.pos+r)===61){return this.finishOp(v.assign,r+1)}return this.finishOp(v.bitShift,r)}if(t===33&&e===60&&!this.inModule&&this.input.charCodeAt(this.pos+2)===45&&this.input.charCodeAt(this.pos+3)===45){this.skipLineComment(4);this.skipSpace();return this.nextToken()}if(t===61){r=2}return this.finishOp(v.relational,r)};we.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===61){return this.finishOp(v.equality,this.input.charCodeAt(this.pos+2)===61?3:2)}if(e===61&&t===62&&this.options.ecmaVersion>=6){this.pos+=2;return this.finishToken(v.arrow)}return this.finishOp(e===61?v.eq:v.prefix,1)};we.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:++this.pos;return this.finishToken(v.parenL);case 41:++this.pos;return this.finishToken(v.parenR);case 59:++this.pos;return this.finishToken(v.semi);case 44:++this.pos;return this.finishToken(v.comma);case 91:++this.pos;return this.finishToken(v.bracketL);case 93:++this.pos;return this.finishToken(v.bracketR);case 123:++this.pos;return this.finishToken(v.braceL);case 125:++this.pos;return this.finishToken(v.braceR);case 58:++this.pos;return this.finishToken(v.colon);case 63:++this.pos;return this.finishToken(v.question);case 96:if(this.options.ecmaVersion<6){break}++this.pos;return this.finishToken(v.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(t===120||t===88){return this.readRadixNumber(16)}if(this.options.ecmaVersion>=6){if(t===111||t===79){return this.readRadixNumber(8)}if(t===98||t===66){return this.readRadixNumber(2)}}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(false);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 126:return this.finishOp(v.prefix,1)}this.raise(this.pos,"Unexpected character '"+codePointToString$1(e)+"'")};we.finishOp=function(e,t){var r=this.input.slice(this.pos,this.pos+t);this.pos+=t;return this.finishToken(e,r)};we.readRegexp=function(){var e,t,r=this.pos;for(;;){if(this.pos>=this.input.length){this.raise(r,"Unterminated regular expression")}var i=this.input.charAt(this.pos);if(y.test(i)){this.raise(r,"Unterminated regular expression")}if(!e){if(i==="["){t=true}else if(i==="]"&&t){t=false}else if(i==="/"&&!t){break}e=i==="\\"}else{e=false}++this.pos}var n=this.input.slice(r,this.pos);++this.pos;var s=this.pos;var a=this.readWord1();if(this.containsEsc){this.unexpected(s)}var u=this.regexpState||(this.regexpState=new Ee(this));u.reset(r,n,a);this.validateRegExpFlags(u);this.validateRegExpPattern(u);var o=null;try{o=new RegExp(n,a)}catch(e){}return this.finishToken(v.regexp,{pattern:n,flags:a,value:o})};we.readInt=function(e,t){var r=this.pos,i=0;for(var n=0,s=t==null?Infinity:t;n<s;++n){var a=this.input.charCodeAt(this.pos),u=void 0;if(a>=97){u=a-97+10}else if(a>=65){u=a-65+10}else if(a>=48&&a<=57){u=a-48}else{u=Infinity}if(u>=e){break}++this.pos;i=i*e+u}if(this.pos===r||t!=null&&this.pos-r!==t){return null}return i};we.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var r=this.readInt(e);if(r==null){this.raise(this.start+2,"Expected number in radix "+e)}if(this.options.ecmaVersion>=11&&this.input.charCodeAt(this.pos)===110){r=typeof BigInt!=="undefined"?BigInt(this.input.slice(t,this.pos)):null;++this.pos}else if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}return this.finishToken(v.num,r)};we.readNumber=function(e){var t=this.pos;if(!e&&this.readInt(10)===null){this.raise(t,"Invalid number")}var r=this.pos-t>=2&&this.input.charCodeAt(t)===48;if(r&&this.strict){this.raise(t,"Invalid number")}var i=this.input.charCodeAt(this.pos);if(!r&&!e&&this.options.ecmaVersion>=11&&i===110){var n=this.input.slice(t,this.pos);var s=typeof BigInt!=="undefined"?BigInt(n):null;++this.pos;if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}return this.finishToken(v.num,s)}if(r&&/[89]/.test(this.input.slice(t,this.pos))){r=false}if(i===46&&!r){++this.pos;this.readInt(10);i=this.input.charCodeAt(this.pos)}if((i===69||i===101)&&!r){i=this.input.charCodeAt(++this.pos);if(i===43||i===45){++this.pos}if(this.readInt(10)===null){this.raise(t,"Invalid number")}}if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}var a=this.input.slice(t,this.pos);var u=r?parseInt(a,8):parseFloat(a);return this.finishToken(v.num,u)};we.readCodePoint=function(){var e=this.input.charCodeAt(this.pos),t;if(e===123){if(this.options.ecmaVersion<6){this.unexpected()}var r=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos);++this.pos;if(t>1114111){this.invalidStringToken(r,"Code point out of bounds")}}else{t=this.readHexChar(4)}return t};function codePointToString$1(e){if(e<=65535){return String.fromCharCode(e)}e-=65536;return String.fromCharCode((e>>10)+55296,(e&1023)+56320)}we.readString=function(e){var t="",r=++this.pos;for(;;){if(this.pos>=this.input.length){this.raise(this.start,"Unterminated string constant")}var i=this.input.charCodeAt(this.pos);if(i===e){break}if(i===92){t+=this.input.slice(r,this.pos);t+=this.readEscapedChar(false);r=this.pos}else{if(isNewLine(i,this.options.ecmaVersion>=10)){this.raise(this.start,"Unterminated string constant")}++this.pos}}t+=this.input.slice(r,this.pos++);return this.finishToken(v.string,t)};var Ae={};we.tryReadTemplateToken=function(){this.inTemplateElement=true;try{this.readTmplToken()}catch(e){if(e===Ae){this.readInvalidTemplateToken()}else{throw e}}this.inTemplateElement=false};we.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9){throw Ae}else{this.raise(e,t)}};we.readTmplToken=function(){var e="",t=this.pos;for(;;){if(this.pos>=this.input.length){this.raise(this.start,"Unterminated template")}var r=this.input.charCodeAt(this.pos);if(r===96||r===36&&this.input.charCodeAt(this.pos+1)===123){if(this.pos===this.start&&(this.type===v.template||this.type===v.invalidTemplate)){if(r===36){this.pos+=2;return this.finishToken(v.dollarBraceL)}else{++this.pos;return this.finishToken(v.backQuote)}}e+=this.input.slice(t,this.pos);return this.finishToken(v.template,e)}if(r===92){e+=this.input.slice(t,this.pos);e+=this.readEscapedChar(true);t=this.pos}else if(isNewLine(r)){e+=this.input.slice(t,this.pos);++this.pos;switch(r){case 13:if(this.input.charCodeAt(this.pos)===10){++this.pos}case 10:e+="\n";break;default:e+=String.fromCharCode(r);break}if(this.options.locations){++this.curLine;this.lineStart=this.pos}t=this.pos}else{++this.pos}}};we.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++){switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if(this.input[this.pos+1]!=="{"){break}case"`":return this.finishToken(v.invalidTemplate,this.input.slice(this.start,this.pos))}}this.raise(this.start,"Unterminated template")};we.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);++this.pos;switch(t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return codePointToString$1(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:if(this.input.charCodeAt(this.pos)===10){++this.pos}case 10:if(this.options.locations){this.lineStart=this.pos;++this.curLine}return"";case 56:case 57:if(e){var r=this.pos-1;this.invalidStringToken(r,"Invalid escape sequence in template string");return null}default:if(t>=48&&t<=55){var i=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0];var n=parseInt(i,8);if(n>255){i=i.slice(0,-1);n=parseInt(i,8)}this.pos+=i.length-1;t=this.input.charCodeAt(this.pos);if((i!=="0"||t===56||t===57)&&(this.strict||e)){this.invalidStringToken(this.pos-1-i.length,e?"Octal literal in template string":"Octal literal in strict mode")}return String.fromCharCode(n)}if(isNewLine(t)){return""}return String.fromCharCode(t)}};we.readHexChar=function(e){var t=this.pos;var r=this.readInt(16,e);if(r===null){this.invalidStringToken(t,"Bad character escape sequence")}return r};we.readWord1=function(){this.containsEsc=false;var e="",t=true,r=this.pos;var i=this.options.ecmaVersion>=6;while(this.pos<this.input.length){var n=this.fullCharCodeAtPos();if(isIdentifierChar(n,i)){this.pos+=n<=65535?1:2}else if(n===92){this.containsEsc=true;e+=this.input.slice(r,this.pos);var s=this.pos;if(this.input.charCodeAt(++this.pos)!==117){this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX")}++this.pos;var a=this.readCodePoint();if(!(t?isIdentifierStart:isIdentifierChar)(a,i)){this.invalidStringToken(s,"Invalid Unicode escape")}e+=codePointToString$1(a);r=this.pos}else{break}t=false}return e+this.input.slice(r,this.pos)};we.readWord=function(){var e=this.readWord1();var t=v.name;if(this.keywords.test(e)){t=d[e]}return this.finishToken(t,e)};var Ce="7.1.0";U.acorn={Parser:U,version:Ce,defaultOptions:x,Position:A,SourceLocation:C,getLineInfo:getLineInfo,Node:se,TokenType:h,tokTypes:v,keywordTypes:d,TokContext:ue,tokContexts:oe,isIdentifierChar:isIdentifierChar,isIdentifierStart:isIdentifierStart,Token:De,isNewLine:isNewLine,lineBreak:y,lineBreakG:g,nonASCIIwhitespace:b};function parse(e,t){return U.parse(e,t)}function parseExpressionAt(e,t,r){return U.parseExpressionAt(e,t,r)}function tokenizer(e,t){return U.tokenizer(e,t)}e.Node=se;e.Parser=U;e.Position=A;e.SourceLocation=C;e.TokContext=ue;e.Token=De;e.TokenType=h;e.defaultOptions=x;e.getLineInfo=getLineInfo;e.isIdentifierChar=isIdentifierChar;e.isIdentifierStart=isIdentifierStart;e.isNewLine=isNewLine;e.keywordTypes=d;e.lineBreak=y;e.lineBreakG=g;e.nonASCIIwhitespace=b;e.parse=parse;e.parseExpressionAt=parseExpressionAt;e.tokContexts=oe;e.tokTypes=v;e.tokenizer=tokenizer;e.version=Ce;Object.defineProperty(e,"__esModule",{value:true})})},998:function(e,t,r){"use strict";var i=r(735);try{e.exports=setImmediate}catch(t){e.exports=i.nextTick}}})},300:(e,t,r)=>{e.exports=r(225)},357:e=>{"use strict";e.exports=require("assert")},293:e=>{"use strict";e.exports=require("buffer")},129:e=>{"use strict";e.exports=require("child_process")},619:e=>{"use strict";e.exports=require("constants")},417:e=>{"use strict";e.exports=require("crypto")},614:e=>{"use strict";e.exports=require("events")},747:e=>{"use strict";e.exports=require("fs")},87:e=>{"use strict";e.exports=require("os")},622:e=>{"use strict";e.exports=require("path")},413:e=>{"use strict";e.exports=require("stream")},835:e=>{"use strict";e.exports=require("url")},669:e=>{"use strict";e.exports=require("util")}};var __webpack_module_cache__={};function __webpack_require__(e){if(__webpack_module_cache__[e]){return __webpack_module_cache__[e].exports}var t=__webpack_module_cache__[e]={exports:{}};var r=true;try{__webpack_modules__[e](t,t.exports,__webpack_require__);r=false}finally{if(r)delete __webpack_module_cache__[e]}return t.exports}__webpack_require__.ab=__dirname+"/";return __webpack_require__(300)})();