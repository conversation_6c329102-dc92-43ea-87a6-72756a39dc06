{"name": "@octokit/core", "description": "Extendable client for GitHub's REST & GraphQL APIs", "version": "3.6.0", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "api", "sdk", "toolkit"], "repository": "github:octokit/core.js", "dependencies": {"@octokit/auth-token": "^2.4.4", "@octokit/graphql": "^4.5.8", "@octokit/request": "^5.6.3", "@octokit/request-error": "^2.0.5", "@octokit/types": "^6.0.3", "before-after-hook": "^2.2.0", "universal-user-agent": "^6.0.0"}, "devDependencies": {"@octokit/auth": "^3.0.1", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.3.1", "@types/jest": "^27.0.0", "@types/lolex": "^5.1.0", "@types/node": "^14.0.4", "fetch-mock": "^9.0.0", "http-proxy-agent": "^5.0.0", "jest": "^27.0.0", "lolex": "^6.0.0", "prettier": "2.4.1", "proxy": "^1.0.1", "semantic-release": "^18.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^27.0.0", "typescript": "^4.0.2"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}