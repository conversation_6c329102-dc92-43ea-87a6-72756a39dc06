{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"3.6.0\";\n", "import { getUserAgent } from \"universal-user-agent\";\nimport { Collection } from \"before-after-hook\";\nimport { request } from \"@octokit/request\";\nimport { withCustomRequest } from \"@octokit/graphql\";\nimport { createTokenAuth } from \"@octokit/auth-token\";\nimport { VERSION } from \"./version\";\nexport class Octokit {\n    constructor(options = {}) {\n        const hook = new Collection();\n        const requestDefaults = {\n            baseUrl: request.endpoint.DEFAULTS.baseUrl,\n            headers: {},\n            request: Object.assign({}, options.request, {\n                // @ts-ignore internal usage only, no need to type\n                hook: hook.bind(null, \"request\"),\n            }),\n            mediaType: {\n                previews: [],\n                format: \"\",\n            },\n        };\n        // prepend default user agent with `options.userAgent` if set\n        requestDefaults.headers[\"user-agent\"] = [\n            options.userAgent,\n            `octokit-core.js/${VERSION} ${getUserAgent()}`,\n        ]\n            .filter(Boolean)\n            .join(\" \");\n        if (options.baseUrl) {\n            requestDefaults.baseUrl = options.baseUrl;\n        }\n        if (options.previews) {\n            requestDefaults.mediaType.previews = options.previews;\n        }\n        if (options.timeZone) {\n            requestDefaults.headers[\"time-zone\"] = options.timeZone;\n        }\n        this.request = request.defaults(requestDefaults);\n        this.graphql = withCustomRequest(this.request).defaults(requestDefaults);\n        this.log = Object.assign({\n            debug: () => { },\n            info: () => { },\n            warn: console.warn.bind(console),\n            error: console.error.bind(console),\n        }, options.log);\n        this.hook = hook;\n        // (1) If neither `options.authStrategy` nor `options.auth` are set, the `octokit` instance\n        //     is unauthenticated. The `this.auth()` method is a no-op and no request hook is registered.\n        // (2) If only `options.auth` is set, use the default token authentication strategy.\n        // (3) If `options.authStrategy` is set then use it and pass in `options.auth`. Always pass own request as many strategies accept a custom request instance.\n        // TODO: type `options.auth` based on `options.authStrategy`.\n        if (!options.authStrategy) {\n            if (!options.auth) {\n                // (1)\n                this.auth = async () => ({\n                    type: \"unauthenticated\",\n                });\n            }\n            else {\n                // (2)\n                const auth = createTokenAuth(options.auth);\n                // @ts-ignore  ¯\\_(ツ)_/¯\n                hook.wrap(\"request\", auth.hook);\n                this.auth = auth;\n            }\n        }\n        else {\n            const { authStrategy, ...otherOptions } = options;\n            const auth = authStrategy(Object.assign({\n                request: this.request,\n                log: this.log,\n                // we pass the current octokit instance as well as its constructor options\n                // to allow for authentication strategies that return a new octokit instance\n                // that shares the same internal state as the current one. The original\n                // requirement for this was the \"event-octokit\" authentication strategy\n                // of https://github.com/probot/octokit-auth-probot.\n                octokit: this,\n                octokitOptions: otherOptions,\n            }, options.auth));\n            // @ts-ignore  ¯\\_(ツ)_/¯\n            hook.wrap(\"request\", auth.hook);\n            this.auth = auth;\n        }\n        // apply plugins\n        // https://stackoverflow.com/a/16345172\n        const classConstructor = this.constructor;\n        classConstructor.plugins.forEach((plugin) => {\n            Object.assign(this, plugin(this, options));\n        });\n    }\n    static defaults(defaults) {\n        const OctokitWithDefaults = class extends this {\n            constructor(...args) {\n                const options = args[0] || {};\n                if (typeof defaults === \"function\") {\n                    super(defaults(options));\n                    return;\n                }\n                super(Object.assign({}, defaults, options, options.userAgent && defaults.userAgent\n                    ? {\n                        userAgent: `${options.userAgent} ${defaults.userAgent}`,\n                    }\n                    : null));\n            }\n        };\n        return OctokitWithDefaults;\n    }\n    /**\n     * Attach a plugin (or many) to your Octokit instance.\n     *\n     * @example\n     * const API = Octokit.plugin(plugin1, plugin2, plugin3, ...)\n     */\n    static plugin(...newPlugins) {\n        var _a;\n        const currentPlugins = this.plugins;\n        const NewOctokit = (_a = class extends this {\n            },\n            _a.plugins = currentPlugins.concat(newPlugins.filter((plugin) => !currentPlugins.includes(plugin))),\n            _a);\n        return NewOctokit;\n    }\n}\nOctokit.VERSION = VERSION;\nOctokit.plugins = [];\n"], "names": ["VERSION", "Octokit", "constructor", "options", "hook", "Collection", "requestDefaults", "baseUrl", "request", "endpoint", "DEFAULTS", "headers", "Object", "assign", "bind", "mediaType", "previews", "format", "userAgent", "getUserAgent", "filter", "Boolean", "join", "timeZone", "defaults", "graphql", "withCustomRequest", "log", "debug", "info", "warn", "console", "error", "authStrategy", "auth", "type", "createTokenAuth", "wrap", "otherOptions", "octokit", "octokitOptions", "classConstructor", "plugins", "for<PERSON>ach", "plugin", "OctokitWithDefaults", "args", "newPlugins", "_a", "currentPlugins", "NewOctokit", "concat", "includes"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,OAAO,GAAG,mBAAhB;;;ACAP,AAMO,MAAMC,OAAN,CAAc;AACjBC,EAAAA,WAAW,CAACC,OAAO,GAAG,EAAX,EAAe;AACtB,UAAMC,IAAI,GAAG,IAAIC,0BAAJ,EAAb;AACA,UAAMC,eAAe,GAAG;AACpBC,MAAAA,OAAO,EAAEC,eAAO,CAACC,QAAR,CAAiBC,QAAjB,CAA0BH,OADf;AAEpBI,MAAAA,OAAO,EAAE,EAFW;AAGpBH,MAAAA,OAAO,EAAEI,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBV,OAAO,CAACK,OAA1B,EAAmC;AACxC;AACAJ,QAAAA,IAAI,EAAEA,IAAI,CAACU,IAAL,CAAU,IAAV,EAAgB,SAAhB;AAFkC,OAAnC,CAHW;AAOpBC,MAAAA,SAAS,EAAE;AACPC,QAAAA,QAAQ,EAAE,EADH;AAEPC,QAAAA,MAAM,EAAE;AAFD;AAPS,KAAxB,CAFsB;;AAetBX,IAAAA,eAAe,CAACK,OAAhB,CAAwB,YAAxB,IAAwC,CACpCR,OAAO,CAACe,SAD4B,EAEnC,mBAAkBlB,OAAQ,IAAGmB,+BAAY,EAAG,EAFT,EAInCC,MAJmC,CAI5BC,OAJ4B,EAKnCC,IALmC,CAK9B,GAL8B,CAAxC;;AAMA,QAAInB,OAAO,CAACI,OAAZ,EAAqB;AACjBD,MAAAA,eAAe,CAACC,OAAhB,GAA0BJ,OAAO,CAACI,OAAlC;AACH;;AACD,QAAIJ,OAAO,CAACa,QAAZ,EAAsB;AAClBV,MAAAA,eAAe,CAACS,SAAhB,CAA0BC,QAA1B,GAAqCb,OAAO,CAACa,QAA7C;AACH;;AACD,QAAIb,OAAO,CAACoB,QAAZ,EAAsB;AAClBjB,MAAAA,eAAe,CAACK,OAAhB,CAAwB,WAAxB,IAAuCR,OAAO,CAACoB,QAA/C;AACH;;AACD,SAAKf,OAAL,GAAeA,eAAO,CAACgB,QAAR,CAAiBlB,eAAjB,CAAf;AACA,SAAKmB,OAAL,GAAeC,yBAAiB,CAAC,KAAKlB,OAAN,CAAjB,CAAgCgB,QAAhC,CAAyClB,eAAzC,CAAf;AACA,SAAKqB,GAAL,GAAWf,MAAM,CAACC,MAAP,CAAc;AACrBe,MAAAA,KAAK,EAAE,MAAM,EADQ;AAErBC,MAAAA,IAAI,EAAE,MAAM,EAFS;AAGrBC,MAAAA,IAAI,EAAEC,OAAO,CAACD,IAAR,CAAahB,IAAb,CAAkBiB,OAAlB,CAHe;AAIrBC,MAAAA,KAAK,EAAED,OAAO,CAACC,KAAR,CAAclB,IAAd,CAAmBiB,OAAnB;AAJc,KAAd,EAKR5B,OAAO,CAACwB,GALA,CAAX;AAMA,SAAKvB,IAAL,GAAYA,IAAZ,CAtCsB;AAwCtB;AACA;AACA;AACA;;AACA,QAAI,CAACD,OAAO,CAAC8B,YAAb,EAA2B;AACvB,UAAI,CAAC9B,OAAO,CAAC+B,IAAb,EAAmB;AACf;AACA,aAAKA,IAAL,GAAY,aAAa;AACrBC,UAAAA,IAAI,EAAE;AADe,SAAb,CAAZ;AAGH,OALD,MAMK;AACD;AACA,cAAMD,IAAI,GAAGE,yBAAe,CAACjC,OAAO,CAAC+B,IAAT,CAA5B,CAFC;;AAID9B,QAAAA,IAAI,CAACiC,IAAL,CAAU,SAAV,EAAqBH,IAAI,CAAC9B,IAA1B;AACA,aAAK8B,IAAL,GAAYA,IAAZ;AACH;AACJ,KAdD,MAeK;AACD,YAAM;AAAED,QAAAA;AAAF,UAAoC9B,OAA1C;AAAA,YAAyBmC,YAAzB,4BAA0CnC,OAA1C;;AACA,YAAM+B,IAAI,GAAGD,YAAY,CAACrB,MAAM,CAACC,MAAP,CAAc;AACpCL,QAAAA,OAAO,EAAE,KAAKA,OADsB;AAEpCmB,QAAAA,GAAG,EAAE,KAAKA,GAF0B;AAGpC;AACA;AACA;AACA;AACA;AACAY,QAAAA,OAAO,EAAE,IAR2B;AASpCC,QAAAA,cAAc,EAAEF;AAToB,OAAd,EAUvBnC,OAAO,CAAC+B,IAVe,CAAD,CAAzB,CAFC;;AAcD9B,MAAAA,IAAI,CAACiC,IAAL,CAAU,SAAV,EAAqBH,IAAI,CAAC9B,IAA1B;AACA,WAAK8B,IAAL,GAAYA,IAAZ;AACH,KA3EqB;AA6EtB;;;AACA,UAAMO,gBAAgB,GAAG,KAAKvC,WAA9B;AACAuC,IAAAA,gBAAgB,CAACC,OAAjB,CAAyBC,OAAzB,CAAkCC,MAAD,IAAY;AACzChC,MAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoB+B,MAAM,CAAC,IAAD,EAAOzC,OAAP,CAA1B;AACH,KAFD;AAGH;;AACc,SAARqB,QAAQ,CAACA,QAAD,EAAW;AACtB,UAAMqB,mBAAmB,GAAG,cAAc,IAAd,CAAmB;AAC3C3C,MAAAA,WAAW,CAAC,GAAG4C,IAAJ,EAAU;AACjB,cAAM3C,OAAO,GAAG2C,IAAI,CAAC,CAAD,CAAJ,IAAW,EAA3B;;AACA,YAAI,OAAOtB,QAAP,KAAoB,UAAxB,EAAoC;AAChC,gBAAMA,QAAQ,CAACrB,OAAD,CAAd;AACA;AACH;;AACD,cAAMS,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBW,QAAlB,EAA4BrB,OAA5B,EAAqCA,OAAO,CAACe,SAAR,IAAqBM,QAAQ,CAACN,SAA9B,GACrC;AACEA,UAAAA,SAAS,EAAG,GAAEf,OAAO,CAACe,SAAU,IAAGM,QAAQ,CAACN,SAAU;AADxD,SADqC,GAIrC,IAJA,CAAN;AAKH;;AAZ0C,KAA/C;AAcA,WAAO2B,mBAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACiB,SAAND,MAAM,CAAC,GAAGG,UAAJ,EAAgB;AACzB,QAAIC,EAAJ;;AACA,UAAMC,cAAc,GAAG,KAAKP,OAA5B;AACA,UAAMQ,UAAU,IAAIF,EAAE,GAAG,cAAc,IAAd,CAAmB,EAAxB,EAEhBA,EAAE,CAACN,OAAH,GAAaO,cAAc,CAACE,MAAf,CAAsBJ,UAAU,CAAC3B,MAAX,CAAmBwB,MAAD,IAAY,CAACK,cAAc,CAACG,QAAf,CAAwBR,MAAxB,CAA/B,CAAtB,CAFG,EAGhBI,EAHY,CAAhB;AAIA,WAAOE,UAAP;AACH;;AAnHgB;AAqHrBjD,OAAO,CAACD,OAAR,GAAkBA,OAAlB;AACAC,OAAO,CAACyC,OAAR,GAAkB,EAAlB;;;;"}