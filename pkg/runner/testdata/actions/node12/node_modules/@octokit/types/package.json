{"name": "@octokit/types", "description": "Shared TypeScript definitions for Octokit projects", "version": "6.41.0", "license": "MIT", "files": ["dist-*/", "bin/"], "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "octokit": {"openapi-version": "6.8.0"}, "main": "dist-node/index.js", "module": "dist-web/index.js", "pika": true, "sideEffects": false, "keywords": ["github", "api", "sdk", "toolkit", "typescript"], "repository": "github:octokit/types.ts", "dependencies": {"@octokit/openapi-types": "^12.11.0"}, "devDependencies": {"@pika/pack": "^0.3.7", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/node": ">= 8", "github-openapi-graphql-query": "^2.0.0", "handlebars": "^4.7.6", "json-schema-to-typescript": "^11.0.0", "lodash.set": "^4.3.2", "npm-run-all": "^4.1.5", "pascal-case": "^3.1.1", "pika-plugin-merge-properties": "^1.0.6", "prettier": "^2.0.0", "semantic-release": "^19.0.3", "semantic-release-plugin-update-version-in-files": "^1.0.0", "sort-keys": "^4.2.0", "string-to-jsdoc-comment": "^1.0.0", "typedoc": "^0.23.0", "typescript": "^4.0.2"}, "publishConfig": {"access": "public"}}