{"version": 3, "file": "index.js", "sources": ["../dist-src/util/lowercase-keys.js", "../dist-src/util/merge-deep.js", "../dist-src/util/remove-undefined-properties.js", "../dist-src/merge.js", "../dist-src/util/add-query-parameters.js", "../dist-src/util/extract-url-variable-names.js", "../dist-src/util/omit.js", "../dist-src/util/url-template.js", "../dist-src/parse.js", "../dist-src/endpoint-with-defaults.js", "../dist-src/with-defaults.js", "../dist-src/version.js", "../dist-src/defaults.js", "../dist-src/index.js"], "sourcesContent": ["export function lowercaseKeys(object) {\n    if (!object) {\n        return {};\n    }\n    return Object.keys(object).reduce((newObj, key) => {\n        newObj[key.toLowerCase()] = object[key];\n        return newObj;\n    }, {});\n}\n", "import { isPlainObject } from \"is-plain-object\";\nexport function mergeDeep(defaults, options) {\n    const result = Object.assign({}, defaults);\n    Object.keys(options).forEach((key) => {\n        if (isPlainObject(options[key])) {\n            if (!(key in defaults))\n                Object.assign(result, { [key]: options[key] });\n            else\n                result[key] = mergeDeep(defaults[key], options[key]);\n        }\n        else {\n            Object.assign(result, { [key]: options[key] });\n        }\n    });\n    return result;\n}\n", "export function removeUndefinedProperties(obj) {\n    for (const key in obj) {\n        if (obj[key] === undefined) {\n            delete obj[key];\n        }\n    }\n    return obj;\n}\n", "import { lowercaseKeys } from \"./util/lowercase-keys\";\nimport { mergeDeep } from \"./util/merge-deep\";\nimport { removeUndefinedProperties } from \"./util/remove-undefined-properties\";\nexport function merge(defaults, route, options) {\n    if (typeof route === \"string\") {\n        let [method, url] = route.split(\" \");\n        options = Object.assign(url ? { method, url } : { url: method }, options);\n    }\n    else {\n        options = Object.assign({}, route);\n    }\n    // lowercase header names before merging with defaults to avoid duplicates\n    options.headers = lowercaseKeys(options.headers);\n    // remove properties with undefined values before merging\n    removeUndefinedProperties(options);\n    removeUndefinedProperties(options.headers);\n    const mergedOptions = mergeDeep(defaults || {}, options);\n    // mediaType.previews arrays are merged, instead of overwritten\n    if (defaults && defaults.mediaType.previews.length) {\n        mergedOptions.mediaType.previews = defaults.mediaType.previews\n            .filter((preview) => !mergedOptions.mediaType.previews.includes(preview))\n            .concat(mergedOptions.mediaType.previews);\n    }\n    mergedOptions.mediaType.previews = mergedOptions.mediaType.previews.map((preview) => preview.replace(/-preview/, \"\"));\n    return mergedOptions;\n}\n", "export function addQueryParameters(url, parameters) {\n    const separator = /\\?/.test(url) ? \"&\" : \"?\";\n    const names = Object.keys(parameters);\n    if (names.length === 0) {\n        return url;\n    }\n    return (url +\n        separator +\n        names\n            .map((name) => {\n            if (name === \"q\") {\n                return (\"q=\" + parameters.q.split(\"+\").map(encodeURIComponent).join(\"+\"));\n            }\n            return `${name}=${encodeURIComponent(parameters[name])}`;\n        })\n            .join(\"&\"));\n}\n", "const urlVariableRegex = /\\{[^}]+\\}/g;\nfunction removeNonChars(variableName) {\n    return variableName.replace(/^\\W+|\\W+$/g, \"\").split(/,/);\n}\nexport function extractUrlVariableNames(url) {\n    const matches = url.match(urlVariableRegex);\n    if (!matches) {\n        return [];\n    }\n    return matches.map(removeNonChars).reduce((a, b) => a.concat(b), []);\n}\n", "export function omit(object, keysToOmit) {\n    return Object.keys(object)\n        .filter((option) => !keysToOmit.includes(option))\n        .reduce((obj, key) => {\n        obj[key] = object[key];\n        return obj;\n    }, {});\n}\n", "// Based on https://github.com/bramstein/url-template, licensed under BSD\n// TODO: create separate package.\n//\n// Copyright (c) 2012-2014, <PERSON> Stein\n// All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions\n// are met:\n//  1. Redistributions of source code must retain the above copyright\n//     notice, this list of conditions and the following disclaimer.\n//  2. Redistributions in binary form must reproduce the above copyright\n//     notice, this list of conditions and the following disclaimer in the\n//     documentation and/or other materials provided with the distribution.\n//  3. The name of the author may not be used to endorse or promote products\n//     derived from this software without specific prior written permission.\n// THIS SOFTWARE IS PROVIDED BY THE AUTHOR \"AS IS\" AND ANY EXPRESS OR IMPLIED\n// WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO\n// EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n// INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,\n// BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY\n// OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n// NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,\n// EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n/* istanbul ignore file */\nfunction encodeReserved(str) {\n    return str\n        .split(/(%[0-9A-Fa-f]{2})/g)\n        .map(function (part) {\n        if (!/%[0-9A-Fa-f]/.test(part)) {\n            part = encodeURI(part).replace(/%5B/g, \"[\").replace(/%5D/g, \"]\");\n        }\n        return part;\n    })\n        .join(\"\");\n}\nfunction encodeUnreserved(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {\n        return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n    });\n}\nfunction encodeValue(operator, value, key) {\n    value =\n        operator === \"+\" || operator === \"#\"\n            ? encodeReserved(value)\n            : encodeUnreserved(value);\n    if (key) {\n        return encodeUnreserved(key) + \"=\" + value;\n    }\n    else {\n        return value;\n    }\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\nfunction isKeyOperator(operator) {\n    return operator === \";\" || operator === \"&\" || operator === \"?\";\n}\nfunction getValues(context, operator, key, modifier) {\n    var value = context[key], result = [];\n    if (isDefined(value) && value !== \"\") {\n        if (typeof value === \"string\" ||\n            typeof value === \"number\" ||\n            typeof value === \"boolean\") {\n            value = value.toString();\n            if (modifier && modifier !== \"*\") {\n                value = value.substring(0, parseInt(modifier, 10));\n            }\n            result.push(encodeValue(operator, value, isKeyOperator(operator) ? key : \"\"));\n        }\n        else {\n            if (modifier === \"*\") {\n                if (Array.isArray(value)) {\n                    value.filter(isDefined).forEach(function (value) {\n                        result.push(encodeValue(operator, value, isKeyOperator(operator) ? key : \"\"));\n                    });\n                }\n                else {\n                    Object.keys(value).forEach(function (k) {\n                        if (isDefined(value[k])) {\n                            result.push(encodeValue(operator, value[k], k));\n                        }\n                    });\n                }\n            }\n            else {\n                const tmp = [];\n                if (Array.isArray(value)) {\n                    value.filter(isDefined).forEach(function (value) {\n                        tmp.push(encodeValue(operator, value));\n                    });\n                }\n                else {\n                    Object.keys(value).forEach(function (k) {\n                        if (isDefined(value[k])) {\n                            tmp.push(encodeUnreserved(k));\n                            tmp.push(encodeValue(operator, value[k].toString()));\n                        }\n                    });\n                }\n                if (isKeyOperator(operator)) {\n                    result.push(encodeUnreserved(key) + \"=\" + tmp.join(\",\"));\n                }\n                else if (tmp.length !== 0) {\n                    result.push(tmp.join(\",\"));\n                }\n            }\n        }\n    }\n    else {\n        if (operator === \";\") {\n            if (isDefined(value)) {\n                result.push(encodeUnreserved(key));\n            }\n        }\n        else if (value === \"\" && (operator === \"&\" || operator === \"?\")) {\n            result.push(encodeUnreserved(key) + \"=\");\n        }\n        else if (value === \"\") {\n            result.push(\"\");\n        }\n    }\n    return result;\n}\nexport function parseUrl(template) {\n    return {\n        expand: expand.bind(null, template),\n    };\n}\nfunction expand(template, context) {\n    var operators = [\"+\", \"#\", \".\", \"/\", \";\", \"?\", \"&\"];\n    return template.replace(/\\{([^\\{\\}]+)\\}|([^\\{\\}]+)/g, function (_, expression, literal) {\n        if (expression) {\n            let operator = \"\";\n            const values = [];\n            if (operators.indexOf(expression.charAt(0)) !== -1) {\n                operator = expression.charAt(0);\n                expression = expression.substr(1);\n            }\n            expression.split(/,/g).forEach(function (variable) {\n                var tmp = /([^:\\*]*)(?::(\\d+)|(\\*))?/.exec(variable);\n                values.push(getValues(context, operator, tmp[1], tmp[2] || tmp[3]));\n            });\n            if (operator && operator !== \"+\") {\n                var separator = \",\";\n                if (operator === \"?\") {\n                    separator = \"&\";\n                }\n                else if (operator !== \"#\") {\n                    separator = operator;\n                }\n                return (values.length !== 0 ? operator : \"\") + values.join(separator);\n            }\n            else {\n                return values.join(\",\");\n            }\n        }\n        else {\n            return encodeReserved(literal);\n        }\n    });\n}\n", "import { addQueryParameters } from \"./util/add-query-parameters\";\nimport { extractUrlVariableNames } from \"./util/extract-url-variable-names\";\nimport { omit } from \"./util/omit\";\nimport { parseUrl } from \"./util/url-template\";\nexport function parse(options) {\n    // https://fetch.spec.whatwg.org/#methods\n    let method = options.method.toUpperCase();\n    // replace :varname with {varname} to make it RFC 6570 compatible\n    let url = (options.url || \"/\").replace(/:([a-z]\\w+)/g, \"{$1}\");\n    let headers = Object.assign({}, options.headers);\n    let body;\n    let parameters = omit(options, [\n        \"method\",\n        \"baseUrl\",\n        \"url\",\n        \"headers\",\n        \"request\",\n        \"mediaType\",\n    ]);\n    // extract variable names from URL to calculate remaining variables later\n    const urlVariableNames = extractUrlVariableNames(url);\n    url = parseUrl(url).expand(parameters);\n    if (!/^http/.test(url)) {\n        url = options.baseUrl + url;\n    }\n    const omittedParameters = Object.keys(options)\n        .filter((option) => urlVariableNames.includes(option))\n        .concat(\"baseUrl\");\n    const remainingParameters = omit(parameters, omittedParameters);\n    const isBinaryRequest = /application\\/octet-stream/i.test(headers.accept);\n    if (!isBinaryRequest) {\n        if (options.mediaType.format) {\n            // e.g. application/vnd.github.v3+json => application/vnd.github.v3.raw\n            headers.accept = headers.accept\n                .split(/,/)\n                .map((preview) => preview.replace(/application\\/vnd(\\.\\w+)(\\.v3)?(\\.\\w+)?(\\+json)?$/, `application/vnd$1$2.${options.mediaType.format}`))\n                .join(\",\");\n        }\n        if (options.mediaType.previews.length) {\n            const previewsFromAcceptHeader = headers.accept.match(/[\\w-]+(?=-preview)/g) || [];\n            headers.accept = previewsFromAcceptHeader\n                .concat(options.mediaType.previews)\n                .map((preview) => {\n                const format = options.mediaType.format\n                    ? `.${options.mediaType.format}`\n                    : \"+json\";\n                return `application/vnd.github.${preview}-preview${format}`;\n            })\n                .join(\",\");\n        }\n    }\n    // for GET/HEAD requests, set URL query parameters from remaining parameters\n    // for PATCH/POST/PUT/DELETE requests, set request body from remaining parameters\n    if ([\"GET\", \"HEAD\"].includes(method)) {\n        url = addQueryParameters(url, remainingParameters);\n    }\n    else {\n        if (\"data\" in remainingParameters) {\n            body = remainingParameters.data;\n        }\n        else {\n            if (Object.keys(remainingParameters).length) {\n                body = remainingParameters;\n            }\n            else {\n                headers[\"content-length\"] = 0;\n            }\n        }\n    }\n    // default content-type for JSON if body is set\n    if (!headers[\"content-type\"] && typeof body !== \"undefined\") {\n        headers[\"content-type\"] = \"application/json; charset=utf-8\";\n    }\n    // GitHub expects 'content-length: 0' header for PUT/PATCH requests without body.\n    // fetch does not allow to set `content-length` header, but we can set body to an empty string\n    if ([\"PATCH\", \"PUT\"].includes(method) && typeof body === \"undefined\") {\n        body = \"\";\n    }\n    // Only return body/request keys if present\n    return Object.assign({ method, url, headers }, typeof body !== \"undefined\" ? { body } : null, options.request ? { request: options.request } : null);\n}\n", "import { merge } from \"./merge\";\nimport { parse } from \"./parse\";\nexport function endpointWithDefaults(defaults, route, options) {\n    return parse(merge(defaults, route, options));\n}\n", "import { endpointWithDefaults } from \"./endpoint-with-defaults\";\nimport { merge } from \"./merge\";\nimport { parse } from \"./parse\";\nexport function withDefaults(oldDefaults, newDefaults) {\n    const DEFAULTS = merge(oldDefaults, newDefaults);\n    const endpoint = endpointWithDefaults.bind(null, DEFAULTS);\n    return Object.assign(endpoint, {\n        DEFAULTS,\n        defaults: withDefaults.bind(null, DEFAULTS),\n        merge: merge.bind(null, DEFAULTS),\n        parse,\n    });\n}\n", "export const VERSION = \"6.0.12\";\n", "import { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nconst userAgent = `octokit-endpoint.js/${VERSION} ${getUserAgent()}`;\n// DEFAULTS has all properties set that EndpointOptions has, except url.\n// So we use RequestParameters and add method as additional required property.\nexport const DEFAULTS = {\n    method: \"GET\",\n    baseUrl: \"https://api.github.com\",\n    headers: {\n        accept: \"application/vnd.github.v3+json\",\n        \"user-agent\": userAgent,\n    },\n    mediaType: {\n        format: \"\",\n        previews: [],\n    },\n};\n", "import { withDefaults } from \"./with-defaults\";\nimport { DEFAULTS } from \"./defaults\";\nexport const endpoint = withDefaults(null, DEFAULTS);\n"], "names": ["lowercaseKeys", "object", "Object", "keys", "reduce", "newObj", "key", "toLowerCase", "mergeDeep", "defaults", "options", "result", "assign", "for<PERSON>ach", "isPlainObject", "removeUndefinedProperties", "obj", "undefined", "merge", "route", "method", "url", "split", "headers", "mergedOptions", "mediaType", "previews", "length", "filter", "preview", "includes", "concat", "map", "replace", "addQueryParameters", "parameters", "separator", "test", "names", "name", "q", "encodeURIComponent", "join", "urlVariableRegex", "removeNonChars", "variableName", "extractUrlVariableNames", "matches", "match", "a", "b", "omit", "keysToOmit", "option", "encodeReserved", "str", "part", "encodeURI", "encodeUnreserved", "c", "charCodeAt", "toString", "toUpperCase", "encodeValue", "operator", "value", "isDefined", "isKeyOperator", "getV<PERSON>ues", "context", "modifier", "substring", "parseInt", "push", "Array", "isArray", "k", "tmp", "parseUrl", "template", "expand", "bind", "operators", "_", "expression", "literal", "values", "indexOf", "char<PERSON>t", "substr", "variable", "exec", "parse", "body", "urlVariableNames", "baseUrl", "omittedParameters", "remainingParameters", "isBinaryRequest", "accept", "format", "previewsFromAcceptHeader", "data", "request", "endpointWithDefaults", "with<PERSON><PERSON><PERSON><PERSON>", "oldDefaults", "newDefaults", "DEFAULTS", "endpoint", "VERSION", "userAgent", "getUserAgent"], "mappings": ";;;;;;;AAAO,SAASA,aAAT,CAAuBC,MAAvB,EAA+B;AAClC,MAAI,CAACA,MAAL,EAAa;AACT,WAAO,EAAP;AACH;;AACD,SAAOC,MAAM,CAACC,IAAP,CAAYF,MAAZ,EAAoBG,MAApB,CAA2B,CAACC,MAAD,EAASC,GAAT,KAAiB;AAC/CD,IAAAA,MAAM,CAACC,GAAG,CAACC,WAAJ,EAAD,CAAN,GAA4BN,MAAM,CAACK,GAAD,CAAlC;AACA,WAAOD,MAAP;AACH,GAHM,EAGJ,EAHI,CAAP;AAIH;;ACPM,SAASG,SAAT,CAAmBC,QAAnB,EAA6BC,OAA7B,EAAsC;AACzC,QAAMC,MAAM,GAAGT,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBH,QAAlB,CAAf;AACAP,EAAAA,MAAM,CAACC,IAAP,CAAYO,OAAZ,EAAqBG,OAArB,CAA8BP,GAAD,IAAS;AAClC,QAAIQ,2BAAa,CAACJ,OAAO,CAACJ,GAAD,CAAR,CAAjB,EAAiC;AAC7B,UAAI,EAAEA,GAAG,IAAIG,QAAT,CAAJ,EACIP,MAAM,CAACU,MAAP,CAAcD,MAAd,EAAsB;AAAE,SAACL,GAAD,GAAOI,OAAO,CAACJ,GAAD;AAAhB,OAAtB,EADJ,KAGIK,MAAM,CAACL,GAAD,CAAN,GAAcE,SAAS,CAACC,QAAQ,CAACH,GAAD,CAAT,EAAgBI,OAAO,CAACJ,GAAD,CAAvB,CAAvB;AACP,KALD,MAMK;AACDJ,MAAAA,MAAM,CAACU,MAAP,CAAcD,MAAd,EAAsB;AAAE,SAACL,GAAD,GAAOI,OAAO,CAACJ,GAAD;AAAhB,OAAtB;AACH;AACJ,GAVD;AAWA,SAAOK,MAAP;AACH;;ACfM,SAASI,yBAAT,CAAmCC,GAAnC,EAAwC;AAC3C,OAAK,MAAMV,GAAX,IAAkBU,GAAlB,EAAuB;AACnB,QAAIA,GAAG,CAACV,GAAD,CAAH,KAAaW,SAAjB,EAA4B;AACxB,aAAOD,GAAG,CAACV,GAAD,CAAV;AACH;AACJ;;AACD,SAAOU,GAAP;AACH;;ACJM,SAASE,KAAT,CAAeT,QAAf,EAAyBU,KAAzB,EAAgCT,OAAhC,EAAyC;AAC5C,MAAI,OAAOS,KAAP,KAAiB,QAArB,EAA+B;AAC3B,QAAI,CAACC,MAAD,EAASC,GAAT,IAAgBF,KAAK,CAACG,KAAN,CAAY,GAAZ,CAApB;AACAZ,IAAAA,OAAO,GAAGR,MAAM,CAACU,MAAP,CAAcS,GAAG,GAAG;AAAED,MAAAA,MAAF;AAAUC,MAAAA;AAAV,KAAH,GAAqB;AAAEA,MAAAA,GAAG,EAAED;AAAP,KAAtC,EAAuDV,OAAvD,CAAV;AACH,GAHD,MAIK;AACDA,IAAAA,OAAO,GAAGR,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBO,KAAlB,CAAV;AACH,GAP2C;;;AAS5CT,EAAAA,OAAO,CAACa,OAAR,GAAkBvB,aAAa,CAACU,OAAO,CAACa,OAAT,CAA/B,CAT4C;;AAW5CR,EAAAA,yBAAyB,CAACL,OAAD,CAAzB;AACAK,EAAAA,yBAAyB,CAACL,OAAO,CAACa,OAAT,CAAzB;AACA,QAAMC,aAAa,GAAGhB,SAAS,CAACC,QAAQ,IAAI,EAAb,EAAiBC,OAAjB,CAA/B,CAb4C;;AAe5C,MAAID,QAAQ,IAAIA,QAAQ,CAACgB,SAAT,CAAmBC,QAAnB,CAA4BC,MAA5C,EAAoD;AAChDH,IAAAA,aAAa,CAACC,SAAd,CAAwBC,QAAxB,GAAmCjB,QAAQ,CAACgB,SAAT,CAAmBC,QAAnB,CAC9BE,MAD8B,CACtBC,OAAD,IAAa,CAACL,aAAa,CAACC,SAAd,CAAwBC,QAAxB,CAAiCI,QAAjC,CAA0CD,OAA1C,CADS,EAE9BE,MAF8B,CAEvBP,aAAa,CAACC,SAAd,CAAwBC,QAFD,CAAnC;AAGH;;AACDF,EAAAA,aAAa,CAACC,SAAd,CAAwBC,QAAxB,GAAmCF,aAAa,CAACC,SAAd,CAAwBC,QAAxB,CAAiCM,GAAjC,CAAsCH,OAAD,IAAaA,OAAO,CAACI,OAAR,CAAgB,UAAhB,EAA4B,EAA5B,CAAlD,CAAnC;AACA,SAAOT,aAAP;AACH;;ACzBM,SAASU,kBAAT,CAA4Bb,GAA5B,EAAiCc,UAAjC,EAA6C;AAChD,QAAMC,SAAS,GAAG,KAAKC,IAAL,CAAUhB,GAAV,IAAiB,GAAjB,GAAuB,GAAzC;AACA,QAAMiB,KAAK,GAAGpC,MAAM,CAACC,IAAP,CAAYgC,UAAZ,CAAd;;AACA,MAAIG,KAAK,CAACX,MAAN,KAAiB,CAArB,EAAwB;AACpB,WAAON,GAAP;AACH;;AACD,SAAQA,GAAG,GACPe,SADI,GAEJE,KAAK,CACAN,GADL,CACUO,IAAD,IAAU;AACf,QAAIA,IAAI,KAAK,GAAb,EAAkB;AACd,aAAQ,OAAOJ,UAAU,CAACK,CAAX,CAAalB,KAAb,CAAmB,GAAnB,EAAwBU,GAAxB,CAA4BS,kBAA5B,EAAgDC,IAAhD,CAAqD,GAArD,CAAf;AACH;;AACD,WAAQ,GAAEH,IAAK,IAAGE,kBAAkB,CAACN,UAAU,CAACI,IAAD,CAAX,CAAmB,EAAvD;AACH,GAND,EAOKG,IAPL,CAOU,GAPV,CAFJ;AAUH;;AChBD,MAAMC,gBAAgB,GAAG,YAAzB;;AACA,SAASC,cAAT,CAAwBC,YAAxB,EAAsC;AAClC,SAAOA,YAAY,CAACZ,OAAb,CAAqB,YAArB,EAAmC,EAAnC,EAAuCX,KAAvC,CAA6C,GAA7C,CAAP;AACH;;AACD,AAAO,SAASwB,uBAAT,CAAiCzB,GAAjC,EAAsC;AACzC,QAAM0B,OAAO,GAAG1B,GAAG,CAAC2B,KAAJ,CAAUL,gBAAV,CAAhB;;AACA,MAAI,CAACI,OAAL,EAAc;AACV,WAAO,EAAP;AACH;;AACD,SAAOA,OAAO,CAACf,GAAR,CAAYY,cAAZ,EAA4BxC,MAA5B,CAAmC,CAAC6C,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAAClB,MAAF,CAASmB,CAAT,CAA7C,EAA0D,EAA1D,CAAP;AACH;;ACVM,SAASC,IAAT,CAAclD,MAAd,EAAsBmD,UAAtB,EAAkC;AACrC,SAAOlD,MAAM,CAACC,IAAP,CAAYF,MAAZ,EACF2B,MADE,CACMyB,MAAD,IAAY,CAACD,UAAU,CAACtB,QAAX,CAAoBuB,MAApB,CADlB,EAEFjD,MAFE,CAEK,CAACY,GAAD,EAAMV,GAAN,KAAc;AACtBU,IAAAA,GAAG,CAACV,GAAD,CAAH,GAAWL,MAAM,CAACK,GAAD,CAAjB;AACA,WAAOU,GAAP;AACH,GALM,EAKJ,EALI,CAAP;AAMH;;ACPD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA,SAASsC,cAAT,CAAwBC,GAAxB,EAA6B;AACzB,SAAOA,GAAG,CACLjC,KADE,CACI,oBADJ,EAEFU,GAFE,CAEE,UAAUwB,IAAV,EAAgB;AACrB,QAAI,CAAC,eAAenB,IAAf,CAAoBmB,IAApB,CAAL,EAAgC;AAC5BA,MAAAA,IAAI,GAAGC,SAAS,CAACD,IAAD,CAAT,CAAgBvB,OAAhB,CAAwB,MAAxB,EAAgC,GAAhC,EAAqCA,OAArC,CAA6C,MAA7C,EAAqD,GAArD,CAAP;AACH;;AACD,WAAOuB,IAAP;AACH,GAPM,EAQFd,IARE,CAQG,EARH,CAAP;AASH;;AACD,SAASgB,gBAAT,CAA0BH,GAA1B,EAA+B;AAC3B,SAAOd,kBAAkB,CAACc,GAAD,CAAlB,CAAwBtB,OAAxB,CAAgC,UAAhC,EAA4C,UAAU0B,CAAV,EAAa;AAC5D,WAAO,MAAMA,CAAC,CAACC,UAAF,CAAa,CAAb,EAAgBC,QAAhB,CAAyB,EAAzB,EAA6BC,WAA7B,EAAb;AACH,GAFM,CAAP;AAGH;;AACD,SAASC,WAAT,CAAqBC,QAArB,EAA+BC,KAA/B,EAAsC3D,GAAtC,EAA2C;AACvC2D,EAAAA,KAAK,GACDD,QAAQ,KAAK,GAAb,IAAoBA,QAAQ,KAAK,GAAjC,GACMV,cAAc,CAACW,KAAD,CADpB,GAEMP,gBAAgB,CAACO,KAAD,CAH1B;;AAIA,MAAI3D,GAAJ,EAAS;AACL,WAAOoD,gBAAgB,CAACpD,GAAD,CAAhB,GAAwB,GAAxB,GAA8B2D,KAArC;AACH,GAFD,MAGK;AACD,WAAOA,KAAP;AACH;AACJ;;AACD,SAASC,SAAT,CAAmBD,KAAnB,EAA0B;AACtB,SAAOA,KAAK,KAAKhD,SAAV,IAAuBgD,KAAK,KAAK,IAAxC;AACH;;AACD,SAASE,aAAT,CAAuBH,QAAvB,EAAiC;AAC7B,SAAOA,QAAQ,KAAK,GAAb,IAAoBA,QAAQ,KAAK,GAAjC,IAAwCA,QAAQ,KAAK,GAA5D;AACH;;AACD,SAASI,SAAT,CAAmBC,OAAnB,EAA4BL,QAA5B,EAAsC1D,GAAtC,EAA2CgE,QAA3C,EAAqD;AACjD,MAAIL,KAAK,GAAGI,OAAO,CAAC/D,GAAD,CAAnB;AAAA,MAA0BK,MAAM,GAAG,EAAnC;;AACA,MAAIuD,SAAS,CAACD,KAAD,CAAT,IAAoBA,KAAK,KAAK,EAAlC,EAAsC;AAClC,QAAI,OAAOA,KAAP,KAAiB,QAAjB,IACA,OAAOA,KAAP,KAAiB,QADjB,IAEA,OAAOA,KAAP,KAAiB,SAFrB,EAEgC;AAC5BA,MAAAA,KAAK,GAAGA,KAAK,CAACJ,QAAN,EAAR;;AACA,UAAIS,QAAQ,IAAIA,QAAQ,KAAK,GAA7B,EAAkC;AAC9BL,QAAAA,KAAK,GAAGA,KAAK,CAACM,SAAN,CAAgB,CAAhB,EAAmBC,QAAQ,CAACF,QAAD,EAAW,EAAX,CAA3B,CAAR;AACH;;AACD3D,MAAAA,MAAM,CAAC8D,IAAP,CAAYV,WAAW,CAACC,QAAD,EAAWC,KAAX,EAAkBE,aAAa,CAACH,QAAD,CAAb,GAA0B1D,GAA1B,GAAgC,EAAlD,CAAvB;AACH,KARD,MASK;AACD,UAAIgE,QAAQ,KAAK,GAAjB,EAAsB;AAClB,YAAII,KAAK,CAACC,OAAN,CAAcV,KAAd,CAAJ,EAA0B;AACtBA,UAAAA,KAAK,CAACrC,MAAN,CAAasC,SAAb,EAAwBrD,OAAxB,CAAgC,UAAUoD,KAAV,EAAiB;AAC7CtD,YAAAA,MAAM,CAAC8D,IAAP,CAAYV,WAAW,CAACC,QAAD,EAAWC,KAAX,EAAkBE,aAAa,CAACH,QAAD,CAAb,GAA0B1D,GAA1B,GAAgC,EAAlD,CAAvB;AACH,WAFD;AAGH,SAJD,MAKK;AACDJ,UAAAA,MAAM,CAACC,IAAP,CAAY8D,KAAZ,EAAmBpD,OAAnB,CAA2B,UAAU+D,CAAV,EAAa;AACpC,gBAAIV,SAAS,CAACD,KAAK,CAACW,CAAD,CAAN,CAAb,EAAyB;AACrBjE,cAAAA,MAAM,CAAC8D,IAAP,CAAYV,WAAW,CAACC,QAAD,EAAWC,KAAK,CAACW,CAAD,CAAhB,EAAqBA,CAArB,CAAvB;AACH;AACJ,WAJD;AAKH;AACJ,OAbD,MAcK;AACD,cAAMC,GAAG,GAAG,EAAZ;;AACA,YAAIH,KAAK,CAACC,OAAN,CAAcV,KAAd,CAAJ,EAA0B;AACtBA,UAAAA,KAAK,CAACrC,MAAN,CAAasC,SAAb,EAAwBrD,OAAxB,CAAgC,UAAUoD,KAAV,EAAiB;AAC7CY,YAAAA,GAAG,CAACJ,IAAJ,CAASV,WAAW,CAACC,QAAD,EAAWC,KAAX,CAApB;AACH,WAFD;AAGH,SAJD,MAKK;AACD/D,UAAAA,MAAM,CAACC,IAAP,CAAY8D,KAAZ,EAAmBpD,OAAnB,CAA2B,UAAU+D,CAAV,EAAa;AACpC,gBAAIV,SAAS,CAACD,KAAK,CAACW,CAAD,CAAN,CAAb,EAAyB;AACrBC,cAAAA,GAAG,CAACJ,IAAJ,CAASf,gBAAgB,CAACkB,CAAD,CAAzB;AACAC,cAAAA,GAAG,CAACJ,IAAJ,CAASV,WAAW,CAACC,QAAD,EAAWC,KAAK,CAACW,CAAD,CAAL,CAASf,QAAT,EAAX,CAApB;AACH;AACJ,WALD;AAMH;;AACD,YAAIM,aAAa,CAACH,QAAD,CAAjB,EAA6B;AACzBrD,UAAAA,MAAM,CAAC8D,IAAP,CAAYf,gBAAgB,CAACpD,GAAD,CAAhB,GAAwB,GAAxB,GAA8BuE,GAAG,CAACnC,IAAJ,CAAS,GAAT,CAA1C;AACH,SAFD,MAGK,IAAImC,GAAG,CAAClD,MAAJ,KAAe,CAAnB,EAAsB;AACvBhB,UAAAA,MAAM,CAAC8D,IAAP,CAAYI,GAAG,CAACnC,IAAJ,CAAS,GAAT,CAAZ;AACH;AACJ;AACJ;AACJ,GAhDD,MAiDK;AACD,QAAIsB,QAAQ,KAAK,GAAjB,EAAsB;AAClB,UAAIE,SAAS,CAACD,KAAD,CAAb,EAAsB;AAClBtD,QAAAA,MAAM,CAAC8D,IAAP,CAAYf,gBAAgB,CAACpD,GAAD,CAA5B;AACH;AACJ,KAJD,MAKK,IAAI2D,KAAK,KAAK,EAAV,KAAiBD,QAAQ,KAAK,GAAb,IAAoBA,QAAQ,KAAK,GAAlD,CAAJ,EAA4D;AAC7DrD,MAAAA,MAAM,CAAC8D,IAAP,CAAYf,gBAAgB,CAACpD,GAAD,CAAhB,GAAwB,GAApC;AACH,KAFI,MAGA,IAAI2D,KAAK,KAAK,EAAd,EAAkB;AACnBtD,MAAAA,MAAM,CAAC8D,IAAP,CAAY,EAAZ;AACH;AACJ;;AACD,SAAO9D,MAAP;AACH;;AACD,AAAO,SAASmE,QAAT,CAAkBC,QAAlB,EAA4B;AAC/B,SAAO;AACHC,IAAAA,MAAM,EAAEA,MAAM,CAACC,IAAP,CAAY,IAAZ,EAAkBF,QAAlB;AADL,GAAP;AAGH;;AACD,SAASC,MAAT,CAAgBD,QAAhB,EAA0BV,OAA1B,EAAmC;AAC/B,MAAIa,SAAS,GAAG,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,CAAhB;AACA,SAAOH,QAAQ,CAAC9C,OAAT,CAAiB,4BAAjB,EAA+C,UAAUkD,CAAV,EAAaC,UAAb,EAAyBC,OAAzB,EAAkC;AACpF,QAAID,UAAJ,EAAgB;AACZ,UAAIpB,QAAQ,GAAG,EAAf;AACA,YAAMsB,MAAM,GAAG,EAAf;;AACA,UAAIJ,SAAS,CAACK,OAAV,CAAkBH,UAAU,CAACI,MAAX,CAAkB,CAAlB,CAAlB,MAA4C,CAAC,CAAjD,EAAoD;AAChDxB,QAAAA,QAAQ,GAAGoB,UAAU,CAACI,MAAX,CAAkB,CAAlB,CAAX;AACAJ,QAAAA,UAAU,GAAGA,UAAU,CAACK,MAAX,CAAkB,CAAlB,CAAb;AACH;;AACDL,MAAAA,UAAU,CAAC9D,KAAX,CAAiB,IAAjB,EAAuBT,OAAvB,CAA+B,UAAU6E,QAAV,EAAoB;AAC/C,YAAIb,GAAG,GAAG,4BAA4Bc,IAA5B,CAAiCD,QAAjC,CAAV;AACAJ,QAAAA,MAAM,CAACb,IAAP,CAAYL,SAAS,CAACC,OAAD,EAAUL,QAAV,EAAoBa,GAAG,CAAC,CAAD,CAAvB,EAA4BA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAG,CAAC,CAAD,CAAzC,CAArB;AACH,OAHD;;AAIA,UAAIb,QAAQ,IAAIA,QAAQ,KAAK,GAA7B,EAAkC;AAC9B,YAAI5B,SAAS,GAAG,GAAhB;;AACA,YAAI4B,QAAQ,KAAK,GAAjB,EAAsB;AAClB5B,UAAAA,SAAS,GAAG,GAAZ;AACH,SAFD,MAGK,IAAI4B,QAAQ,KAAK,GAAjB,EAAsB;AACvB5B,UAAAA,SAAS,GAAG4B,QAAZ;AACH;;AACD,eAAO,CAACsB,MAAM,CAAC3D,MAAP,KAAkB,CAAlB,GAAsBqC,QAAtB,GAAiC,EAAlC,IAAwCsB,MAAM,CAAC5C,IAAP,CAAYN,SAAZ,CAA/C;AACH,OATD,MAUK;AACD,eAAOkD,MAAM,CAAC5C,IAAP,CAAY,GAAZ,CAAP;AACH;AACJ,KAxBD,MAyBK;AACD,aAAOY,cAAc,CAAC+B,OAAD,CAArB;AACH;AACJ,GA7BM,CAAP;AA8BH;;AC/JM,SAASO,KAAT,CAAelF,OAAf,EAAwB;AAC3B;AACA,MAAIU,MAAM,GAAGV,OAAO,CAACU,MAAR,CAAe0C,WAAf,EAAb,CAF2B;;AAI3B,MAAIzC,GAAG,GAAG,CAACX,OAAO,CAACW,GAAR,IAAe,GAAhB,EAAqBY,OAArB,CAA6B,cAA7B,EAA6C,MAA7C,CAAV;AACA,MAAIV,OAAO,GAAGrB,MAAM,CAACU,MAAP,CAAc,EAAd,EAAkBF,OAAO,CAACa,OAA1B,CAAd;AACA,MAAIsE,IAAJ;AACA,MAAI1D,UAAU,GAAGgB,IAAI,CAACzC,OAAD,EAAU,CAC3B,QAD2B,EAE3B,SAF2B,EAG3B,KAH2B,EAI3B,SAJ2B,EAK3B,SAL2B,EAM3B,WAN2B,CAAV,CAArB,CAP2B;;AAgB3B,QAAMoF,gBAAgB,GAAGhD,uBAAuB,CAACzB,GAAD,CAAhD;AACAA,EAAAA,GAAG,GAAGyD,QAAQ,CAACzD,GAAD,CAAR,CAAc2D,MAAd,CAAqB7C,UAArB,CAAN;;AACA,MAAI,CAAC,QAAQE,IAAR,CAAahB,GAAb,CAAL,EAAwB;AACpBA,IAAAA,GAAG,GAAGX,OAAO,CAACqF,OAAR,GAAkB1E,GAAxB;AACH;;AACD,QAAM2E,iBAAiB,GAAG9F,MAAM,CAACC,IAAP,CAAYO,OAAZ,EACrBkB,MADqB,CACbyB,MAAD,IAAYyC,gBAAgB,CAAChE,QAAjB,CAA0BuB,MAA1B,CADE,EAErBtB,MAFqB,CAEd,SAFc,CAA1B;AAGA,QAAMkE,mBAAmB,GAAG9C,IAAI,CAAChB,UAAD,EAAa6D,iBAAb,CAAhC;AACA,QAAME,eAAe,GAAG,6BAA6B7D,IAA7B,CAAkCd,OAAO,CAAC4E,MAA1C,CAAxB;;AACA,MAAI,CAACD,eAAL,EAAsB;AAClB,QAAIxF,OAAO,CAACe,SAAR,CAAkB2E,MAAtB,EAA8B;AAC1B;AACA7E,MAAAA,OAAO,CAAC4E,MAAR,GAAiB5E,OAAO,CAAC4E,MAAR,CACZ7E,KADY,CACN,GADM,EAEZU,GAFY,CAEPH,OAAD,IAAaA,OAAO,CAACI,OAAR,CAAgB,kDAAhB,EAAqE,uBAAsBvB,OAAO,CAACe,SAAR,CAAkB2E,MAAO,EAApH,CAFL,EAGZ1D,IAHY,CAGP,GAHO,CAAjB;AAIH;;AACD,QAAIhC,OAAO,CAACe,SAAR,CAAkBC,QAAlB,CAA2BC,MAA/B,EAAuC;AACnC,YAAM0E,wBAAwB,GAAG9E,OAAO,CAAC4E,MAAR,CAAenD,KAAf,CAAqB,qBAArB,KAA+C,EAAhF;AACAzB,MAAAA,OAAO,CAAC4E,MAAR,GAAiBE,wBAAwB,CACpCtE,MADY,CACLrB,OAAO,CAACe,SAAR,CAAkBC,QADb,EAEZM,GAFY,CAEPH,OAAD,IAAa;AAClB,cAAMuE,MAAM,GAAG1F,OAAO,CAACe,SAAR,CAAkB2E,MAAlB,GACR,IAAG1F,OAAO,CAACe,SAAR,CAAkB2E,MAAO,EADpB,GAET,OAFN;AAGA,eAAQ,0BAAyBvE,OAAQ,WAAUuE,MAAO,EAA1D;AACH,OAPgB,EAQZ1D,IARY,CAQP,GARO,CAAjB;AASH;AACJ,GA9C0B;AAgD3B;;;AACA,MAAI,CAAC,KAAD,EAAQ,MAAR,EAAgBZ,QAAhB,CAAyBV,MAAzB,CAAJ,EAAsC;AAClCC,IAAAA,GAAG,GAAGa,kBAAkB,CAACb,GAAD,EAAM4E,mBAAN,CAAxB;AACH,GAFD,MAGK;AACD,QAAI,UAAUA,mBAAd,EAAmC;AAC/BJ,MAAAA,IAAI,GAAGI,mBAAmB,CAACK,IAA3B;AACH,KAFD,MAGK;AACD,UAAIpG,MAAM,CAACC,IAAP,CAAY8F,mBAAZ,EAAiCtE,MAArC,EAA6C;AACzCkE,QAAAA,IAAI,GAAGI,mBAAP;AACH,OAFD,MAGK;AACD1E,QAAAA,OAAO,CAAC,gBAAD,CAAP,GAA4B,CAA5B;AACH;AACJ;AACJ,GAhE0B;;;AAkE3B,MAAI,CAACA,OAAO,CAAC,cAAD,CAAR,IAA4B,OAAOsE,IAAP,KAAgB,WAAhD,EAA6D;AACzDtE,IAAAA,OAAO,CAAC,cAAD,CAAP,GAA0B,iCAA1B;AACH,GApE0B;AAsE3B;;;AACA,MAAI,CAAC,OAAD,EAAU,KAAV,EAAiBO,QAAjB,CAA0BV,MAA1B,KAAqC,OAAOyE,IAAP,KAAgB,WAAzD,EAAsE;AAClEA,IAAAA,IAAI,GAAG,EAAP;AACH,GAzE0B;;;AA2E3B,SAAO3F,MAAM,CAACU,MAAP,CAAc;AAAEQ,IAAAA,MAAF;AAAUC,IAAAA,GAAV;AAAeE,IAAAA;AAAf,GAAd,EAAwC,OAAOsE,IAAP,KAAgB,WAAhB,GAA8B;AAAEA,IAAAA;AAAF,GAA9B,GAAyC,IAAjF,EAAuFnF,OAAO,CAAC6F,OAAR,GAAkB;AAAEA,IAAAA,OAAO,EAAE7F,OAAO,CAAC6F;AAAnB,GAAlB,GAAiD,IAAxI,CAAP;AACH;;AC9EM,SAASC,oBAAT,CAA8B/F,QAA9B,EAAwCU,KAAxC,EAA+CT,OAA/C,EAAwD;AAC3D,SAAOkF,KAAK,CAAC1E,KAAK,CAACT,QAAD,EAAWU,KAAX,EAAkBT,OAAlB,CAAN,CAAZ;AACH;;ACDM,SAAS+F,YAAT,CAAsBC,WAAtB,EAAmCC,WAAnC,EAAgD;AACnD,QAAMC,QAAQ,GAAG1F,KAAK,CAACwF,WAAD,EAAcC,WAAd,CAAtB;AACA,QAAME,QAAQ,GAAGL,oBAAoB,CAACvB,IAArB,CAA0B,IAA1B,EAAgC2B,QAAhC,CAAjB;AACA,SAAO1G,MAAM,CAACU,MAAP,CAAciG,QAAd,EAAwB;AAC3BD,IAAAA,QAD2B;AAE3BnG,IAAAA,QAAQ,EAAEgG,YAAY,CAACxB,IAAb,CAAkB,IAAlB,EAAwB2B,QAAxB,CAFiB;AAG3B1F,IAAAA,KAAK,EAAEA,KAAK,CAAC+D,IAAN,CAAW,IAAX,EAAiB2B,QAAjB,CAHoB;AAI3BhB,IAAAA;AAJ2B,GAAxB,CAAP;AAMH;;ACZM,MAAMkB,OAAO,GAAG,mBAAhB;;ACEP,MAAMC,SAAS,GAAI,uBAAsBD,OAAQ,IAAGE,+BAAY,EAAG,EAAnE;AAEA;;AACA,AAAO,MAAMJ,QAAQ,GAAG;AACpBxF,EAAAA,MAAM,EAAE,KADY;AAEpB2E,EAAAA,OAAO,EAAE,wBAFW;AAGpBxE,EAAAA,OAAO,EAAE;AACL4E,IAAAA,MAAM,EAAE,gCADH;AAEL,kBAAcY;AAFT,GAHW;AAOpBtF,EAAAA,SAAS,EAAE;AACP2E,IAAAA,MAAM,EAAE,EADD;AAEP1E,IAAAA,QAAQ,EAAE;AAFH;AAPS,CAAjB;;MCHMmF,QAAQ,GAAGJ,YAAY,CAAC,IAAD,EAAOG,QAAP,CAA7B;;;;"}