{"name": "@octokit/endpoint", "description": "Turns REST API endpoints into generic request options", "version": "6.0.12", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "api", "rest"], "repository": "github:octokit/endpoint.js", "dependencies": {"@octokit/types": "^6.0.3", "is-plain-object": "^5.0.0", "universal-user-agent": "^6.0.0"}, "devDependencies": {"@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/jest": "^26.0.0", "jest": "^27.0.0", "prettier": "2.3.1", "semantic-release": "^17.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^27.0.0-next.12", "typescript": "^4.0.2"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}