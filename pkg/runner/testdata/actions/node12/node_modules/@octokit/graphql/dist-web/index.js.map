{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/error.js", "../dist-src/graphql.js", "../dist-src/with-defaults.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"4.8.0\";\n", "function _buildMessageForResponseErrors(data) {\n    return (`Request failed due to following response errors:\\n` +\n        data.errors.map((e) => ` - ${e.message}`).join(\"\\n\"));\n}\nexport class GraphqlResponseError extends Error {\n    constructor(request, headers, response) {\n        super(_buildMessageForResponseErrors(response));\n        this.request = request;\n        this.headers = headers;\n        this.response = response;\n        this.name = \"GraphqlResponseError\";\n        // Expose the errors and response data in their shorthand properties.\n        this.errors = response.errors;\n        this.data = response.data;\n        // Maintains proper stack trace (only available on V8)\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        }\n    }\n}\n", "import { GraphqlResponseError } from \"./error\";\nconst NON_VARIABLE_OPTIONS = [\n    \"method\",\n    \"baseUrl\",\n    \"url\",\n    \"headers\",\n    \"request\",\n    \"query\",\n    \"mediaType\",\n];\nconst FORBIDDEN_VARIABLE_OPTIONS = [\"query\", \"method\", \"url\"];\nconst GHES_V3_SUFFIX_REGEX = /\\/api\\/v3\\/?$/;\nexport function graphql(request, query, options) {\n    if (options) {\n        if (typeof query === \"string\" && \"query\" in options) {\n            return Promise.reject(new Error(`[@octokit/graphql] \"query\" cannot be used as variable name`));\n        }\n        for (const key in options) {\n            if (!FORBIDDEN_VARIABLE_OPTIONS.includes(key))\n                continue;\n            return Promise.reject(new Error(`[@octokit/graphql] \"${key}\" cannot be used as variable name`));\n        }\n    }\n    const parsedOptions = typeof query === \"string\" ? Object.assign({ query }, options) : query;\n    const requestOptions = Object.keys(parsedOptions).reduce((result, key) => {\n        if (NON_VARIABLE_OPTIONS.includes(key)) {\n            result[key] = parsedOptions[key];\n            return result;\n        }\n        if (!result.variables) {\n            result.variables = {};\n        }\n        result.variables[key] = parsedOptions[key];\n        return result;\n    }, {});\n    // workaround for GitHub Enterprise baseUrl set with /api/v3 suffix\n    // https://github.com/octokit/auth-app.js/issues/111#issuecomment-657610451\n    const baseUrl = parsedOptions.baseUrl || request.endpoint.DEFAULTS.baseUrl;\n    if (GHES_V3_SUFFIX_REGEX.test(baseUrl)) {\n        requestOptions.url = baseUrl.replace(GHES_V3_SUFFIX_REGEX, \"/api/graphql\");\n    }\n    return request(requestOptions).then((response) => {\n        if (response.data.errors) {\n            const headers = {};\n            for (const key of Object.keys(response.headers)) {\n                headers[key] = response.headers[key];\n            }\n            throw new GraphqlResponseError(requestOptions, headers, response.data);\n        }\n        return response.data.data;\n    });\n}\n", "import { request as Request } from \"@octokit/request\";\nimport { graphql } from \"./graphql\";\nexport function withDefaults(request, newDefaults) {\n    const newRequest = request.defaults(newDefaults);\n    const newApi = (query, options) => {\n        return graphql(newRequest, query, options);\n    };\n    return Object.assign(newApi, {\n        defaults: withDefaults.bind(null, newRequest),\n        endpoint: Request.endpoint,\n    });\n}\n", "import { request } from \"@octokit/request\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nimport { withDefaults } from \"./with-defaults\";\nexport const graphql = withDefaults(request, {\n    headers: {\n        \"user-agent\": `octokit-graphql.js/${VERSION} ${getUserAgent()}`,\n    },\n    method: \"POST\",\n    url: \"/graphql\",\n});\nexport { GraphqlResponseError } from \"./error\";\nexport function withCustomRequest(customRequest) {\n    return withDefaults(customRequest, {\n        method: \"POST\",\n        url: \"/graphql\",\n    });\n}\n"], "names": ["request", "Request", "graphql"], "mappings": ";;;AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACA1C,SAAS,8BAA8B,CAAC,IAAI,EAAE;AAC9C,IAAI,QAAQ,CAAC,kDAAkD,CAAC;AAChE,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC9D,CAAC;AACD,AAAO,MAAM,oBAAoB,SAAS,KAAK,CAAC;AAChD,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC5C,QAAQ,KAAK,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxD,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;AAC3C;AACA,QAAQ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAClC;AACA;AACA,QAAQ,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACrC,YAAY,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5D,SAAS;AACT,KAAK;AACL,CAAC;;ACnBD,MAAM,oBAAoB,GAAG;AAC7B,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,WAAW;AACf,CAAC,CAAC;AACF,MAAM,0BAA0B,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9D,MAAM,oBAAoB,GAAG,eAAe,CAAC;AAC7C,AAAO,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;AACjD,IAAI,IAAI,OAAO,EAAE;AACjB,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,OAAO,EAAE;AAC7D,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,0DAA0D,CAAC,CAAC,CAAC,CAAC;AAC3G,SAAS;AACT,QAAQ,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;AACnC,YAAY,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,GAAG,CAAC;AACzD,gBAAgB,SAAS;AACzB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC;AAC5G,SAAS;AACT,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC;AAChG,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK;AAC9E,QAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAChD,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7C,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC/B,YAAY,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;AAClC,SAAS;AACT,QAAQ,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACnD,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX;AACA;AACA,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC/E,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC5C,QAAQ,cAAc,CAAC,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;AACnF,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AACtD,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE;AAClC,YAAY,MAAM,OAAO,GAAG,EAAE,CAAC;AAC/B,YAAY,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC7D,gBAAgB,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACrD,aAAa;AACb,YAAY,MAAM,IAAI,oBAAoB,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnF,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAClC,KAAK,CAAC,CAAC;AACP,CAAC;;ACjDM,SAAS,YAAY,CAACA,SAAO,EAAE,WAAW,EAAE;AACnD,IAAI,MAAM,UAAU,GAAGA,SAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACrD,IAAI,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK;AACvC,QAAQ,OAAO,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACnD,KAAK,CAAC;AACN,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;AACjC,QAAQ,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;AACrD,QAAQ,QAAQ,EAAEC,OAAO,CAAC,QAAQ;AAClC,KAAK,CAAC,CAAC;AACP,CAAC;;ACPW,MAACC,SAAO,GAAG,YAAY,CAAC,OAAO,EAAE;AAC7C,IAAI,OAAO,EAAE;AACb,QAAQ,YAAY,EAAE,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,GAAG,EAAE,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,AACO,SAAS,iBAAiB,CAAC,aAAa,EAAE;AACjD,IAAI,OAAO,YAAY,CAAC,aAAa,EAAE;AACvC,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,KAAK,CAAC,CAAC;AACP,CAAC;;;;"}