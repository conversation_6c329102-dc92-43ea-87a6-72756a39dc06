{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["import { Deprecation } from \"deprecation\";\nimport once from \"once\";\nconst logOnceCode = once((deprecation) => console.warn(deprecation));\nconst logOnceHeaders = once((deprecation) => console.warn(deprecation));\n/**\n * Error with extra properties to help with debugging\n */\nexport class RequestError extends Error {\n    constructor(message, statusCode, options) {\n        super(message);\n        // Maintains proper stack trace (only available on V8)\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        }\n        this.name = \"HttpError\";\n        this.status = statusCode;\n        let headers;\n        if (\"headers\" in options && typeof options.headers !== \"undefined\") {\n            headers = options.headers;\n        }\n        if (\"response\" in options) {\n            this.response = options.response;\n            headers = options.response.headers;\n        }\n        // redact request credentials without mutating original request options\n        const requestCopy = Object.assign({}, options.request);\n        if (options.request.headers.authorization) {\n            requestCopy.headers = Object.assign({}, options.request.headers, {\n                authorization: options.request.headers.authorization.replace(/ .*$/, \" [REDACTED]\"),\n            });\n        }\n        requestCopy.url = requestCopy.url\n            // client_id & client_secret can be passed as URL query parameters to increase rate limit\n            // see https://developer.github.com/v3/#increasing-the-unauthenticated-rate-limit-for-oauth-applications\n            .replace(/\\bclient_secret=\\w+/g, \"client_secret=[REDACTED]\")\n            // OAuth tokens can be passed as URL query parameters, although it is not recommended\n            // see https://developer.github.com/v3/#oauth2-token-sent-in-a-header\n            .replace(/\\baccess_token=\\w+/g, \"access_token=[REDACTED]\");\n        this.request = requestCopy;\n        // deprecations\n        Object.defineProperty(this, \"code\", {\n            get() {\n                logOnceCode(new Deprecation(\"[@octokit/request-error] `error.code` is deprecated, use `error.status`.\"));\n                return statusCode;\n            },\n        });\n        Object.defineProperty(this, \"headers\", {\n            get() {\n                logOnceHeaders(new Deprecation(\"[@octokit/request-error] `error.headers` is deprecated, use `error.response.headers`.\"));\n                return headers || {};\n            },\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAEA,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AACrE,MAAM,cAAc,GAAG,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AACxE;AACA;AACA;AACO,MAAM,YAAY,SAAS,KAAK,CAAC;AACxC,IAAI,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE;AAC9C,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC;AACvB;AACA;AACA,QAAQ,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACrC,YAAY,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5D,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;AAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;AACjC,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,SAAS,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;AAC5E,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACtC,SAAS;AACT,QAAQ,IAAI,UAAU,IAAI,OAAO,EAAE;AACnC,YAAY,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAC7C,YAAY,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC/C,SAAS;AACT;AACA,QAAQ,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE;AACnD,YAAY,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;AAC7E,gBAAgB,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,aAAa,CAAC;AACnG,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG;AACzC;AACA;AACA,aAAa,OAAO,CAAC,sBAAsB,EAAE,0BAA0B,CAAC;AACxE;AACA;AACA,aAAa,OAAO,CAAC,qBAAqB,EAAE,yBAAyB,CAAC,CAAC;AACvE,QAAQ,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;AACnC;AACA,QAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5C,YAAY,GAAG,GAAG;AAClB,gBAAgB,WAAW,CAAC,IAAI,WAAW,CAAC,0EAA0E,CAAC,CAAC,CAAC;AACzH,gBAAgB,OAAO,UAAU,CAAC;AAClC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;AAC/C,YAAY,GAAG,GAAG;AAClB,gBAAgB,cAAc,CAAC,IAAI,WAAW,CAAC,uFAAuF,CAAC,CAAC,CAAC;AACzI,gBAAgB,OAAO,OAAO,IAAI,EAAE,CAAC;AACrC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;;;"}