{"name": "@octokit/request-error", "description": "Error class for Octokit request errors", "version": "2.1.0", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "api", "error"], "repository": "github:octokit/request-error.js", "dependencies": {"@octokit/types": "^6.0.3", "deprecation": "^2.0.0", "once": "^1.4.0"}, "devDependencies": {"@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-bundle-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/jest": "^26.0.0", "@types/node": "^14.0.4", "@types/once": "^1.4.0", "jest": "^27.0.0", "pika-plugin-unpkg-field": "^1.1.0", "prettier": "2.3.1", "semantic-release": "^17.0.0", "ts-jest": "^27.0.0-next.12", "typescript": "^4.0.0"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}