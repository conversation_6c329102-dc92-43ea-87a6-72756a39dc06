{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["import { Deprecation } from \"deprecation\";\nimport once from \"once\";\nconst logOnceCode = once((deprecation) => console.warn(deprecation));\nconst logOnceHeaders = once((deprecation) => console.warn(deprecation));\n/**\n * Error with extra properties to help with debugging\n */\nexport class RequestError extends Error {\n    constructor(message, statusCode, options) {\n        super(message);\n        // Maintains proper stack trace (only available on V8)\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, this.constructor);\n        }\n        this.name = \"HttpError\";\n        this.status = statusCode;\n        let headers;\n        if (\"headers\" in options && typeof options.headers !== \"undefined\") {\n            headers = options.headers;\n        }\n        if (\"response\" in options) {\n            this.response = options.response;\n            headers = options.response.headers;\n        }\n        // redact request credentials without mutating original request options\n        const requestCopy = Object.assign({}, options.request);\n        if (options.request.headers.authorization) {\n            requestCopy.headers = Object.assign({}, options.request.headers, {\n                authorization: options.request.headers.authorization.replace(/ .*$/, \" [REDACTED]\"),\n            });\n        }\n        requestCopy.url = requestCopy.url\n            // client_id & client_secret can be passed as URL query parameters to increase rate limit\n            // see https://developer.github.com/v3/#increasing-the-unauthenticated-rate-limit-for-oauth-applications\n            .replace(/\\bclient_secret=\\w+/g, \"client_secret=[REDACTED]\")\n            // OAuth tokens can be passed as URL query parameters, although it is not recommended\n            // see https://developer.github.com/v3/#oauth2-token-sent-in-a-header\n            .replace(/\\baccess_token=\\w+/g, \"access_token=[REDACTED]\");\n        this.request = requestCopy;\n        // deprecations\n        Object.defineProperty(this, \"code\", {\n            get() {\n                logOnceCode(new Deprecation(\"[@octokit/request-error] `error.code` is deprecated, use `error.status`.\"));\n                return statusCode;\n            },\n        });\n        Object.defineProperty(this, \"headers\", {\n            get() {\n                logOnceHeaders(new Deprecation(\"[@octokit/request-error] `error.headers` is deprecated, use `error.response.headers`.\"));\n                return headers || {};\n            },\n        });\n    }\n}\n"], "names": ["logOnceCode", "once", "deprecation", "console", "warn", "logOnceHeaders", "RequestError", "Error", "constructor", "message", "statusCode", "options", "captureStackTrace", "name", "status", "headers", "response", "requestCopy", "Object", "assign", "request", "authorization", "replace", "url", "defineProperty", "get", "Deprecation"], "mappings": ";;;;;;;;;AAEA,MAAMA,WAAW,GAAGC,IAAI,CAAEC,WAAD,IAAiBC,OAAO,CAACC,IAAR,CAAaF,WAAb,CAAlB,CAAxB;AACA,MAAMG,cAAc,GAAGJ,IAAI,CAAEC,WAAD,IAAiBC,OAAO,CAACC,IAAR,CAAaF,WAAb,CAAlB,CAA3B;AACA;AACA;AACA;;AACO,MAAMI,YAAN,SAA2BC,KAA3B,CAAiC;AACpCC,EAAAA,WAAW,CAACC,OAAD,EAAUC,UAAV,EAAsBC,OAAtB,EAA+B;AACtC,UAAMF,OAAN,EADsC;;AAGtC;;AACA,QAAIF,KAAK,CAACK,iBAAV,EAA6B;AACzBL,MAAAA,KAAK,CAACK,iBAAN,CAAwB,IAAxB,EAA8B,KAAKJ,WAAnC;AACH;;AACD,SAAKK,IAAL,GAAY,WAAZ;AACA,SAAKC,MAAL,GAAcJ,UAAd;AACA,QAAIK,OAAJ;;AACA,QAAI,aAAaJ,OAAb,IAAwB,OAAOA,OAAO,CAACI,OAAf,KAA2B,WAAvD,EAAoE;AAChEA,MAAAA,OAAO,GAAGJ,OAAO,CAACI,OAAlB;AACH;;AACD,QAAI,cAAcJ,OAAlB,EAA2B;AACvB,WAAKK,QAAL,GAAgBL,OAAO,CAACK,QAAxB;AACAD,MAAAA,OAAO,GAAGJ,OAAO,CAACK,QAAR,CAAiBD,OAA3B;AACH,KAhBqC;;;AAkBtC,UAAME,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBR,OAAO,CAACS,OAA1B,CAApB;;AACA,QAAIT,OAAO,CAACS,OAAR,CAAgBL,OAAhB,CAAwBM,aAA5B,EAA2C;AACvCJ,MAAAA,WAAW,CAACF,OAAZ,GAAsBG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBR,OAAO,CAACS,OAAR,CAAgBL,OAAlC,EAA2C;AAC7DM,QAAAA,aAAa,EAAEV,OAAO,CAACS,OAAR,CAAgBL,OAAhB,CAAwBM,aAAxB,CAAsCC,OAAtC,CAA8C,MAA9C,EAAsD,aAAtD;AAD8C,OAA3C,CAAtB;AAGH;;AACDL,IAAAA,WAAW,CAACM,GAAZ,GAAkBN,WAAW,CAACM,GAAZ;AAEd;AAFc,KAGbD,OAHa,CAGL,sBAHK,EAGmB,0BAHnB;AAKd;AALc,KAMbA,OANa,CAML,qBANK,EAMkB,yBANlB,CAAlB;AAOA,SAAKF,OAAL,GAAeH,WAAf,CA/BsC;;AAiCtCC,IAAAA,MAAM,CAACM,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;AAChCC,MAAAA,GAAG,GAAG;AACFzB,QAAAA,WAAW,CAAC,IAAI0B,uBAAJ,CAAgB,0EAAhB,CAAD,CAAX;AACA,eAAOhB,UAAP;AACH;;AAJ+B,KAApC;AAMAQ,IAAAA,MAAM,CAACM,cAAP,CAAsB,IAAtB,EAA4B,SAA5B,EAAuC;AACnCC,MAAAA,GAAG,GAAG;AACFpB,QAAAA,cAAc,CAAC,IAAIqB,uBAAJ,CAAgB,uFAAhB,CAAD,CAAd;AACA,eAAOX,OAAO,IAAI,EAAlB;AACH;;AAJkC,KAAvC;AAMH;;AA9CmC;;;;"}