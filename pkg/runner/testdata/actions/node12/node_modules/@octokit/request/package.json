{"name": "@octokit/request", "description": "Send parameterized requests to GitHub's APIs with sensible defaults in browsers and Node", "version": "5.6.3", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["octokit", "github", "api", "request"], "repository": "github:octokit/request.js", "dependencies": {"@octokit/endpoint": "^6.0.1", "@octokit/request-error": "^2.1.0", "@octokit/types": "^6.16.1", "is-plain-object": "^5.0.0", "node-fetch": "^2.6.7", "universal-user-agent": "^6.0.0"}, "devDependencies": {"@octokit/auth-app": "^3.0.0", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.0", "@pika/plugin-build-web": "^0.9.0", "@pika/plugin-ts-standard-pkg": "^0.9.0", "@types/fetch-mock": "^7.2.4", "@types/jest": "^27.0.0", "@types/lolex": "^5.1.0", "@types/node": "^14.0.0", "@types/node-fetch": "^2.3.3", "@types/once": "^1.4.0", "fetch-mock": "^9.3.1", "jest": "^27.0.0", "lolex": "^6.0.0", "prettier": "2.4.1", "semantic-release": "^18.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "string-to-arraybuffer": "^1.0.2", "ts-jest": "^27.0.0", "typescript": "^4.0.2"}, "publishConfig": {"access": "public"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}