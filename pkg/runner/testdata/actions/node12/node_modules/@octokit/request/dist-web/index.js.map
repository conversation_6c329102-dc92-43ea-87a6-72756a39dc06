{"version": 3, "file": "index.js", "sources": ["../dist-src/version.js", "../dist-src/get-buffer-response.js", "../dist-src/fetch-wrapper.js", "../dist-src/with-defaults.js", "../dist-src/index.js"], "sourcesContent": ["export const VERSION = \"5.6.3\";\n", "export default function getBufferResponse(response) {\n    return response.arrayBuffer();\n}\n", "import { isPlainObject } from \"is-plain-object\";\nimport nodeFetch from \"node-fetch\";\nimport { RequestError } from \"@octokit/request-error\";\nimport getBuffer from \"./get-buffer-response\";\nexport default function fetchWrapper(requestOptions) {\n    const log = requestOptions.request && requestOptions.request.log\n        ? requestOptions.request.log\n        : console;\n    if (isPlainObject(requestOptions.body) ||\n        Array.isArray(requestOptions.body)) {\n        requestOptions.body = JSON.stringify(requestOptions.body);\n    }\n    let headers = {};\n    let status;\n    let url;\n    const fetch = (requestOptions.request && requestOptions.request.fetch) || nodeFetch;\n    return fetch(requestOptions.url, Object.assign({\n        method: requestOptions.method,\n        body: requestOptions.body,\n        headers: requestOptions.headers,\n        redirect: requestOptions.redirect,\n    }, \n    // `requestOptions.request.agent` type is incompatible\n    // see https://github.com/octokit/types.ts/pull/264\n    requestOptions.request))\n        .then(async (response) => {\n        url = response.url;\n        status = response.status;\n        for (const keyAndValue of response.headers) {\n            headers[keyAndValue[0]] = keyAndValue[1];\n        }\n        if (\"deprecation\" in headers) {\n            const matches = headers.link && headers.link.match(/<([^>]+)>; rel=\"deprecation\"/);\n            const deprecationLink = matches && matches.pop();\n            log.warn(`[@octokit/request] \"${requestOptions.method} ${requestOptions.url}\" is deprecated. It is scheduled to be removed on ${headers.sunset}${deprecationLink ? `. See ${deprecationLink}` : \"\"}`);\n        }\n        if (status === 204 || status === 205) {\n            return;\n        }\n        // GitHub API returns 200 for HEAD requests\n        if (requestOptions.method === \"HEAD\") {\n            if (status < 400) {\n                return;\n            }\n            throw new RequestError(response.statusText, status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data: undefined,\n                },\n                request: requestOptions,\n            });\n        }\n        if (status === 304) {\n            throw new RequestError(\"Not modified\", status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data: await getResponseData(response),\n                },\n                request: requestOptions,\n            });\n        }\n        if (status >= 400) {\n            const data = await getResponseData(response);\n            const error = new RequestError(toErrorMessage(data), status, {\n                response: {\n                    url,\n                    status,\n                    headers,\n                    data,\n                },\n                request: requestOptions,\n            });\n            throw error;\n        }\n        return getResponseData(response);\n    })\n        .then((data) => {\n        return {\n            status,\n            url,\n            headers,\n            data,\n        };\n    })\n        .catch((error) => {\n        if (error instanceof RequestError)\n            throw error;\n        throw new RequestError(error.message, 500, {\n            request: requestOptions,\n        });\n    });\n}\nasync function getResponseData(response) {\n    const contentType = response.headers.get(\"content-type\");\n    if (/application\\/json/.test(contentType)) {\n        return response.json();\n    }\n    if (!contentType || /^text\\/|charset=utf-8$/.test(contentType)) {\n        return response.text();\n    }\n    return getBuffer(response);\n}\nfunction toErrorMessage(data) {\n    if (typeof data === \"string\")\n        return data;\n    // istanbul ignore else - just in case\n    if (\"message\" in data) {\n        if (Array.isArray(data.errors)) {\n            return `${data.message}: ${data.errors.map(JSON.stringify).join(\", \")}`;\n        }\n        return data.message;\n    }\n    // istanbul ignore next - just in case\n    return `Unknown error: ${JSON.stringify(data)}`;\n}\n", "import fetchWrapper from \"./fetch-wrapper\";\nexport default function withDefaults(oldEndpoint, newDefaults) {\n    const endpoint = oldEndpoint.defaults(newDefaults);\n    const newApi = function (route, parameters) {\n        const endpointOptions = endpoint.merge(route, parameters);\n        if (!endpointOptions.request || !endpointOptions.request.hook) {\n            return fetchWrapper(endpoint.parse(endpointOptions));\n        }\n        const request = (route, parameters) => {\n            return fetchWrapper(endpoint.parse(endpoint.merge(route, parameters)));\n        };\n        Object.assign(request, {\n            endpoint,\n            defaults: withDefaults.bind(null, endpoint),\n        });\n        return endpointOptions.request.hook(request, endpointOptions);\n    };\n    return Object.assign(newApi, {\n        endpoint,\n        defaults: withDefaults.bind(null, endpoint),\n    });\n}\n", "import { endpoint } from \"@octokit/endpoint\";\nimport { getUserAgent } from \"universal-user-agent\";\nimport { VERSION } from \"./version\";\nimport withDefaults from \"./with-defaults\";\nexport const request = withDefaults(endpoint, {\n    headers: {\n        \"user-agent\": `octokit-request.js/${VERSION} ${getUserAgent()}`,\n    },\n});\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AAAO,MAAM,OAAO,GAAG,mBAAmB;;ACA3B,SAAS,iBAAiB,CAAC,QAAQ,EAAE;AACpD,IAAI,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;;ACEc,SAAS,YAAY,CAAC,cAAc,EAAE;AACrD,IAAI,MAAM,GAAG,GAAG,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG;AACpE,UAAU,cAAc,CAAC,OAAO,CAAC,GAAG;AACpC,UAAU,OAAO,CAAC;AAClB,IAAI,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC;AAC1C,QAAQ,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;AAC5C,QAAQ,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClE,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,MAAM,KAAK,GAAG,CAAC,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;AACxF,IAAI,OAAO,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC;AACnD,QAAQ,MAAM,EAAE,cAAc,CAAC,MAAM;AACrC,QAAQ,IAAI,EAAE,cAAc,CAAC,IAAI;AACjC,QAAQ,OAAO,EAAE,cAAc,CAAC,OAAO;AACvC,QAAQ,QAAQ,EAAE,cAAc,CAAC,QAAQ;AACzC,KAAK;AACL;AACA;AACA,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;AAC5B,SAAS,IAAI,CAAC,OAAO,QAAQ,KAAK;AAClC,QAAQ,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;AAC3B,QAAQ,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,QAAQ,KAAK,MAAM,WAAW,IAAI,QAAQ,CAAC,OAAO,EAAE;AACpD,YAAY,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,aAAa,IAAI,OAAO,EAAE;AACtC,YAAY,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAC/F,YAAY,MAAM,eAAe,GAAG,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;AAC7D,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,CAAC,kDAAkD,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,eAAe,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAClN,SAAS;AACT,QAAQ,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE;AAC9C,YAAY,OAAO;AACnB,SAAS;AACT;AACA,QAAQ,IAAI,cAAc,CAAC,MAAM,KAAK,MAAM,EAAE;AAC9C,YAAY,IAAI,MAAM,GAAG,GAAG,EAAE;AAC9B,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,MAAM,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE;AAChE,gBAAgB,QAAQ,EAAE;AAC1B,oBAAoB,GAAG;AACvB,oBAAoB,MAAM;AAC1B,oBAAoB,OAAO;AAC3B,oBAAoB,IAAI,EAAE,SAAS;AACnC,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,cAAc;AACvC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,MAAM,KAAK,GAAG,EAAE;AAC5B,YAAY,MAAM,IAAI,YAAY,CAAC,cAAc,EAAE,MAAM,EAAE;AAC3D,gBAAgB,QAAQ,EAAE;AAC1B,oBAAoB,GAAG;AACvB,oBAAoB,MAAM;AAC1B,oBAAoB,OAAO;AAC3B,oBAAoB,IAAI,EAAE,MAAM,eAAe,CAAC,QAAQ,CAAC;AACzD,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,cAAc;AACvC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,IAAI,MAAM,IAAI,GAAG,EAAE;AAC3B,YAAY,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;AACzD,YAAY,MAAM,KAAK,GAAG,IAAI,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE;AACzE,gBAAgB,QAAQ,EAAE;AAC1B,oBAAoB,GAAG;AACvB,oBAAoB,MAAM;AAC1B,oBAAoB,OAAO;AAC3B,oBAAoB,IAAI;AACxB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,cAAc;AACvC,aAAa,CAAC,CAAC;AACf,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS;AACT,QAAQ,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;AACzC,KAAK,CAAC;AACN,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK;AACxB,QAAQ,OAAO;AACf,YAAY,MAAM;AAClB,YAAY,GAAG;AACf,YAAY,OAAO;AACnB,YAAY,IAAI;AAChB,SAAS,CAAC;AACV,KAAK,CAAC;AACN,SAAS,KAAK,CAAC,CAAC,KAAK,KAAK;AAC1B,QAAQ,IAAI,KAAK,YAAY,YAAY;AACzC,YAAY,MAAM,KAAK,CAAC;AACxB,QAAQ,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;AACnD,YAAY,OAAO,EAAE,cAAc;AACnC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACD,eAAe,eAAe,CAAC,QAAQ,EAAE;AACzC,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAI,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC/C,QAAQ,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,IAAI,wBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AACpE,QAAQ,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,OAAOA,iBAAS,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;AAChC,QAAQ,OAAO,IAAI,CAAC;AACpB;AACA,IAAI,IAAI,SAAS,IAAI,IAAI,EAAE;AAC3B,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACxC,YAAY,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpF,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC;;ACrHc,SAAS,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE;AAC/D,IAAI,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACvD,IAAI,MAAM,MAAM,GAAG,UAAU,KAAK,EAAE,UAAU,EAAE;AAChD,QAAQ,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AAClE,QAAQ,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE;AACvE,YAAY,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;AACjE,SAAS;AACT,QAAQ,MAAM,OAAO,GAAG,CAAC,KAAK,EAAE,UAAU,KAAK;AAC/C,YAAY,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACnF,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;AAC/B,YAAY,QAAQ;AACpB,YAAY,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AACvD,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;AACtE,KAAK,CAAC;AACN,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;AACjC,QAAQ,QAAQ;AAChB,QAAQ,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;AACnD,KAAK,CAAC,CAAC;AACP,CAAC;;ACjBW,MAAC,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE;AAC9C,IAAI,OAAO,EAAE;AACb,QAAQ,YAAY,EAAE,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC;AACvE,KAAK;AACL,CAAC,CAAC;;;;"}