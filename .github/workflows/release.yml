name: release
on:
  push:
    tags:
      - v*

permissions:
  contents: write
  actions: write

jobs:
  release:
    name: release
    runs-on: ubuntu-latest
    environment: release
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: <PERSON><PERSON><PERSON><PERSON><PERSON>
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: <PERSON><PERSON>
        uses: vedantmgoyal2009/winget-releaser@v2
        with:
          identifier: nektos.act
          installers-regex: '_Windows_\w+\.zip$'
          token: ${{ secrets.WINGET_TOKEN }}
      - name: <PERSON><PERSON>
        uses: ./.github/actions/choco
        with:
          version: ${{ github.ref }}
          apiKey: ${{ secrets.CHOCO_APIKEY }}
          push: true
      - name: GitHub CLI extension
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_ACT_TOKEN }}
          script: |
            const mainRef = (await github.rest.git.getRef({
              owner: 'nektos',
              repo: 'gh-act',
              ref: 'heads/main',
            })).data;
            console.log(mainRef);
            github.rest.git.createRef({
              owner: 'nektos',
              repo: 'gh-act',
              ref: context.ref,
              sha: mainRef.object.sha,
            });
