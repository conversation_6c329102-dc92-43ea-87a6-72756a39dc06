name: checks
on: [pull_request, workflow_dispatch]

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

env:
  ACT_OWNER: ${{ github.repository_owner }}
  ACT_REPOSITORY: ${{ github.repository }}
  CGO_ENABLED: 0

jobs:
  lint:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - uses: golangci/golangci-lint-action@v6.5.0
        with:
          version: v1.64.8
      - uses: megalinter/megalinter/flavors/go@v8.4.2
        env:
          DEFAULT_BRANCH: master
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_ALL_CODEBASE: false
          GITHUB_STATUS_REPORTER: ${{ !env.ACT }}
          GITHUB_COMMENT_REPORTER: ${{ !env.ACT }}

  test-linux:
    name: test-linux
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: Run Tests
        run: go run gotest.tools/gotestsum@latest --junitfile unit-tests.xml --format pkgname -- -v -cover -coverpkg=./... -coverprofile=coverage.txt -covermode=atomic -timeout 20m ./...
      - name: Test Summary
        uses: test-summary/action@v2
        with:
          paths: "unit-tests.xml"
        if: always()
      - name: Run act from cli
        run: go run main.go -P ubuntu-latest=node:16-buster-slim -C ./pkg/runner/testdata/ -W ./basic/push.yml
      - name: Run act from cli without docker support
        run: go run -tags WITHOUT_DOCKER main.go -P ubuntu-latest=-self-hosted -C ./pkg/runner/testdata/ -W ./local-action-js/push.yml
      - name: Upload Codecov report
        uses: codecov/codecov-action@v5
        with:
          files: coverage.txt
          fail_ci_if_error: true # optional (default = false)
          token: ${{ secrets.CODECOV_TOKEN }}

  test-host:
    strategy:
      matrix:
        os:
          - windows-latest
          - macos-latest
    name: test-host-${{matrix.os}}
    runs-on: ${{matrix.os}}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: Run Tests
        run: go run gotest.tools/gotestsum@latest --junitfile unit-tests.xml --format pkgname -- -v -cover -coverpkg=./... -coverprofile=coverage.txt -covermode=atomic -timeout 20m -run ^TestRunEventHostEnvironment$ ./...
        shell: bash
      - name: Test Summary
        uses: test-summary/action@v2
        with:
          paths: "unit-tests.xml"
        if: always()


  snapshot:
    name: snapshot
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --snapshot --clean
      - name: Capture x86_64 (64-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-amd64
          path: dist/act_linux_amd64_v1/act
      - name: Capture i386 (32-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-i386
          path: dist/act_linux_386/act
      - name: Capture arm64 (64-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-arm64
          path: dist/act_linux_arm64/act
      - name: Capture armv6 (32-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-armv6
          path: dist/act_linux_arm_6/act
      - name: Capture armv7 (32-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-armv7
          path: dist/act_linux_arm_7/act
      - name: Capture riscv64 (64-bit) Linux binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-linux-riscv64
          path: dist/act_linux_riscv64/act
      - name: Capture x86_64 (64-bit) Windows binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-windows-amd64
          path: dist/act_windows_amd64_v1/act.exe
      - name: Capture i386 (32-bit) Windows binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-windows-i386
          path: dist/act_windows_386/act.exe
      - name: Capture arm64 (64-bit) Windows binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-windows-arm64
          path: dist/act_windows_arm64/act.exe
      - name: Capture armv7 (32-bit) Windows binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-windows-armv7
          path: dist/act_windows_arm_7/act.exe
      - name: Capture x86_64 (64-bit) MacOS binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-macos-amd64
          path: dist/act_darwin_amd64_v1/act
      - name: Capture arm64 (64-bit) MacOS binary
        if: ${{ !env.ACT }}
        uses: actions/upload-artifact@v4
        with:
          name: act-macos-arm64
          path: dist/act_darwin_arm64/act
      - name: Chocolatey
        uses: ./.github/actions/choco
        with:
          version: v0.0.0-pr
