name: 'Close stale issues'
on:
  workflow_dispatch:

jobs:
  stale:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'Issue is stale and will be closed in 14 days unless there is new activity'
          stale-pr-message: 'PR is stale and will be closed in 14 days unless there is new activity'
          stale-issue-label: 'stale'
          exempt-issue-labels: 'stale-exempt,kind/feature-request'
          stale-pr-label: 'stale'
          exempt-pr-labels: 'stale-exempt'
          remove-stale-when-updated: 'True'
          operations-per-run: 500
          days-before-stale: 180
          days-before-close: 14
